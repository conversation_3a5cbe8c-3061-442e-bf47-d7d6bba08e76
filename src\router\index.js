import { createRouter, createWebHashHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'
import AccountManagement from '../views/AccountManagement.vue'
import MaterialManagement from '../views/MaterialManagement.vue'
import PublishCenter from '../views/PublishCenter.vue'
import About from '../views/About.vue'
import FetchDebugger from '../components/FetchDebugger.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      requiresAuth: false,
      title: '登录'
    }
  },
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      requiresAuth: true,
      title: '仪表盘'
    }
  },
  {
    path: '/account-management',
    name: 'AccountManagement',
    component: AccountManagement,
    meta: {
      requiresAuth: true,
      title: '账号管理'
    }
  },
  {
    path: '/material-management',
    name: 'MaterialManagement',
    component: MaterialManagement,
    meta: {
      requiresAuth: true,
      title: '素材管理'
    }
  },
  {
    path: '/publish-center',
    name: 'PublishCenter',
    component: PublishCenter,
    meta: {
      requiresAuth: true,
      title: '发布中心'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      requiresAuth: true,
      title: '关于'
    }
  },
  {
    path: '/debug-fetch',
    name: 'FetchDebugger',
    component: FetchDebugger,
    meta: {
      requiresAuth: false,
      title: 'Fetch 调试'
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 视频发布管理系统`
  }

  // 如果路由需要认证
  if (to.meta.requiresAuth) {
    if (userStore.isLoggedIn) {
      next()
    } else {
      // 尝试从localStorage恢复用户信息
      const restored = userStore.restoreUserInfo()
      if (restored) {
        next()
      } else {
        next('/login')
      }
    }
  } else {
    // 如果已登录用户访问登录页，重定向到首页
    if (to.name === 'Login' && userStore.isLoggedIn) {
      next('/')
    } else {
      next()
    }
  }
})

export default router