#![cfg_attr(
    all(not(debug_assertions), target_os = "windows"),
    windows_subsystem = "windows"
)]

use std::process::{Child, Command};
use std::sync::{Arc, Mutex};
use std::path::PathBuf;
use tauri::utils::platform::current_exe;
mod commands;

// Import CommandExt for Windows-specific process creation flags
#[cfg(target_os = "windows")]
use std::os::windows::process::CommandExt;

#[tauri::command]
fn close_main_window(app: tauri::AppHandle, _webview_window: tauri::WebviewWindow) -> () {
    app.exit(0); // 关闭应用程序
}


fn main() {
    // 获取当前可执行文件目录并拼接 main.exe 路径
    let exe_path: PathBuf = current_exe()
        .unwrap_or_default()
        .parent()
        .unwrap_or_else(|| std::path::Path::new(""))
        .join("xiaochao-server.exe");

    // 用 Arc<Mutex<Option<Child>>> 管理子进程
    let main_exe: Arc<Mutex<Option<Child>>> = Arc::new(Mutex::new(
        Command::new(exe_path)
            .creation_flags(0x08000000) // 后台无窗口运行，仅限 Windows
            .spawn()
            .ok(),
    ));

    tauri::Builder::default()
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .invoke_handler(tauri::generate_handler![
            close_main_window,
            commands::machine::get_machine_info,
            commands::utils::sig_py,
            commands::utils::token_py,
            commands::utils::extract_signature,
            commands::utils::get_sig3,
            commands::utils::check_java_available,
            commands::utils::get_jar_path,
            commands::utils::batch_get_sig3,
            commands::utils::validate_signature,
        ])
        .setup({
            let _main_exe = main_exe.clone();
            move |_app| {
                // 这里可以做更多初始化
                Ok(())
            }
        })
        .on_window_event({
            let main_exe = main_exe.clone();
            move |_window, event| {
                if let tauri::WindowEvent::CloseRequested { .. } = event {
                    if let Ok(mut child_opt) = main_exe.lock() {
                        if let Some(child) = child_opt.as_mut() {
                            let _ = child.kill();
                        }
                    }
                }
            }
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}