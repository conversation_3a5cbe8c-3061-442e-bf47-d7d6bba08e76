"""
数据库迁移脚本：添加 auto_publish 列到 user_info 表
"""
import sqlite3
import os
from pathlib import Path

def add_auto_publish_column():
    # 数据库文件路径
    BASE_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
    db_path = BASE_DIR / "database.db"
    print(f"使用的数据库路径: {db_path}")
    
    # 检查数据库文件是否存在
    if not db_path.exists():
        print("错误：数据库文件不存在")
        return False
    
    try:
        # 连接到SQLite数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查 auto_publish 列是否已存在
        cursor.execute("PRAGMA table_info(user_info)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'auto_publish' in columns:
            print("auto_publish 列已存在，无需添加")
            return True
        
        # 添加 auto_publish 列
        cursor.execute('''
            ALTER TABLE user_info 
            ADD COLUMN auto_publish INTEGER DEFAULT 1
        ''')
        
        # 提交更改
        conn.commit()
        print("成功添加 auto_publish 列到 user_info 表")
        
        # 验证列是否添加成功
        cursor.execute("PRAGMA table_info(user_info)")
        columns_after = [column[1] for column in cursor.fetchall()]
        print(f"更新后的表结构: {columns_after}")
        
        # 关闭连接
        conn.close()
        return True
        
    except Exception as e:
        print(f"添加列失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = add_auto_publish_column()
    if success:
        print("数据库迁移完成")
    else:
        print("数据库迁移失败")
