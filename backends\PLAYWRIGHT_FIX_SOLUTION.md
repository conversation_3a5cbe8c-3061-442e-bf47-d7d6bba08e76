# Playwright Chrome 路径问题解决方案

## 问题描述

使用 Nuitka 打包后，Playwright 在 `headless=True` 模式下尝试使用不存在的路径：
```
chromium_headless_shell-1169\chrome-win\headless_shell.exe
```

但实际存在的路径是：
```
chromium-1169\chrome-win\chrome.exe
```

## 根本原因

1. **Playwright 行为**：当 `headless=True` 时，Playwright 默认尝试使用 headless shell 版本
2. **打包问题**：Nuitka 打包后，只包含了标准的 Chrome 可执行文件，没有 headless shell 版本
3. **路径问题**：打包环境中的浏览器路径与开发环境不同

## 解决方案

### 1. 修改 `conf.py` 配置

已更新 `conf.py` 文件，在打包环境中自动检测并配置正确的 Chrome 路径：

```python
if is_frozen():
    # 打包环境：main.exe 所在目录
    base_dir = os.path.dirname(sys.executable)
    playwright_browsers_dir = os.path.join(base_dir, "playwright", "driver", "package", ".local-browsers")
    
    # 尝试多个可能的 Chrome 路径，优先使用较新版本
    possible_chrome_paths = [
        os.path.join(playwright_browsers_dir, "chromium-1169", "chrome-win", "chrome.exe"),
        os.path.join(playwright_browsers_dir, "chromium-1091", "chrome-win", "chrome.exe"),
        # ... 其他备选路径
    ]
    
    playwright_path = None
    for chrome_path in possible_chrome_paths:
        if os.path.exists(chrome_path):
            playwright_path = chrome_path
            break
```

### 2. 修改所有 Playwright 启动调用

已更新 `myUtils/login.py` 中的所有函数，确保使用配置的 Chrome 路径：

```python
from conf import LOCAL_CHROME_PATH

async def get_ks_shop_cookie(id, status_queue):
    async with async_playwright() as playwright:
        options = {
            'args': ['--lang en-GB'],
            'headless': True,
        }
        
        # 如果配置了本地 Chrome 路径，则使用它
        if LOCAL_CHROME_PATH:
            options['executable_path'] = LOCAL_CHROME_PATH
            
        browser = await playwright.chromium.launch(**options)
        # ... 其余代码
```

### 3. 验证修复

运行测试脚本验证修复效果：

```bash
cd backends
python test_chrome_path.py
python test_playwright_fix.py
```

## 测试结果

 **开发环境**：Playwright 正常工作
 **打包环境检测**：成功找到 Chrome 路径
 **路径配置**：推荐使用 `chromium-1169\chrome-win\chrome.exe`

## 修改的文件

1. `backends/conf.py` - 更新了 Chrome 路径检测逻辑
2. `backends/myUtils/login.py` - 更新了所有 Playwright 启动调用
3. 新增测试文件：
   - `backends/test_chrome_path.py` - Chrome 路径测试
   - `backends/test_playwright_fix.py` - Playwright 启动测试

## 部署建议

1. **重新打包**：使用更新后的代码重新进行 Nuitka 打包
2. **测试验证**：在打包后的环境中测试快手小店登录功能
3. **监控日志**：观察是否还有 Chrome 路径相关的错误

## 预期效果

修复后，`get_ks_shop_cookie` 函数应该能够：
1. 正确启动 Chrome 浏览器（headless 模式）
2. 成功访问快手小店页面
3. 正常获取二维码并完成登录流程

不再出现 `Executable doesn't exist at ...headless_shell.exe` 错误。
