#!/usr/bin/env python3
"""
简化版的 main.py - 用于测试 Nuitka 打包
移除 KS-Downloader 依赖，专注于基本功能
"""

import asyncio
import os
import sys
import logging
import sqlite3
import traceback
import uuid
import shutil
import subprocess
import pathlib
import time
import re
from pathlib import Path

# Flask 相关导入
from flask import Flask, request, jsonify, Response, render_template, send_from_directory
from flask_cors import CORS
import requests
import urllib3

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 禁用 urllib3 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 获取基础目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
print(f"BASE_DIR: {BASE_DIR}")

# 导入配置
try:
    import conf
    print("✓ conf 模块导入成功")
except ImportError as e:
    print(f"❌ conf 模块导入失败: {e}")

try:
    import ffmpeg_config
    print("✓ ffmpeg_config 模块导入成功")
except ImportError as e:
    print(f"❌ ffmpeg_config 模块导入失败: {e}")

# 创建 Flask 应用
app = Flask(__name__)
CORS(app)

# 日志缓存
log_cache = []
log_lock = None

@app.route('/')
def index():
    """主页"""
    return jsonify({
        "code": 200,
        "message": "小超媒体管理系统 - 简化版",
        "data": {
            "version": "1.0.0-simple",
            "status": "running"
        }
    })

@app.route('/test', methods=['GET'])
def test():
    """测试接口"""
    return jsonify({
        "code": 200,
        "message": "测试成功",
        "data": {
            "timestamp": time.time(),
            "base_dir": BASE_DIR,
            "python_version": sys.version
        }
    })

@app.route('/getFiles', methods=['GET'])
def get_all_files():
    """获取所有文件记录"""
    try:
        # 导入 services 模块
        from services import get_all_files_handler
        
        result = get_all_files_handler(BASE_DIR)
        
        return jsonify({
            "code": result['code'],
            "data": result['data'],
            "msg": result['msg']
        }), result['code']
        
    except Exception as e:
        logging.error(f"获取文件列表失败: {e}")
        traceback.print_exc()
        return jsonify({
            "code": 500,
            "data": None,
            "msg": f"获取文件列表失败: {str(e)}"
        }), 500

@app.route('/getAllAccounts', methods=['GET'])
def get_all_accounts():
    """获取所有账号信息"""
    try:
        from services import get_all_accounts_handler
        
        result = get_all_accounts_handler(BASE_DIR)
        
        if result['success']:
            return jsonify({
                "code": result['code'],
                "data": result['data'],
                "msg": result['msg']
            }), result['code']
        else:
            return jsonify({
                "code": result['code'],
                "data": None,
                "msg": result['msg']
            }), result['code']
            
    except Exception as e:
        logging.error(f"获取账号列表失败: {e}")
        return jsonify({
            "code": 500,
            "data": None,
            "msg": f"获取账号列表失败: {str(e)}"
        }), 500

@app.route('/stats', methods=['GET'])
def get_stats():
    """获取统计信息"""
    try:
        from services import get_stats_handler
        
        result = get_stats_handler(BASE_DIR)
        
        if result['code'] == 200:
            return jsonify(result['data'])
        else:
            return jsonify({"error": result['msg']}), result['code']
            
    except Exception as e:
        logging.error(f"获取统计信息失败: {e}")
        return jsonify({"error": f"获取统计信息失败: {str(e)}"}), 500

@app.route('/deleteFile', methods=['GET'])
def delete_file():
    """删除单个文件"""
    try:
        from services import delete_file_handler
        
        file_id = request.args.get('id')
        
        result = delete_file_handler(BASE_DIR, file_id)
        
        return jsonify({
            "code": result['code'],
            "data": result['data'],
            "msg": result['msg']
        }), result['code']
        
    except Exception as e:
        logging.error(f"删除文件失败: {e}")
        return jsonify({
            "code": 500,
            "data": None,
            "msg": f"删除文件失败: {str(e)}"
        }), 500

@app.route('/api/logs/test', methods=['GET'])
def test_logs():
    """测试日志功能"""
    try:
        from services import process_test_logs_request, create_log_service
        
        # 创建日志服务实例
        log_service = create_log_service(log_cache, log_lock)
        
        # 调用服务处理请求
        return process_test_logs_request(log_service)
        
    except Exception as e:
        logging.error(f"测试日志失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"测试日志失败: {str(e)}"
        }), 500

@app.route('/api/logs', methods=['GET'])
def get_logs():
    """获取日志"""
    try:
        from services import process_get_logs_request, create_log_service
        
        # 创建日志服务实例
        log_service = create_log_service(log_cache, log_lock)
        
        # 调用服务处理请求
        return process_get_logs_request(log_service)
        
    except Exception as e:
        logging.error(f"获取日志失败: {e}")
        return jsonify({
            "code": 500,
            "message": f"获取日志失败: {str(e)}"
        }), 500

@app.route('/getCwd', methods=['GET'])
def get_cwd():
    """返回当前工作目录的绝对路径"""
    cwd = os.path.abspath(os.getcwd())
    return jsonify({
        "code": 200,
        "data": cwd,
        "msg": "获取当前工作目录成功"
    }), 200

def test_database():
    """测试数据库连接"""
    try:
        db_path = os.path.join(BASE_DIR, "database.db")
        if os.path.exists(db_path):
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            conn.close()
            print(f"✓ 数据库连接成功，表: {[table[0] for table in tables]}")
            return True
        else:
            print("❌ 数据库文件不存在")
            return False
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_services():
    """测试 services 模块"""
    try:
        import services
        print("✓ services 模块导入成功")
        
        # 测试基本函数
        if hasattr(services, 'get_all_files_handler'):
            print("✓ get_all_files_handler 函数存在")
        
        return True
    except Exception as e:
        print(f"❌ services 模块测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("小超媒体管理系统 - 简化版启动")
    print("=" * 50)
    
    # 运行测试
    print("运行启动测试...")
    test_database()
    test_services()
    
    print("\n启动 Flask 服务器...")
    print(f"访问地址: http://localhost:5000")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
