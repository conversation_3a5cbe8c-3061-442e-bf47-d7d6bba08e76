import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { refreshTokenApi } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref({
    access_token: '',
    refresh_token: '',
    access_token_expires_at: null,
    user_info: {}
  })

  const refreshTimer = ref(null)
  const expireCheckTimer = ref(null)

  // 刷新配置
  const REFRESH_CONFIG = {
    REGULAR_INTERVAL: 5 * 60 * 1000, // 定时刷新间隔：30分钟
    EXPIRE_CHECK_INTERVAL: 60 * 1000,  // 过期检查间隔：1分钟
    EXPIRE_THRESHOLD: 5 * 60 * 1000    // 过期阈值：提前5分钟
  }

  // 计算属性：是否已登录
  const isLoggedIn = computed(() => {
    return !!userInfo.value.access_token
  })

  // 计算属性：token是否即将过期（提前5分钟刷新）
  const isTokenExpiringSoon = computed(() => {
    if (!userInfo.value.access_token_expires_at) return false
    const expiresAt = new Date(userInfo.value.access_token_expires_at).getTime()
    const now = Date.now()
    const fiveMinutes = 5 * 60 * 1000
    return (expiresAt - now) <= fiveMinutes
  })

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = {
      access_token: info.access_token || '',
      refresh_token: info.refresh_token || '',
      access_token_expires_at: info.access_token_expires_at || null,
      user_info: info.user_info || {}
    }

    // 保存到localStorage
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value))

    // 启动token刷新定时器
    startTokenRefreshTimer()
  }

  // 从localStorage恢复用户信息
  const restoreUserInfo = () => {
    try {
      const stored = localStorage.getItem('userInfo')
      if (stored) {
        const parsedInfo = JSON.parse(stored)
        userInfo.value = parsedInfo

        // 检查token是否过期
        if (userInfo.value.access_token_expires_at) {
          const expiresAt = new Date(userInfo.value.access_token_expires_at).getTime()
          const now = Date.now()
          console.log('token过期时间:', expiresAt)
          console.log('当前时间:', now)
          console.log('是否过期:', now >= expiresAt)
          if (now >= expiresAt) {
            // token已过期，清除信息
            logout()
            return false
          } else {
            // token未过期，启动刷新定时器
            startTokenRefreshTimer()
            return true
          }
        }
      }
    } catch (error) {
      console.error('恢复用户信息失败:', error)
      logout()
    }
    return false
  }

  // 启动token刷新定时器
  const startTokenRefreshTimer = () => {
    // 清除现有定时器
    clearAllTimers()

    console.log('启动token刷新定时器...')

    // 1. 定时刷新定时器（每30分钟主动刷新一次）
    refreshTimer.value = setInterval(async () => {
      if (userInfo.value.refresh_token) {
        console.log('定时刷新token（30分钟间隔）...')
        await refreshToken()
      }
    }, REFRESH_CONFIG.REGULAR_INTERVAL)

    // 2. 过期检查定时器（每分钟检查一次，用于兜底保护）
    // expireCheckTimer.value = setInterval(async () => {
    //   if (isTokenExpiringSoon.value && userInfo.value.refresh_token) {
    //     console.log('token即将过期，立即刷新...')
    //     await refreshToken()
    //   }
    // }, REFRESH_CONFIG.EXPIRE_CHECK_INTERVAL)

    // console.log(`定时器已启动：定时刷新间隔${REFRESH_CONFIG.REGULAR_INTERVAL/60000}分钟，过期检查间隔${REFRESH_CONFIG.EXPIRE_CHECK_INTERVAL/1000}秒`)
  }

  // 清除所有定时器
  const clearAllTimers = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
    if (expireCheckTimer.value) {
      clearInterval(expireCheckTimer.value)
      expireCheckTimer.value = null
    }
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      if (!userInfo.value.refresh_token) {
        throw new Error('没有refresh_token')
      }

      console.log('开始刷新token...')
      console.log('当前refresh_token:', userInfo.value.refresh_token?.substring(0, 20) + '...')
      console.log('当前access_token:', userInfo.value.access_token?.substring(0, 20) + '...')

      const response = await refreshTokenApi(userInfo.value.refresh_token, userInfo.value.access_token)
      console.log('刷新token响应:', response)

      // 兼容不同的响应格式
      if (response.status === 1 ) {
        const tokenData = response.data || response

        // 更新token信息（不重新启动定时器，避免递归）
        const newUserInfo = {
          ...userInfo.value,
          access_token: tokenData.access_token,
          access_token_expires_at: tokenData.access_token_expires_at,
          refresh_token: tokenData.refresh_token || userInfo.value.refresh_token
        }

        // 直接更新userInfo，不调用setUserInfo（避免重新启动定时器）
        userInfo.value = newUserInfo
        localStorage.setItem('userInfo', JSON.stringify(newUserInfo))

        console.log('Token刷新成功，新的过期时间:', tokenData.access_token_expires_at)
        return true
      } else {
        throw new Error(response.message || response.msg || 'Token刷新失败')
      }
    } catch (error) {
      console.error('Token刷新失败:', error)
      // 刷新失败，清除用户信息
      logout()
      return false
    }
  }

  // 登出
  const logout = () => {
    console.log('用户登出，清除所有数据和定时器...')

    userInfo.value = {
      access_token: '',
      refresh_token: '',
      access_token_expires_at: null,
      user_info: {}
    }

    // 清除localStorage
    localStorage.removeItem('userInfo')

    // 清除所有定时器
    clearAllTimers()
  }

  return {
    userInfo,
    isLoggedIn,
    isTokenExpiringSoon,
    setUserInfo,
    restoreUserInfo,
    refreshToken,
    logout
  }
})