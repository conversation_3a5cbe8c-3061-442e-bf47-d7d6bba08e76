{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"rand_pcg\", \"small_rng\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 2225463790103693989, "path": 10293373026704675588, "deps": [[1333041802001714747, "rand_chacha", false, 13517963358369219980], [1740877332521282793, "rand_core", false, 9355622796080968164], [5170503507811329045, "getrandom_package", false, 8272287408650288787], [9875507072765444643, "rand_pcg", false, 15358047123358858175]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-1c3b5d77b29ff779\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}