"""
统计服务模块
处理所有与统计信息相关的业务逻辑
"""

import sqlite3
import logging
from pathlib import Path


class StatsService:
    """统计服务类"""
    
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        self.db_path = self.base_dir / "database.db"
    
    def get_stats(self):
        """
        获取账号总数、任务总数、视频总数等统计信息
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 账号总数
                cursor.execute("SELECT COUNT(*) FROM user_info")
                account_count = cursor.fetchone()[0]
                
                # 视频总数
                cursor.execute("SELECT COUNT(*) FROM file_records")
                video_count = cursor.fetchone()[0]
                
                # 任务总数（假设有 task_records 表，如果没有请替换为实际任务表名或逻辑）
                try:
                    cursor.execute("SELECT COUNT(*) FROM task_records")
                    task_count = cursor.fetchone()[0]
                except Exception:
                    task_count = None  # 如果没有任务表则为 None

            return {
                "success": True,
                "code": 200,
                "msg": "success",
                "data": {
                    "account_count": account_count,
                    "video_count": video_count,
                    "task_count": task_count
                }
            }

        except Exception as e:
            logging.error(f"获取统计信息失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": f"统计信息获取失败: {str(e)}",
                "data": None
            }
    
    def get_file_stats(self):
        """
        获取文件相关统计信息
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 文件总数
                cursor.execute("SELECT COUNT(*) FROM file_records")
                total_files = cursor.fetchone()[0]
                
                # 文件总大小（MB）
                cursor.execute("SELECT SUM(filesize) FROM file_records")
                total_size = cursor.fetchone()[0] or 0
                
                # 按类型统计（如果有分类字段）
                try:
                    cursor.execute("""
                        SELECT category, COUNT(*) as count 
                        FROM file_records 
                        WHERE category IS NOT NULL 
                        GROUP BY category
                    """)
                    category_stats = [{"category": row[0], "count": row[1]} for row in cursor.fetchall()]
                except Exception:
                    category_stats = []
                
                # 最近上传的文件
                try:
                    cursor.execute("""
                        SELECT filename, filesize, created_at 
                        FROM file_records 
                        ORDER BY id DESC 
                        LIMIT 5
                    """)
                    recent_files = [
                        {
                            "filename": row[0], 
                            "filesize": row[1], 
                            "created_at": row[2] if len(row) > 2 else None
                        } 
                        for row in cursor.fetchall()
                    ]
                except Exception:
                    recent_files = []

            return {
                "success": True,
                "code": 200,
                "msg": "success",
                "data": {
                    "total_files": total_files,
                    "total_size_mb": round(total_size, 2),
                    "category_stats": category_stats,
                    "recent_files": recent_files
                }
            }

        except Exception as e:
            logging.error(f"获取文件统计信息失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": f"文件统计信息获取失败: {str(e)}",
                "data": None
            }
    
    def get_account_stats(self):
        """
        获取账号相关统计信息
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 账号总数
                cursor.execute("SELECT COUNT(*) FROM user_info")
                total_accounts = cursor.fetchone()[0]
                
                # 有效账号数（status = 1）
                cursor.execute("SELECT COUNT(*) FROM user_info WHERE status = 1")
                active_accounts = cursor.fetchone()[0]
                
                # 按类型统计
                cursor.execute("""
                    SELECT type, COUNT(*) as count 
                    FROM user_info 
                    GROUP BY type
                """)
                type_stats = [{"type": row[0], "count": row[1]} for row in cursor.fetchall()]
                
                # 按状态统计
                cursor.execute("""
                    SELECT status, COUNT(*) as count 
                    FROM user_info 
                    GROUP BY status
                """)
                status_stats = [{"status": row[0], "count": row[1]} for row in cursor.fetchall()]

            return {
                "success": True,
                "code": 200,
                "msg": "success",
                "data": {
                    "total_accounts": total_accounts,
                    "active_accounts": active_accounts,
                    "inactive_accounts": total_accounts - active_accounts,
                    "type_stats": type_stats,
                    "status_stats": status_stats
                }
            }

        except Exception as e:
            logging.error(f"获取账号统计信息失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": f"账号统计信息获取失败: {str(e)}",
                "data": None
            }


# 便捷函数供路由调用
def get_stats_handler(base_dir):
    """获取统计信息处理函数"""
    service = StatsService(base_dir)
    return service.get_stats()


def get_file_stats_handler(base_dir):
    """获取文件统计信息处理函数"""
    service = StatsService(base_dir)
    return service.get_file_stats()


def get_account_stats_handler(base_dir):
    """获取账号统计信息处理函数"""
    service = StatsService(base_dir)
    return service.get_account_stats()
