#!/usr/bin/env python3
"""
使用 PyInstaller 打包简化版本
避免 Nuitka 的 DLL 依赖检测问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def install_pyinstaller():
    """安装 PyInstaller"""
    print("检查 PyInstaller...")
    
    try:
        result = subprocess.run([
            "..\\venv\\Scripts\\python.exe", "-c", "import PyInstaller; print(PyInstaller.__version__)"
        ], check=True, capture_output=True, text=True)
        print(f"✓ PyInstaller 已安装: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError:
        print("安装 PyInstaller...")
        try:
            result = subprocess.run([
                "..\\venv\\Scripts\\python.exe", "-m", "pip", "install", "pyinstaller"
            ], check=True, capture_output=True, text=True)
            print("✓ PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller 安装失败: {e}")
            return False

def build_with_pyinstaller():
    """使用 PyInstaller 进行打包"""
    print("开始 PyInstaller 打包...")
    
    # PyInstaller 命令
    pyinstaller_cmd = [
        "..\\venv\\Scripts\\python.exe", "-m", "PyInstaller",
        "--onedir",  # 创建目录而不是单文件
        "--windowed",  # 无控制台窗口
        "--name=xiaochao-simple",
        "--icon=logo.ico",
        "--distpath=out_pyinstaller",
        "--workpath=build_pyinstaller",
        "--specpath=.",
        
        # 添加数据文件
        "--add-data=database.db;.",
        "--add-data=conf.py;.",
        "--add-data=ffmpeg_config.py;.",
        "--add-data=services;services",
        "--add-data=utils;utils",
        "--add-data=myUtils;myUtils",
        "--add-data=logo.ico;.",
        
        # 隐藏导入
        "--hidden-import=services",
        "--hidden-import=utils",
        "--hidden-import=myUtils",
        "--hidden-import=sqlite3",
        "--hidden-import=logging",
        "--hidden-import=json",
        "--hidden-import=flask",
        "--hidden-import=requests",
        "--hidden-import=flask_cors",
        
        # 主文件
        "main_simple.py"
    ]
    
    print("执行命令:", " ".join(pyinstaller_cmd))
    
    try:
        result = subprocess.run(pyinstaller_cmd, check=True, capture_output=True, text=True)
        print("PyInstaller 打包成功!")
        if result.stdout:
            print("输出:", result.stdout[-500:])  # 显示最后500字符
        return True
    except subprocess.CalledProcessError as e:
        print("PyInstaller 打包失败!")
        print("错误输出:", e.stderr[-1000:] if e.stderr else "无错误输出")
        if e.stdout:
            print("标准输出:", e.stdout[-500:])
        return False

def create_launch_script():
    """创建启动脚本"""
    script_content = """@echo off
echo ========================================
echo 小超媒体管理系统 - PyInstaller 版本
echo ========================================
echo.

echo 启动程序...
xiaochao-simple.exe

echo.
echo ========================================
if errorlevel 1 (
    echo 程序异常退出，错误代码: %errorlevel%
    echo.
    echo 这是 PyInstaller 打包版本
    echo 如果有问题，请检查依赖文件是否完整
) else (
    echo 程序正常退出
)
echo ========================================
pause
"""
    
    dist_dir = Path("out_pyinstaller/xiaochao-simple")
    if dist_dir.exists():
        script_path = dist_dir / "启动.bat"
        with open(script_path, 'w', encoding='gbk') as f:
            f.write(script_content)
        print(f"创建启动脚本: {script_path}")
        return True
    else:
        print("❌ 打包目录不存在")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("小超媒体管理系统 - PyInstaller 简化版打包工具")
    print("=" * 60)
    
    if not os.path.exists("main_simple.py"):
        print("错误: 未找到 main_simple.py")
        return
    
    # 执行步骤
    steps = [
        ("安装 PyInstaller", install_pyinstaller),
        ("PyInstaller 打包", build_with_pyinstaller),
        ("创建启动脚本", create_launch_script),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if not step_func():
            print(f"❌ 步骤失败: {step_name}")
            return
        print(f"✓ {step_name} 完成")
    
    print("\n" + "="*60)
    print("🎉 PyInstaller 打包完成!")
    print("📁 可执行文件: out_pyinstaller/xiaochao-simple/xiaochao-simple.exe")
    print("🚀 启动脚本: out_pyinstaller/xiaochao-simple/启动.bat")
    print()
    print("💡 测试步骤:")
    print("   1. 运行启动脚本")
    print("   2. 访问 http://localhost:5000")
    print("   3. 测试基本 API 接口")
    print("=" * 60)

if __name__ == "__main__":
    main()
