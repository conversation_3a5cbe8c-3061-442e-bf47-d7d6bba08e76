// 不使用 fetch，改用 Tauri 的 HTTP 客户端
import { getClient } from "@tauri-apps/plugin-http";
import { ElMessage } from "element-plus";
import { AUTH_API_CONFIG } from "@/config/api";

// 认证服务器基础URL - 与主应用服务器不同
const AUTH_BASE_URL = AUTH_API_CONFIG.baseURL;

// 获取用户token的函数
const getUserToken = () => {
  try {
    const userInfo = JSON.parse(localStorage.getItem("userInfo") || "{}");
    return userInfo.access_token || null;
  } catch {
    return null;
  }
};

// 认证专用请求函数
async function authRequest({
  url,
  method = "GET",
  data = null,
  params = null,
  headers = {},
}) {
  // 设置默认请求头
  headers["Content-Type"] = headers["Content-Type"] || "application/json";
  headers["Accept"] = "application/json";

  // 对于某些认证接口（如刷新token、登出、验证），需要添加Authorization头
  const token = getUserToken();
  if (
    token &&
    (url.includes("refresh") ||
      url.includes("logout") ||
      url.includes("verify"))
  ) {
    headers["Authorization"] = `Bearer ${token}`;
    console.log(
      "[authRequest] 添加Authorization头:",
      `Bearer ${token.substring(0, 20)}...`
    );
  }

  // 拼接参数到URL
  let fullUrl = AUTH_BASE_URL + url;
  if (params && Object.keys(params).length > 0) {
    const query = new URLSearchParams(params).toString();
    fullUrl += (fullUrl.includes("?") ? "&" : "?") + query;
  }

  try {
    // 使用 Tauri HTTP 客户端而不是 fetch
    console.log("[authRequest] 创建 HTTP 客户端...");
    const client = await getClient();

    const response = await client.request({
      url: fullUrl,
      method,
      headers,
      body: data
        ? headers["Content-Type"] === "application/json"
          ? JSON.stringify(data)
          : data
        : undefined,
    });

    console.log("[authRequest] url:", fullUrl);
    console.log("[authRequest] method:", method);
    console.log("[authRequest] headers:", headers);
    console.log("[authRequest] data:", data);
    console.log("[authRequest] response status:", response.status);
    console.log("[authRequest] response headers:", response.headers);

    // 处理响应数据 - Tauri HTTP 客户端直接返回数据
    console.log("[authRequest] 处理响应数据...");
    let text = "";

    if (response.data) {
      if (typeof response.data === 'string') {
        text = response.data;
        console.log("[authRequest] 响应数据是字符串，长度:", text.length);
      } else if (response.data instanceof ArrayBuffer) {
        text = new TextDecoder().decode(response.data);
        console.log("[authRequest] 响应数据是 ArrayBuffer，转换后长度:", text.length);
      } else if (typeof response.data === 'object') {
        text = JSON.stringify(response.data);
        console.log("[authRequest] 响应数据是对象，序列化后长度:", text.length);
      } else {
        text = String(response.data);
        console.log("[authRequest] 响应数据转换为字符串，长度:", text.length);
      }
    } else {
      console.warn("[authRequest] 响应没有数据");
      text = "";
    }

    console.log("[authRequest] response text:", text.substring(0, 500) + (text.length > 500 ? "..." : ""));

    let resData = {};
    try {
      resData = text ? JSON.parse(text) : {};
    } catch (parseError) {
      console.error("[authRequest] JSON parse error:", parseError);
      resData = { code: response.status, message: "响应格式错误" };
    }

    console.log("[authRequest] resData:", resData);

    if (!response.ok) {
      // 处理HTTP错误状态码
      let errorMessage = "";
      if (response.status === 401) {
        errorMessage = "认证失败，请重新登录";
      } else if (response.status === 403) {
        errorMessage = "访问被拒绝";
      } else if (response.status === 404) {
        errorMessage = "认证服务不可用";
        // 404不需要登出，可能只是服务暂时不可用
      } else if (response.status >= 500) {
        errorMessage = "认证服务器错误";
        // 5xx错误不需要登出，是服务器问题
      } else {
        errorMessage = resData?.message || resData?.msg || "请求失败";
      }

      ElMessage.error(errorMessage);

      // 如果需要登出，清除登录状态
      localStorage.removeItem("userInfo");
      if (window.location.hash !== "#/login") {
        window.location.hash = "#/login";
        window.location.reload();
      }

      throw new Error(
        resData?.message || resData?.msg || `HTTP ${response.status}`
      );
    }

    // 检查业务状态码
    if (resData.status === 1) {
      return resData;
    } else {
      const errorMsg =
        resData?.message || resData?.msg || resData?.error || "请求失败";

      throw new Error(errorMsg);
    }
  } catch (error) {
    console.error("[authRequest] Error:", error);
    // 如果需要登出，清除登录状态
    ElMessage.error(error.message || "网络连接失败");
    localStorage.removeItem("userInfo");
    if (window.location.hash !== "#/login") {
      window.location.hash = "#/login";
      window.location.reload();
    }

    throw error;
  }
}

// 封装认证相关的请求方法
export const authHttp = {
  get(url, params) {
    return authRequest({ url, method: "GET", params });
  },

  post(url, data, config = {}) {
    return authRequest({
      url,
      method: "POST",
      data,
      headers: config.headers || {},
    });
  },

  put(url, data, config = {}) {
    return authRequest({
      url,
      method: "PUT",
      data,
      headers: config.headers || {},
    });
  },

  delete(url, params) {
    return authRequest({ url, method: "DELETE", params });
  },
};

export default authRequest;
