use std::collections::HashMap;
use std::process::Command;
use sha2::{Sha256, Digest};
use regex::Regex;
use tauri::utils::platform::current_exe;

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct SigResult {
    pub success: bool,
    pub signature: Option<String>,
    pub error: Option<String>,
}

/// 实现 Python 中的 sig_py 函数
/// 生成基于查询参数和数据参数的 MD5 签名
#[tauri::command]
pub fn sig_py(query_params: HashMap<String, String>, data_params: HashMap<String, String>) -> String {
    // salt 值，对应 Python 中的 b'382700b563f4'
    let salt = b"382700b563f4";
    
    // 合并并排序所有参数
    let mut all_params = Vec::new();
    
    // 添加查询参数
    for (key, value) in query_params.iter() {
        all_params.push((key.clone(), value.clone()));
    }
    
    // 添加数据参数
    for (key, value) in data_params.iter() {
        all_params.push((key.clone(), value.clone()));
    }
    
    // 按键排序
    all_params.sort_by(|a, b| a.0.cmp(&b.0));
    
    // 构建签名字符串
    let param_string: String = all_params
        .iter()
        .map(|(key, value)| format!("{}={}", key, value))
        .collect::<Vec<String>>()
        .join("");
    
    // 添加 salt 并计算 MD5
    let mut input = param_string.into_bytes();
    input.extend_from_slice(salt);
    let result = md5::compute(&input);

    // 转换为十六进制字符串
    format!("{:x}", result)
}

/// 实现 Python 中的 token_py 函数
/// 生成基于签名和客户端盐值的 SHA256 token
#[tauri::command]
pub fn token_py(sig: String, client_salt: String) -> String {
    let combined = format!("{}{}", sig, client_salt);

    let mut hasher = Sha256::new();
    Digest::update(&mut hasher, combined.as_bytes());
    let result = Digest::finalize(hasher);

    // 转换为十六进制字符串
    format!("{:x}", result)
}

/// 实现 Python 中的 extract_signature 函数
/// 从文本中提取 ##...## 之间的内容
#[tauri::command]
pub fn extract_signature(text: String) -> Option<String> {
    let re = Regex::new(r"##(.*?)##").ok()?;
    
    if let Some(captures) = re.captures(&text) {
        captures.get(1).map(|m| m.as_str().to_string())
    } else {
        None
    }
}

/// 实现 Python 中的 get_sig3 函数
/// 调用 Java JAR 文件生成签名
#[tauri::command]
pub async fn get_sig3(param: String) -> SigResult {
    // 获取当前可执行文件目录
    let exe_dir = match current_exe() {
        Ok(exe_path) => {
            exe_path.parent()
                .unwrap_or_else(|| std::path::Path::new(""))
                .to_path_buf()
        }
        Err(_) => {
            return SigResult {
                success: false,
                signature: None,
                error: Some("无法获取当前可执行文件路径".to_string()),
            };
        }
    };
    
    // 构建 JAR 文件路径
    let jar_path = exe_dir.join("ks_sig3.jar");
    
    // 检查 JAR 文件是否存在
    if !jar_path.exists() {
        return SigResult {
            success: false,
            signature: None,
            error: Some(format!("JAR 文件不存在: {}", jar_path.display())),
        };
    }
    
    // 构建 Java 命令
    let mut command = Command::new("java");
    command
        .arg("-jar")
        .arg(&jar_path)
        .arg(&param);
    
    // 在 Windows 上设置创建标志以隐藏窗口
    #[cfg(target_os = "windows")]
    {
        use std::os::windows::process::CommandExt;
        command.creation_flags(0x08000000); // CREATE_NO_WINDOW
    }
    
    // 执行命令
    match command.output() {
        Ok(output) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if !stderr.is_empty() {
                return SigResult {
                    success: false,
                    signature: None,
                    error: Some(format!("Java 执行错误: {}", stderr)),
                };
            }
            
            // 提取签名
            if let Some(signature) = extract_signature(stdout.to_string()) {
                SigResult {
                    success: true,
                    signature: Some(signature),
                    error: None,
                }
            } else {
                SigResult {
                    success: false,
                    signature: None,
                    error: Some("无法从输出中提取签名".to_string()),
                }
            }
        }
        Err(e) => SigResult {
            success: false,
            signature: None,
            error: Some(format!("执行 Java 命令失败: {}", e)),
        },
    }
}

/// 辅助函数：检查 Java 是否可用
#[tauri::command]
pub fn check_java_available() -> bool {
    match Command::new("java").arg("-version").output() {
        Ok(output) => output.status.success(),
        Err(_) => false,
    }
}

/// 辅助函数：获取 JAR 文件路径
#[tauri::command]
pub fn get_jar_path() -> Result<String, String> {
    let exe_dir = current_exe()
        .map_err(|e| format!("无法获取可执行文件路径: {}", e))?
        .parent()
        .ok_or("无法获取父目录")?
        .to_path_buf();
    
    let jar_path = exe_dir.join("ks_sig3.jar");
    
    if jar_path.exists() {
        Ok(jar_path.to_string_lossy().to_string())
    } else {
        Err(format!("JAR 文件不存在: {}", jar_path.display()))
    }
}

/// 批量处理签名生成
#[tauri::command]
pub async fn batch_get_sig3(params: Vec<String>) -> Vec<SigResult> {
    let mut results = Vec::new();
    
    for param in params {
        let result = get_sig3(param).await;
        results.push(result);
    }
    
    results
}

/// 验证签名格式
#[tauri::command]
pub fn validate_signature(signature: String) -> bool {
    // 检查是否为有效的十六进制字符串
    let hex_regex = Regex::new(r"^[a-fA-F0-9]+$").unwrap();
    hex_regex.is_match(&signature) && signature.len() >= 32
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_sig_py() {
        let mut query_params = HashMap::new();
        query_params.insert("param1".to_string(), "value1".to_string());
        
        let mut data_params = HashMap::new();
        data_params.insert("param2".to_string(), "value2".to_string());
        
        let result = sig_py(query_params, data_params);
        assert!(!result.is_empty());
        assert_eq!(result.len(), 32); // MD5 hash length
    }

    #[test]
    fn test_token_py() {
        let sig = "test_signature".to_string();
        let client_salt = "test_salt".to_string();
        
        let result = token_py(sig, client_salt);
        assert!(!result.is_empty());
        assert_eq!(result.len(), 64); // SHA256 hash length
    }

    #[test]
    fn test_extract_signature() {
        let text = "Some text ##extracted_signature## more text".to_string();
        let result = extract_signature(text);
        assert_eq!(result, Some("extracted_signature".to_string()));
        
        let text_no_match = "No signature here".to_string();
        let result_no_match = extract_signature(text_no_match);
        assert_eq!(result_no_match, None);
    }

    #[test]
    fn test_validate_signature() {
        assert!(validate_signature("abcdef1234567890abcdef1234567890".to_string()));
        assert!(!validate_signature("invalid_signature".to_string()));
        assert!(!validate_signature("short".to_string()));
    }
}
