<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器日志查看器</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #2d2d30;
            border-radius: 5px;
        }
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .controls select, .controls input, .controls button {
            padding: 5px 10px;
            border: 1px solid #3e3e42;
            background-color: #2d2d30;
            color: #d4d4d4;
            border-radius: 3px;
        }
        .controls button {
            cursor: pointer;
            background-color: #0e639c;
        }
        .controls button:hover {
            background-color: #1177bb;
        }
        .log-container {
            background-color: #0d1117;
            border: 1px solid #30363d;
            border-radius: 5px;
            height: 600px;
            overflow-y: auto;
            padding: 10px;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 2px;
            padding: 2px 5px;
            border-radius: 2px;
        }
        .log-entry.DEBUG { color: #8b949e; }
        .log-entry.INFO { color: #58a6ff; }
        .log-entry.WARNING { color: #f85149; }
        .log-entry.ERROR { color: #ff6b6b; background-color: #2d1b1b; }
        .log-entry.CRITICAL { color: #ffffff; background-color: #8b0000; }
        .timestamp {
            color: #7d8590;
            margin-right: 10px;
        }
        .level {
            font-weight: bold;
            margin-right: 10px;
            min-width: 60px;
            display: inline-block;
        }
        .message {
            word-wrap: break-word;
        }
        .status {
            margin-left: 10px;
            font-size: 12px;
            color: #7d8590;
        }
        .auto-scroll {
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>服务器日志查看器</h1>
            <div class="controls">
                <label>日志级别:</label>
                <select id="levelFilter">
                    <option value="">全部</option>
                    <option value="DEBUG">DEBUG</option>
                    <option value="INFO">INFO</option>
                    <option value="WARNING">WARNING</option>
                    <option value="ERROR">ERROR</option>
                    <option value="CRITICAL">CRITICAL</option>
                </select>
                
                <label>条数:</label>
                <input type="number" id="limitInput" value="100" min="1" max="1000">
                
                <button onclick="refreshLogs()">刷新</button>
                <button onclick="clearLogs()">清空缓存</button>
                <button onclick="toggleAutoRefresh()" id="autoRefreshBtn">开启自动刷新</button>
                
                <label class="auto-scroll">
                    <input type="checkbox" id="autoScroll" checked> 自动滚动
                </label>
                
                <span class="status" id="status">就绪</span>
            </div>
        </div>
        
        <div class="log-container" id="logContainer">
            <div class="log-entry INFO">
                <span class="timestamp">等待加载日志...</span>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        // 格式化时间戳
        function formatTimestamp(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 渲染日志条目
        function renderLogEntry(log) {
            return `
                <div class="log-entry ${log.level}">
                    <span class="timestamp">${formatTimestamp(log.time)}</span>
                    <span class="level">[${log.level}]</span>
                    <span class="message">${escapeHtml(log.message)}</span>
                </div>
            `;
        }

        // HTML 转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 获取日志
        async function fetchLogs() {
            try {
                const level = document.getElementById('levelFilter').value;
                const limit = document.getElementById('limitInput').value;
                
                let url = `/api/logs?limit=${limit}`;
                if (level) {
                    url += `&level=${level}`;
                }

                const response = await fetch(url);
                const result = await response.json();

                if (result.code === 200) {
                    const container = document.getElementById('logContainer');
                    const logs = result.data.logs;
                    
                    if (logs.length === 0) {
                        container.innerHTML = '<div class="log-entry INFO"><span class="message">暂无日志</span></div>';
                    } else {
                        container.innerHTML = logs.map(renderLogEntry).join('');
                    }

                    // 自动滚动到底部
                    if (document.getElementById('autoScroll').checked) {
                        container.scrollTop = container.scrollHeight;
                    }

                    document.getElementById('status').textContent = 
                        `已加载 ${logs.length} 条日志 (缓存总数: ${result.data.cache_size})`;
                } else {
                    throw new Error(result.msg);
                }
            } catch (error) {
                console.error('获取日志失败:', error);
                document.getElementById('status').textContent = `错误: ${error.message}`;
            }
        }

        // 刷新日志
        function refreshLogs() {
            document.getElementById('status').textContent = '加载中...';
            fetchLogs();
        }

        // 清空日志缓存
        async function clearLogs() {
            if (!confirm('确定要清空日志缓存吗？')) {
                return;
            }

            try {
                const response = await fetch('/api/logs/clear', { method: 'POST' });
                const result = await response.json();

                if (result.code === 200) {
                    document.getElementById('status').textContent = '日志缓存已清空';
                    refreshLogs();
                } else {
                    throw new Error(result.msg);
                }
            } catch (error) {
                console.error('清空日志失败:', error);
                document.getElementById('status').textContent = `错误: ${error.message}`;
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                btn.textContent = '开启自动刷新';
                isAutoRefresh = false;
                document.getElementById('status').textContent = '自动刷新已关闭';
            } else {
                autoRefreshInterval = setInterval(refreshLogs, 2000); // 每2秒刷新
                btn.textContent = '关闭自动刷新';
                isAutoRefresh = true;
                document.getElementById('status').textContent = '自动刷新已开启';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshLogs();
            
            // 绑定过滤器变化事件
            document.getElementById('levelFilter').addEventListener('change', refreshLogs);
            document.getElementById('limitInput').addEventListener('change', refreshLogs);
        });
    </script>
</body>
</html>
