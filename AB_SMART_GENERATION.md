# 智能AB去重系统优化方案

## 优化概述

原系统问题：
- 每个下载的视频只生成1个AB去重版本
- 多个账号发布时都使用相同的AB去重视频
- 容易被平台识别为重复内容

优化后方案：
- **智能生成**：根据启用自动发布的快手账号数量，生成相应数量的AB去重版本
- **差异化分发**：每个账号获得不同的AB去重版本，避免重复
- **性能优化**：合理控制生成数量，避免资源浪费
- **简化架构**：移除兼容逻辑，统一使用新的多版本机制

## 核心逻辑流程

### 1. 账号数量检测
```python
async def _get_auto_publish_account_count(self):
    """获取启用自动发布的快手账号数量"""
    # 查询数据库：SELECT COUNT(*) FROM user_info WHERE type = 4 AND auto_publish = 1
    # 返回启用自动发布的快手账号数量
```

### 2. 智能图片生成
```python
# 根据账号数量计算所需图片总数
total_images_needed = ab_video_count * 3  # 每个AB版本需要3张图片

# 调用AI图片生成API
image_urls = self._generate_images_from_api(
    video_generation_key, 
    prompt, 
    image_count=total_images_needed
)
```

### 3. 多版本AB生成
```python
for i in range(ab_video_count):
    # 为每个AB版本选择不同的图片组合
    start_idx = (i * 3) % len(downloaded_images)
    batch_images = downloaded_images[start_idx:start_idx+3]
    
    # 生成唯一的副视频
    secondary_video_name = f"secondary_{uuid.uuid1()}_v{i+1}.mp4"
    
    # 执行AB去重处理
    ab_result = await self._execute_ab_processing(
        video_file_path, 
        secondary_video_path, 
        result, 
        version_suffix=f"v{i+1}"
    )
```

### 4. 智能分发机制
```python
# 为每个账号分配不同的AB版本
for i, account in enumerate(kuaishou_accounts):
    # 循环分配AB版本
    version_index = i % len(ab_versions)
    assigned_version = ab_versions[version_index]
    
    # 单独发布到该账号
    post_video_ks(
        title=title,
        files=[assigned_version.get('filename')],
        account_file=[account.get('filePath')],
        cover_list=[assigned_version.get('cover_image')]
    )
```

## 优化效果

### 内容差异化
- **原系统**：3个账号 → 1个AB版本 → 3个相同视频
- **新系统**：3个账号 → 3个AB版本 → 3个不同视频

### 资源效率
- **智能生成**：账号数量 = AB版本数量，避免浪费
- **图片复用**：不足时智能复用图片，保证每个版本都有差异

### 发布成功率
- **差异化内容**：降低平台重复检测风险
- **独立发布**：每个账号使用不同视频文件
- **错误隔离**：单个账号失败不影响其他账号

## 数据库结构

### 多版本信息存储
```json
{
  "ab_processed": true,
  "ab_versions": [
    {
      "version": 1,
      "output_path": "/path/to/AB去重_uuid_v1_video.mp4",
      "filename": "AB去重_uuid_v1_video.mp4",
      "record_id": 123,
      "cover_image": "/path/to/cover1.jpg"
    },
    {
      "version": 2,
      "output_path": "/path/to/AB去重_uuid_v2_video.mp4",
      "filename": "AB去重_uuid_v2_video.mp4", 
      "record_id": 124,
      "cover_image": "/path/to/cover2.jpg"
    }
  ],
  "ab_count": 2
}
```

## 性能考量

### 计算资源
- **图片生成**：一次性生成所需图片，减少API调用
- **视频处理**：并行处理多个AB版本
- **存储优化**：清理临时文件，避免磁盘占用

### 网络资源
- **批量图片下载**：一次性下载所有图片
- **API效率**：单次调用生成多张图片
- **发布间隔**：账号间1秒延迟，避免并发压力

## 兼容性设计

### 容错机制
- 图片生成失败 → 保存原视频
- AB处理失败 → 降级到原视频
- 部分版本失败 → 使用成功的版本
- 无AB版本时 → 使用原始视频文件

## 使用场景

### 场景1：单账号
- 启用自动发布账号：1个
- 生成AB版本：1个
- 行为：与原系统相同

### 场景2：多账号
- 启用自动发布账号：3个
- 生成AB版本：3个
- 分配：账号1→版本1，账号2→版本2，账号3→版本3

### 场景3：账号数超过AB版本
- 启用自动发布账号：5个
- 生成AB版本：5个
- 分配：循环分配，确保每个账号都有不同版本

## 监控与日志

### 关键日志
```
检测到 3 个启用自动发布的快手账号，将生成 3 个AB去重版本
正在生成第 1/3 个AB版本，使用图片索引 0-2
账号 'user1' 分配AB版本 1 (文件: AB去重_uuid_v1_video.mp4)
✅ 账号 'user1' AB版本1 发布任务提交成功
```

### 性能指标
- AB版本生成成功率
- 图片生成效率
- 账号发布成功率
- 资源使用情况

## 配置说明

### 环境要求
- Python 3.7+
- FFmpeg (支持CUDA加速)
- MiniMaxi API密钥
- 启用自动发布的快手账号

### 参数调整
- `image_count`: 图片生成数量 = 账号数 × 3
- `duration_per_image`: 副视频中每张图片持续时间
- `delay_between_accounts`: 账号发布间隔时间

## 总结

通过智能生成机制，新系统实现了：

1. **自适应生成**：根据实际需要生成AB版本
2. **内容差异化**：每个账号获得独特内容  
3. **资源优化**：避免不必要的计算和存储
4. **发布成功率提升**：降低平台重复检测风险
5. **架构简化**：移除兼容逻辑，统一使用多版本机制

这样的设计既提高了内容的独特性，又保持了系统的高效性和稳定性，同时简化了代码架构便于维护。
