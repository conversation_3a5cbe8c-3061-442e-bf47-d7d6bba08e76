// API 配置文件

// 主应用服务器配置
export const MAIN_API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://admin.dev.com/',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
}

// 认证服务器配置
export const AUTH_API_CONFIG = {
  baseURL: import.meta.env.VITE_AUTH_BASE_URL || 'http://admin.dev.com/',
  timeout: 15000, // 认证服务可能需要更长时间
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
}

// API 端点定义
export const API_ENDPOINTS = {
  // 认证相关端点（使用认证服务器）
  AUTH: {
    LOGIN: '/api/card-secret/validatev2',
    REFRESH: '/api/card-secret/refresh-token',
    LOGOUT: '/api/card-secret/logout',
    VERIFY: '/api/card-secret/validatev3',
    TOKEN_CHECK: '/api/token/check'
  },
  
  // 主应用端点（使用主服务器）
  MAIN: {
    // 账号管理
    ACCOUNTS: '/getAllAccounts',
    VALID_ACCOUNTS: '/getValidAccounts',
    
    // 文件管理
    FILES: '/getFiles',
    UPLOAD: '/upload',
    
    // 发布相关
    POST_VIDEO: '/postVideo',
    
    // 日志相关
    LOGS: '/api/logs',
    LOGS_CLEAR: '/api/logs/clear'
  }
}

// 环境配置
export const ENV_CONFIG = {
  isDev: import.meta.env.DEV,
  isProd: import.meta.env.PROD,
  mode: import.meta.env.MODE
}

// 请求配置
export const REQUEST_CONFIG = {
  // 重试配置
  retry: {
    times: 3,
    delay: 1000
  },
  
  // 超时配置
  timeout: {
    default: 10000,
    upload: 60000,
    auth: 15000
  }
}

export default {
  MAIN_API_CONFIG,
  AUTH_API_CONFIG,
  API_ENDPOINTS,
  ENV_CONFIG,
  REQUEST_CONFIG
}
