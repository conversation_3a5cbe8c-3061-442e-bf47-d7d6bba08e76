import os
import sys
from pathlib import Path

# 全局禁用 Playwright 的依赖检查，避免 PrintDeps.exe 问题
os.environ['PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD'] = '1'
os.environ['PLAYWRIGHT_BROWSERS_PATH'] = '0'
os.environ['PLAYWRIGHT_SKIP_VALIDATE_HOST_REQUIREMENTS'] = '1'
os.environ['PLAYWRIGHT_SKIP_BROWSER_GC'] = '1'

def is_frozen():
    """检查是否为打包环境"""
    return getattr(sys, 'frozen', False) or hasattr(sys, '_MEIPASS')

# 基础配置
if is_frozen():
    # 打包环境：BASE_DIR 指向可执行文件目录（根目录）
    BASE_DIR = Path(sys.executable).parent.resolve()
    # 检查 cookiesFile 是否在根目录，如果不在则使用 _internal 目录
    COOKIES_DIR = BASE_DIR / "cookiesFile"
    if not COOKIES_DIR.exists():
        COOKIES_DIR = BASE_DIR / "_internal" / "cookiesFile"

    # 检查 videoFile 是否在根目录，如果不在则使用 _internal 目录
    VIDEO_DIR = BASE_DIR / "videoFile"
    if not VIDEO_DIR.exists():
        VIDEO_DIR = BASE_DIR / "_internal" / "videoFile"
else:
    # 开发环境：BASE_DIR 指向当前文件目录
    BASE_DIR = Path(__file__).parent.resolve()
    COOKIES_DIR = BASE_DIR / "cookiesFile"
    VIDEO_DIR = BASE_DIR / "videoFile"

XHS_SERVER = "http://127.0.0.1:11901"
HEADLESS = True

# 浏览器路径配置
if is_frozen():
    # 打包环境：优先使用系统浏览器
    system_chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
    ]

    LOCAL_CHROME_PATH = None
    for chrome_path in system_chrome_paths:
        if os.path.exists(chrome_path):
            LOCAL_CHROME_PATH = chrome_path
            break

    print(f"[INFO] 打包环境，浏览器路径: {LOCAL_CHROME_PATH or 'Playwright默认'}")
else:
    # 开发环境：使用 Playwright 默认浏览器
    LOCAL_CHROME_PATH = None
    print(f"[INFO] 开发环境，使用 Playwright 默认浏览器")

print(f"[INFO] 已禁用 Playwright 依赖检查")