# English translations for KS-Downloader package.
# Copyright (C) 2025 THE KS-Downloader'S COPYRIGHT HOLDER
# This file is distributed under the same license as the KS-Downloader package.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: KS-Downloader V1.5\n"
"Report-Msgid-Bugs-To: <<EMAIL>>\n"
"POT-Creation-Date: 2025-05-17 15:18+0800\n"
"PO-Revision-Date: 2025-03-22 10:49+0800\n"
"Last-Translator: <<EMAIL>>\n"
"Language-Team: English\n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:84
msgid "请输入快手作品链接："
msgstr "Please enter KuaiShou works link: "

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:98
msgid "读取并写入 Cookie 成功！"
msgstr "Cookie read and written successfully!"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:104
msgid "请选择 KS-Downloader 功能"
msgstr "Select KS-Downloader feature"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:119
msgid "启用"
msgstr "Enable"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:120
msgid "禁用"
msgstr "Disable"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:123
msgid "从浏览器读取 Cookie"
msgstr "Read Cookie from browser"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:124
msgid "批量下载链接作品"
msgstr "Batch download linked works"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:126
msgid "下载记录功能"
msgstr " Download history feature"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:129
msgid "检查程序版本更新"
msgstr "Check for updates"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:130
msgid "切换语言"
msgstr "切换至简体中文"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:155
msgid "检测新版本失败"
msgstr "Failed to check for new version"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:161
msgid "修改设置成功！"
msgstr "Settings updated successfully!"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:176
#, python-brace-format
msgid "项目地址：{repo}"
msgstr "Project repository: {repo}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:178
#, python-brace-format
msgid "开源协议：{licence}"
msgstr "License: {licence}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:185
msgid "提取作品链接失败"
msgstr "Failed to extract works link"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:192
#, python-brace-format
msgid "URL 解析失败：{url}"
msgstr "URL parsing failed: {url}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\app\app.py:289
msgid "是否已仔细阅读上述免责声明(YES/NO): "
msgstr "Have you read the above disclaimer carefully? (YES/NO): "

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\config.py:51
#, python-brace-format
msgid "配置文件编码错误：{error}"
msgstr "Configuration file encoding error: {error}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\config.py:52
msgid "本次运作将会使用默认配置参数！"
msgstr "Default configuration parameters will be used for this operation!"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\config.py:60
msgid "已创建默认配置文件"
msgstr "Default configuration file created"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:94
msgid "timeout 参数错误"
msgstr "Invalid timeout parameter"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:100
msgid "max_retry 参数错误"
msgstr "Invalid max_retry parameter"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:107
msgid "max_workers 参数错误"
msgstr "Invalid max_workers parameter"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:126
#, python-brace-format
msgid "代理 {proxy} 测试成功"
msgstr "Proxy {proxy} tested successfully"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:129
#, python-brace-format
msgid "代理 {proxy} 测试超时"
msgstr "Proxy {proxy} test timeout"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:132
#, python-brace-format
msgid "代理 {proxy} 测试失败：{error}"
msgstr "Proxy {proxy} test failed: {error}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:143
#, fuzzy
msgid "work_path 参数不是有效的文件夹路径，程序将使用项目根路径作为储存路径"
msgstr "Invalid works_path parameter, using project root as storage path"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:158
msgid "folder_name 参数不是有效的文件夹名称，程序将使用默认值：Download"
msgstr "Invalid folder_name parameter, using default: Download"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:167
msgid "cookie 参数错误"
msgstr "Invalid cookie parameter"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:173
msgid "cover 参数错误"
msgstr "Invalid cover parameter"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\config\parameter.py:194
#, python-brace-format
msgid "name_format 参数包含未知字段: {field}"
msgstr "name_format contains unknown field: {field}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:103
#, python-brace-format
msgid "作品 {detail_id} 存在下载记录，跳过下载！"
msgstr "Works {detail_id} exists in download history, skipping!"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:113
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:179
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\extract\extractor.py:159
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\extract\extractor.py:308
msgid "视频"
msgstr "Video"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:121
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:206
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\extract\extractor.py:108
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\extract\extractor.py:311
msgid "图片"
msgstr "Image"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:130
msgid "未知的作品类型"
msgstr "Unknown works type"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:158
msgid "音乐"
msgstr "Music"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:227
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:239
msgid "封面"
msgstr "Cover"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:261
#, python-brace-format
msgid "【{type}】{name} 下载链接为空"
msgstr "【{type}】{name} download link is empty"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:280
#, python-brace-format
msgid "【{type}】{name} 缓存异常，重新下载"
msgstr "【{type}】{name} cache error, re-downloading"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:305
#, python-brace-format
msgid "【{type}】{name} 下载完成"
msgstr "【{type}】{name} download completed"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:317
#, python-brace-format
msgid "未知的文件类型：{content_type}"
msgstr "Unknown file type: {content_type}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\downloader\downloader.py:334
#, python-brace-format
msgid "{filename} 已存在，跳过下载"
msgstr "{filename} already exists, skipping download"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\extract\extractor.py:44
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\extract\extractor.py:68
msgid "提取网页数据失败"
msgstr "Failed to extract web data"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\extract\extractor.py:59
msgid "获取网页内容失败"
msgstr "Failed to retrieve web content"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\extract\extractor.py:344
msgid "未知"
msgstr "Unknown"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\request\template.py:157
#, python-brace-format
msgid "{note}数据响应内容异常：{response}"
msgstr "{note} abnormal response: {response}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\request\template.py:171
#, python-brace-format
msgid "{note}数据响应内容为空"
msgstr "{note} empty response"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\static\internal.py:17
msgid ""
"关于 KS-Downloader 的 免责声明：\n"
"\n"
"1. 使用者对本项目的使用由使用者自行决定，并自行承担风险。作者对使用者使用本项"
"目所产生的任何损失、责任、或风险概不负责。\n"
"2. 本项目的作者提供的代码和功能是基于现有知识和技术的开发成果。作者按现有技术"
"水平努力确保代码的正确性和安全性，但不保证代码完全没有错误或缺陷。\n"
"3. 本项目依赖的所有第三方库、插件或服务各自遵循其原始开源或商业许可，使用者需"
"自行查阅并遵守相应协议，作者不对第三方组件的稳定性、安全性及合规性承担任何责"
"任。\n"
"4. 使用者在使用本项目时必须严格遵守 GNU General Public License v3.0 的要求，"
"并在适当的地方注明使用了 GNU General Public License v3.0 的代码。\n"
"5. 使用者在使用本项目的代码和功能时，必须自行研究相关法律法规，并确保其使用行"
"为合法合规。任何因违反法律法规而导致的法律责任和风险，均由使用者自行承担。\n"
"6. 使用者不得使用本工具从事任何侵犯知识产权的行为，包括但不限于未经授权下载、"
"传播受版权保护的内容，开发者不参与、不支持、不认可任何非法内容的获取或分"
"发。\n"
"7. 本项目不对使用者涉及的数据收集、存储、传输等处理活动的合规性承担责任。使用"
"者应自行遵守相关法律法规，确保处理行为合法正当；因违规操作导致的法律责任由使"
"用者自行承担。\n"
"8. 使用者在任何情况下均不得将本项目的作者、贡献者或其他相关方与使用者的使用行"
"为联系起来，或要求其对使用者使用本项目所产生的任何损失或损害负责。\n"
"9. 本项目的作者不会提供 KS-Downloader 项目的付费版本，也不会提供与 KS-"
"Downloader 项目相关的任何商业服务。\n"
"10. 基于本项目进行的任何二次开发、修改或编译的程序与原创作者无关，原创作者不"
"承担与二次开发行为或其结果相关的任何责任，使用者应自行对因二次开发可能带来的"
"各种情况负全部责任。\n"
"11. 本项目不授予使用者任何专利许可；若使用本项目导致专利纠纷或侵权，使用者自"
"行承担全部风险和责任。未经作者或权利人书面授权，不得使用本项目进行任何商业宣"
"传、推广或再授权。\n"
"12. 作者保留随时终止向任何违反本声明的使用者提供服务的权利，并可能要求其销毁"
"已获取的代码及衍生作品。\n"
"13. 作者保留在不另行通知的情况下更新本声明的权利，使用者持续使用即视为接受修"
"订后的条款。\n"
"\n"
"在使用本项目的代码和功能之前，请您认真考虑并接受以上免责声明。如果您对上述声"
"明有任何疑问或不同意，请不要使用本项目的代码和功能。如果您使用了本项目的代码"
"和功能，则视为您已完全理解并接受上述免责声明，并自愿承担使用本项目的一切风险"
"和后果。\n"
msgstr ""
"Disclaimer for KS-Downloader:\n"
"\n"
"1. The use of this project is entirely at the user's own discretion and risk. The author assumes no responsibility or liability of any kind for any loss, damage, or risk arising from the user's use of this project.\n"
"2. The code and functionalities provided by the author of this project are developed based on current knowledge and technology. The author makes every effort to ensure the correctness and security of the code according to current technical standards but does not guarantee that the code is completely free of errors or defects.\n"
"3. All third-party libraries, plugins, or services used by this project follow their original open-source or commercial licenses. Users must review and comply with these license agreements accordingly. The author assumes no responsibility for the stability, security, or compliance of any third-party components.\n"
"4. When using this project, users must strictly comply with the requirements of the GNU General Public License v3.0 and clearly indicate in appropriate places that the code was used under the GNU General Public License v3.0.\n"
"5. When using the code and functionalities of this project, users must independently research relevant laws and regulations and ensure that their usage is legal and compliant. Any legal liabilities or risks arising from violations of laws and regulations shall be borne solely by the user.\n"
"6. Users must not use this tool to engage in any activities that infringe intellectual property rights, including but not limited to downloading or distributing copyrighted content without authorization. Developers do not participate in, support, or endorse the acquisition or distribution of any illegal or unauthorized content.\n"
"7. This project assumes no responsibility for the compliance of data processing activities (including collection, storage, and transmission) performed by users. Users must comply with relevant laws and regulations and ensure that such activities are lawful and proper. Legal liabilities resulting from non-compliant operations shall be borne by the user.\n"
"8. Under no circumstances may users associate the author, contributors, or other related parties of this project with their usage of the project, nor may they hold these parties liable for any loss or damage resulting from such usage.\n"
"9. The author of this project will not provide a paid version of the KS-Downloader project, nor will they offer any commercial services related to it.\n"
"10. Any secondary development, modification, or compilation based on this project is unrelated to the original author. The original author assumes no liability for any consequences resulting from such secondary development. Users bear full responsibility for all outcomes arising from such modifications.\n"
"11. This project does not grant users any patent licenses. If the use of this project leads to patent disputes or infringement, the user assumes all associated risks and responsibilities. Without written authorization from the author or rights holder, users may not use this project for any commercial promotion, advertising, or re-licensing.\n"
"12. The author reserves the right to terminate service to any user who violates this disclaimer at any time and may require them to destroy all obtained code and derivative works.\n"
"13. The author reserves the right to update this disclaimer at any time without prior notice. Continued use of the project constitutes acceptance of the revised terms.\n"
"\n"
"Before using the code and functionalities of this project, please carefully consider and accept the above disclaimer. If you have any questions or disagree with the above statements, please do not use the code and functionalities of this project. If you do use the code and functionalities of this project, it shall be deemed that you have fully understood and accepted the above disclaimer and voluntarily assume all risks and consequences associated with its use.\n"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\browser.py:50
#, python-brace-format
msgid ""
"读取指定浏览器的 Cookie 并写入配置文件\n"
"注意：Windows 系统需要以管理员身份运行程序才能读取 Chromium、Chrome、Edge 浏"
"览器 Cookie！\n"
"{options}\n"
"请输入浏览器名称或序号："
msgstr ""
"Read browser cookies and write to config file\n"
"Note: On Windows, run as administrator to read Chromium/Chrome/Edge "
"cookies!\n"
"{options}\n"
"Enter browser name/number: "

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\browser.py:60
msgid "未选择浏览器！"
msgstr "No browser selected!"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\browser.py:71
msgid "浏览器名称或序号输入错误！"
msgstr "Invalid browser name/number!"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\browser.py:77
msgid "获取 Cookie 失败，Cookie 数据为空！"
msgstr "Failed to retrieve cookies: Cookie data is empty!"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\browser.py:79
msgid "获取 Cookie 失败，未找到 Cookie 数据！"
msgstr "Failed to retrieve cookies: No cookie data found!"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\capture.py:14
#, python-brace-format
msgid "网络异常：{error}"
msgstr "Network error: {error}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\capture.py:16
#, python-brace-format
msgid "响应内容异常：{error}"
msgstr "Unexpected response content: {error}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\capture.py:18
#, python-brace-format
msgid "权限异常：{error}"
msgstr "Permission error: {error}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:56
#, python-brace-format
msgid "{old_folder} 文件夹不存在，跳过处理"
msgstr "The {old_folder} folder does not exist, skipping processing"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:82
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:101
msgid "文件夹"
msgstr "folder"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:85
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:104
#, python-brace-format
msgid "文件夹 {old_folder} 重命名为 {new_folder}"
msgstr "The folder {old_folder} has been renamed to {new_folder}."

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:170
#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:184
msgid "文件"
msgstr "file"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:173
#, python-brace-format
msgid "文件 {old_file} 重命名为 {new_file}"
msgstr "The file {old_file} has been renamed to {new_file}."

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:191
#, python-brace-format
msgid "{type} {old}被占用，重命名失败: {error}"
msgstr "{type} {old} is occupied, rename failed: {error}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:198
#, python-brace-format
msgid "{type} {new}名称重复，重命名失败: {error}"
msgstr "{type} {new} name duplicated, rename failed: {error}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\mapping.py:205
#, python-brace-format
msgid "处理{type} {old}时发生预期之外的错误: {error}"
msgstr "An unexpected error occurred while processing {type} {old}: {error}"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\retry.py:9
#, python-brace-format
msgid "正在进行第 {count} 次重试"
msgstr "Retrying for the {count} time"

#: C:\Users\<USER>\PycharmProjects\KS-Downloader\source\tools\retry.py:24
msgid ""
"如需重新尝试处理该对象，请关闭所有正在访问该对象的窗口或程序，然后直接按下回"
"车键！\n"
"如需跳过处理该对象，请输入任意字符后按下回车键！"
msgstr ""
"To retry processing, close all windows or programs using this item, then "
"press Enter.\n"
"To skip, type any character and press Enter."
