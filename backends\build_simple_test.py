 #!/usr/bin/env python3
"""
简化版打包脚本 - 测试 Nuitka 基本功能
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def build_simple():
    """使用 Nuitka 进行简化打包"""
    print("开始简化版 Nuitka 打包...")
    
    # 最简化的 Nuitka 命令
    nuitka_cmd = [
        "..\\venv\\Scripts\\python.exe", "-m", "nuitka",
        "--standalone",
        "--show-progress",
        "--output-dir=out",
        "--windows-icon-from-ico=logo.ico",

        # 关键修复参数 - 完全禁用 DLL 依赖检测
        "--disable-dll-dependency-cache",
        "--assume-yes-for-downloads",
        "--mingw64",
        "--disable-dependency-walker",

        # 禁用可能导致运行时问题的优化
        "--plugin-disable=anti-bloat",
        "--plugin-disable=pylint-warnings",

        # 使用更保守的导入策略
        "--nofollow-imports",

        # 只跟踪必要的项目模块
        "--follow-import-to=services",
        "--follow-import-to=utils",
        "--follow-import-to=myUtils",

        # 基本系统模块
        "--follow-import-to=sqlite3",
        "--follow-import-to=logging",
        "--follow-import-to=json",

        # Flask 相关
        "--follow-import-to=flask",
        "--follow-import-to=requests",

        # 简化版主文件
        "main_simple.py"
    ]
    
    print("执行命令:", " ".join(nuitka_cmd))
    
    try:
        result = subprocess.run(nuitka_cmd, check=True, capture_output=True, text=True)
        print("Nuitka 打包成功!")
        return True
    except subprocess.CalledProcessError as e:
        print("Nuitka 打包失败!")
        print("错误输出:", e.stderr[-1000:] if e.stderr else "无错误输出")
        return False

def copy_files():
    """复制必要文件"""
    print("复制必要文件...")
    
    dist_dir = Path("out/main_simple.dist")
    if not dist_dir.exists():
        print("构建目录不存在!")
        return False
    
    # 复制基本文件
    files_to_copy = [
        "database.db",
        "conf.py",
        "ffmpeg_config.py"
    ]
    
    for file_path in files_to_copy:
        if os.path.exists(file_path):
            try:
                shutil.copy2(file_path, dist_dir)
                print(f"复制文件: {file_path}")
            except Exception as e:
                print(f"复制失败 {file_path}: {e}")
    
    # 复制目录
    dirs_to_copy = [
        "services",
        "utils",
        "myUtils"
    ]
    
    for dir_path in dirs_to_copy:
        if os.path.exists(dir_path):
            try:
                dest = dist_dir / dir_path
                if dest.exists():
                    shutil.rmtree(dest)
                shutil.copytree(dir_path, dest)
                print(f"复制目录: {dir_path}")
            except Exception as e:
                print(f"复制失败 {dir_path}: {e}")
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("简化版 Nuitka 打包测试")
    print("=" * 50)
    
    if not os.path.exists("main_simple.py"):
        print("错误: 未找到 main_simple.py")
        return
    
    if build_simple():
        print("✓ 打包成功")
        if copy_files():
            print("✓ 文件复制完成")
            print("📁 可执行文件: out/main_simple.dist/main_simple.exe")
        else:
            print("❌ 文件复制失败")
    else:
        print("❌ 打包失败")

if __name__ == "__main__":
    main()
