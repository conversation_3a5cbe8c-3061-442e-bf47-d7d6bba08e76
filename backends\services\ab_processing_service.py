"""
AB视频去重处理服务模块
负责处理AB视频融合去重的核心逻辑
"""

import os
import sys
import logging
import re
import uuid
import json
import subprocess
import traceback
from pathlib import Path

# 导入项目配置
try:
    from conf import BASE_DIR
except ImportError:
    # 如果在打包环境下，BASE_DIR 应该在主模块中定义
    BASE_DIR = Path(__file__).parent.parent

from ffmpeg_config import FFMPEG_BIN, FFPROBE_BIN


def process_ab_video_fusion(main_video_path, secondary_video_path):
    """
    内部AB去重处理函数，用于detailEnquire接口
    参数:
        main_video_path: 主视频完整路径（绝对路径或相对路径）
        secondary_video_path: 副视频绝对路径
    返回:
        dict: 包含处理结果的字典
    """
    try:
        logging.info(f"开始AB去重处理 - 主视频: {main_video_path}, 副视频: {secondary_video_path}")
        
        # 路径处理 - 支持完整路径和相对路径
        if os.path.isabs(main_video_path):
            # 如果是绝对路径，直接使用
            main_video_abs = str(Path(main_video_path).resolve())
        else:
            # 如果是相对路径，相对于videoFile目录
            main_video_abs = str(Path(BASE_DIR / "videoFile" / main_video_path).resolve())
        
        secondary_video_abs = str(Path(secondary_video_path).resolve())
        
        logging.info(f"处理后的主视频路径: {main_video_abs}")
        logging.info(f"处理后的副视频路径: {secondary_video_abs}")
        
        # 检查文件是否存在
        if not Path(main_video_abs).exists():
            return {"success": False, "error": f"主视频文件不存在: {main_video_abs}"}
        
        if not Path(secondary_video_abs).exists():
            return {"success": False, "error": f"副视频文件不存在: {secondary_video_abs}"}
        
        # 检查文件路径是否包含特殊字符，如果是则创建临时符号链接
        def safe_path(file_path):
            """确保文件路径对ffmpeg安全"""
            path_obj = Path(file_path)
            # 检查是否包含可能有问题的字符
            if any(char in str(path_obj) for char in ['【', '】', '！', '...', '#']):
                # 创建临时文件名
                temp_name = f"temp_{uuid.uuid1()}{path_obj.suffix}"
                temp_path = path_obj.parent / temp_name
                try:
                    # 创建硬链接（Windows）或符号链接
                    if os.name == 'nt':
                        # Windows 使用硬链接
                        os.link(str(path_obj), str(temp_path))
                    else:
                        # Unix 使用符号链接
                        os.symlink(str(path_obj), str(temp_path))
                    logging.info(f"创建临时链接: {temp_path}")
                    return str(temp_path), True
                except Exception as e:
                    logging.warning(f"创建临时链接失败: {e}，直接使用原路径")
                    return str(path_obj), False
            return str(path_obj), False
        
        # 处理文件路径
        safe_main_video, main_is_temp = safe_path(main_video_abs)
        safe_secondary_video, secondary_is_temp = safe_path(secondary_video_abs)
        
        # 生成输出文件名
        uuid_v1 = uuid.uuid1()
        filename = Path(main_video_abs).name
        final_filename = f"AB去重_{uuid_v1}_{filename}"
        output_path = str(Path(BASE_DIR / "videoFile" / final_filename))
        
        # ffmpeg 路径
        ffmpeg_path = FFMPEG_BIN
        ffprobe_path = FFPROBE_BIN
        
        if not os.path.exists(ffmpeg_path):
            return {"success": False, "error": f"ffmpeg 未找到: {ffmpeg_path}"}
        
        logging.info(f"输出文件路径: {output_path}")
        
        # 获取主视频时长
        duration = _get_video_duration(ffprobe_path, main_video_abs)
        
        # 检测视频流信息
        main_has_video, main_has_audio = _get_video_streams_info(ffprobe_path, safe_main_video)
        secondary_has_video, secondary_has_audio = _get_video_streams_info(ffprobe_path, safe_secondary_video)
        
        logging.info(f"主视频流: 视频={main_has_video}, 音频={main_has_audio}")
        logging.info(f"副视频流: 视频={secondary_has_video}, 音频={secondary_has_audio}")
        
        # 检测CUDA支持
        cuda = _check_cuda_support(ffmpeg_path)
        logging.info(f"CUDA 支持检测: {cuda}")
        
        # 选择编码器和预设
        video_codec = "h264_nvenc" if cuda else "libx264"
        preset = "p1" if cuda else "medium"
        
        # 根据音频流情况构建不同的filter_complex
        filter_complex, audio_mapping = _build_filter_complex(
            main_has_audio, secondary_has_audio
        )
        
        # 构建ffmpeg命令
        cmd = _build_ffmpeg_command(
            ffmpeg_path, safe_main_video, safe_secondary_video,
            filter_complex, audio_mapping, video_codec, preset, output_path
        )
        
        # 执行ffmpeg命令
        result = _execute_ffmpeg_command(cmd, duration, output_path)
        
        # 清理临时文件
        _cleanup_temp_files(safe_main_video, main_is_temp, safe_secondary_video, secondary_is_temp)
        
        return result
        
    except Exception as e:
        logging.error(f"AB去重处理异常: {e}")
        logging.error(f"异常堆栈: {traceback.format_exc()}")
        return {"success": False, "error": f"AB去重处理异常: {e}"}


def _get_video_duration(ffprobe_path, video_path):
    """获取视频时长"""
    try:
        cmd = [
            ffprobe_path, "-v", "error", "-show_entries",
            "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", video_path
        ]
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                              text=True, encoding='utf-8', errors='ignore')
        duration = float(result.stdout.strip())
        logging.info(f"获取到视频时长: {duration}秒")
        return duration
    except Exception as e:
        logging.warning(f"获取视频时长失败: {e}")
        return None


def _get_video_streams_info(ffprobe_path, video_path):
    """获取视频文件的流信息"""
    try:
        cmd = [
            ffprobe_path, "-v", "quiet", "-print_format", "json", 
            "-show_streams", video_path
        ]
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                              text=True, encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            streams_info = json.loads(result.stdout)
            streams = streams_info.get('streams', [])
            
            has_video = any(s.get('codec_type') == 'video' for s in streams)
            has_audio = any(s.get('codec_type') == 'audio' for s in streams)
            
            logging.info(f"视频流信息 - 有视频流: {has_video}, 有音频流: {has_audio}")
            return has_video, has_audio
        else:
            logging.warning(f"获取流信息失败: {result.stderr}")
            return True, True  # 默认假设都有
    except Exception as e:
        logging.warning(f"检测流信息异常: {e}")
        return True, True  # 默认假设都有


def _check_cuda_support(ffmpeg_path):
    """检测CUDA支持"""
    try:
        result = subprocess.run(
            [ffmpeg_path, "-hide_banner", "-encoders"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            encoding="utf-8",
            errors="replace"
        )
        return "h264_nvenc" in result.stdout
    except Exception:
        return False


def _build_filter_complex(main_has_audio, secondary_has_audio):
    """根据音频流情况构建filter_complex"""
    if main_has_audio and secondary_has_audio:
        # 两个视频都有音频流
        filter_complex = (
            "[0:v]fps=60,scale=1080:1920,setsar=1[v1p];"
            "[v1p]split=2[v1i][v1r];"
            "[v1i]trim=end_frame=2,setpts=PTS-STARTPTS[init_v1f];"
            "[v1r]trim=start_frame=2,setpts=PTS-STARTPTS[v1rest];"
            "[1:v]fps=60,scale=1080:1920,setsar=1[v2p];"
            "[v2p]trim=start_frame=2,setpts=PTS-STARTPTS[v2trim];"
            "[v1rest][v2trim]interleave[inter];"
            "[init_v1f][inter]concat=n=2:v=1:a=0[vout];"
            "[0:a]anull[a1];"
            "[1:a]volume=0.001[a2];"
            "[a1][a2]amix=inputs=2:duration=first:dropout_transition=0[aout]"
        )
        audio_mapping = ["-map", "[aout]"]
    elif main_has_audio and not secondary_has_audio:
        # 只有主视频有音频流
        filter_complex = (
            "[0:v]fps=60,scale=1080:1920,setsar=1[v1p];"
            "[v1p]split=2[v1i][v1r];"
            "[v1i]trim=end_frame=2,setpts=PTS-STARTPTS[init_v1f];"
            "[v1r]trim=start_frame=2,setpts=PTS-STARTPTS[v1rest];"
            "[1:v]fps=60,scale=1080:1920,setsar=1[v2p];"
            "[v2p]trim=start_frame=2,setpts=PTS-STARTPTS[v2trim];"
            "[v1rest][v2trim]interleave[inter];"
            "[init_v1f][inter]concat=n=2:v=1:a=0[vout];"
            "[0:a]anull[aout]"
        )
        audio_mapping = ["-map", "[aout]"]
    elif not main_has_audio and secondary_has_audio:
        # 只有副视频有音频流
        filter_complex = (
            "[0:v]fps=60,scale=1080:1920,setsar=1[v1p];"
            "[v1p]split=2[v1i][v1r];"
            "[v1i]trim=end_frame=2,setpts=PTS-STARTPTS[init_v1f];"
            "[v1r]trim=start_frame=2,setpts=PTS-STARTPTS[v1rest];"
            "[1:v]fps=60,scale=1080:1920,setsar=1[v2p];"
            "[v2p]trim=start_frame=2,setpts=PTS-STARTPTS[v2trim];"
            "[v1rest][v2trim]interleave[inter];"
            "[init_v1f][inter]concat=n=2:v=1:a=0[vout];"
            "[1:a]volume=0.001[aout]"
        )
        audio_mapping = ["-map", "[aout]"]
    else:
        # 两个视频都没有音频流
        filter_complex = (
            "[0:v]fps=60,scale=1080:1920,setsar=1[v1p];"
            "[v1p]split=2[v1i][v1r];"
            "[v1i]trim=end_frame=2,setpts=PTS-STARTPTS[init_v1f];"
            "[v1r]trim=start_frame=2,setpts=PTS-STARTPTS[v1rest];"
            "[1:v]fps=60,scale=1080:1920,setsar=1[v2p];"
            "[v2p]trim=start_frame=2,setpts=PTS-STARTPTS[v2trim];"
            "[v1rest][v2trim]interleave[inter];"
            "[init_v1f][inter]concat=n=2:v=1:a=0[vout]"
        )
        audio_mapping = []  # 没有音频映射
    
    return filter_complex, audio_mapping


def _build_ffmpeg_command(ffmpeg_path, safe_main_video, safe_secondary_video,
                         filter_complex, audio_mapping, video_codec, preset, output_path):
    """构建ffmpeg命令"""
    cmd = [
        ffmpeg_path,
        "-y",  # 覆盖输出文件
        "-i", safe_main_video,
        "-stream_loop", "-1", "-i", safe_secondary_video,
        "-filter_complex", filter_complex,
        "-map", "[vout]",
    ] + audio_mapping + [
        "-shortest",
        "-r", "120",
        "-c:v", video_codec,
    ]
    
    # 添加质量参数
    if video_codec == "libx264":
        cmd += ["-crf", "28"]
    elif video_codec == "h264_nvenc":
        cmd += ["-qp", "28"]
    
    # 添加音频编码器（如果有音频流）
    if audio_mapping:
        cmd += ["-c:a", "aac", "-b:a", "128k"]
    
    cmd += [
        "-preset", preset,
        output_path
    ]
    
    return cmd


def _execute_ffmpeg_command(cmd, duration, output_path):
    """执行ffmpeg命令"""
    try:
        # 执行ffmpeg命令
        process = subprocess.Popen(
            cmd,
            stderr=subprocess.PIPE,
            stdout=subprocess.PIPE,
            universal_newlines=True,
            encoding="utf-8",
            errors="replace"
        )
        
        # 收集所有stderr输出
        stderr_lines = []
        time_pattern = re.compile(r'time=(\d+):(\d+):(\d+\.\d+)')
        
        # 监控进度
        while True:
            line = process.stderr.readline()
            if not line:
                break
            
            stderr_lines.append(line.strip())
            
            # 记录ffmpeg输出（只记录重要信息）
            if line.strip() and ('error' in line.lower() or 'warning' in line.lower() or 'failed' in line.lower()):
                logging.warning(f"ffmpeg stderr: {line.strip()}")
            
            # 解析进度
            if duration:
                match = time_pattern.search(line)
                if match:
                    h, m, s = map(float, match.groups())
                    current_time = h * 3600 + m * 60 + s
                    percent = min(current_time / duration * 100, 100)
                    if percent > 0:
                        logging.info(f"AB去重进度: {percent:.1f}%")
        
        # 等待进程完成
        process.wait()
        
        # 获取完整的stderr输出
        full_stderr = '\n'.join(stderr_lines)
        
        logging.info(f"ffmpeg 执行完成，返回码: {process.returncode}")
        
        if process.returncode == 0:
            # 检查输出文件是否存在
            if Path(output_path).exists():
                return _process_successful_output(output_path)
            else:
                return {"success": False, "error": "输出文件未生成"}
        else:
            return _process_ffmpeg_error(process.returncode, stderr_lines, full_stderr)
            
    except Exception as e:
        logging.error(f"执行ffmpeg命令异常: {e}")
        return {"success": False, "error": f"执行ffmpeg命令异常: {e}"}


def _process_successful_output(output_path):
    """处理成功的输出结果"""
    # 生成封面图
    uuid_v1 = uuid.uuid1()
    cover_name = f"{uuid_v1}_cover.jpg"
    cover_path = Path(BASE_DIR / "videoFile" / cover_name)
    
    try:
        subprocess.run([
            FFMPEG_BIN,
            "-y",
            "-i", output_path,
            "-vf", "select=eq(n\\,0)",
            "-q:v", "2",
            str(cover_path)
        ], check=True, encoding='utf-8', errors='ignore')
        logging.info(f"封面图生成成功: {cover_path}")
    except Exception as cover_e:
        logging.warning(f"封面图生成失败: {cover_e}")
        cover_path = None
    
    # 计算文件大小
    file_size = os.path.getsize(output_path)
    file_size_mb = round(file_size / (1024 * 1024), 2)
    
    logging.info(f"AB去重处理成功:")
    logging.info(f"  - 输出文件: {output_path}")
    logging.info(f"  - 文件大小: {file_size_mb} MB")
    logging.info(f"  - 封面图: {cover_path}")
    
    return {
        "success": True,
        "output": output_path,
        "outputPath": output_path,  # 兼容原接口
        "filename": Path(output_path).name,
        "filepath": Path(output_path).name,
        "cover_image": str(cover_path) if cover_path else None,
        "filesize": file_size_mb
    }


def _process_ffmpeg_error(return_code, stderr_lines, full_stderr):
    """处理ffmpeg错误"""
    # 记录详细的错误信息
    logging.error(f"ffmpeg 详细错误输出:")
    for line in stderr_lines[-20:]:  # 只记录最后20行错误
        if line.strip():
            logging.error(f"  {line}")
    
    # 分析常见错误
    error_analysis = ""
    if "Invalid argument" in full_stderr:
        error_analysis = "参数错误，可能是文件路径包含特殊字符"
    elif "No such file or directory" in full_stderr:
        error_analysis = "文件不存在或路径错误"
    elif "Permission denied" in full_stderr:
        error_analysis = "权限不足，无法访问文件"
    elif "Encoder" in full_stderr and "not found" in full_stderr:
        error_analysis = "编码器不可用"
    
    error_msg = f"ffmpeg 执行失败 (返回码: {return_code})"
    if error_analysis:
        error_msg += f" - {error_analysis}"
    
    return {
        "success": False, 
        "error": error_msg, 
        "stderr": full_stderr[-1000:] if full_stderr else ""
    }


def _cleanup_temp_files(safe_main_video, main_is_temp, safe_secondary_video, secondary_is_temp):
    """清理临时文件"""
    if main_is_temp and Path(safe_main_video).exists():
        try:
            Path(safe_main_video).unlink()
            logging.info(f"已清理临时主视频文件: {safe_main_video}")
        except Exception as e:
            logging.warning(f"清理临时主视频文件失败: {e}")
    
    if secondary_is_temp and Path(safe_secondary_video).exists():
        try:
            Path(safe_secondary_video).unlink()
            logging.info(f"已清理临时副视频文件: {safe_secondary_video}")
        except Exception as e:
            logging.warning(f"清理临时副视频文件失败: {e}")
