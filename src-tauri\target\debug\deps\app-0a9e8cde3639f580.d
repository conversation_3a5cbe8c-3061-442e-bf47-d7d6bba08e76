D:\peoject\test\src-tauri\target\debug\deps\libapp-0a9e8cde3639f580.rmeta: src\main.rs src\commands\mod.rs src\commands\machine.rs src\commands\utils.rs D:\peoject\test\src-tauri\target\debug\build\app-2b6ae1f535ffe04c\out/089bb2bd964870c55028de0839117fa9b433e98a5447a0c8b233263573c2dbf3

D:\peoject\test\src-tauri\target\debug\deps\app-0a9e8cde3639f580.d: src\main.rs src\commands\mod.rs src\commands\machine.rs src\commands\utils.rs D:\peoject\test\src-tauri\target\debug\build\app-2b6ae1f535ffe04c\out/089bb2bd964870c55028de0839117fa9b433e98a5447a0c8b233263573c2dbf3

src\main.rs:
src\commands\mod.rs:
src\commands\machine.rs:
src\commands\utils.rs:
D:\peoject\test\src-tauri\target\debug\build\app-2b6ae1f535ffe04c\out/089bb2bd964870c55028de0839117fa9b433e98a5447a0c8b233263573c2dbf3:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri app that uses sidecars to load a Python server.
# env-dep:CARGO_PKG_NAME=app
# env-dep:CARGO_PKG_VERSION=0.1.0
# env-dep:OUT_DIR=D:\\peoject\\test\\src-tauri\\target\\debug\\build\\app-2b6ae1f535ffe04c\\out
