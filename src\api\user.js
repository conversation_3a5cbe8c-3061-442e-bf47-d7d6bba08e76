import { http } from '@/utils/request'

// 用户相关API
export const userApi = {
  // 获取用户信息
  getUserInfo(id) {
    return http.get(`/user/${id}`)
  },
  
  // 获取用户列表
  getUserList(params) {
    return http.get('/user/list', params)
  },
  
  // 创建用户
  createUser(data) {
    return http.post('/user', data)
  },
  
  // 更新用户信息
  updateUser(id, data) {
    return http.put(`/user/${id}`, data)
  },
  
  // 删除用户
  deleteUser(id) {
    return http.delete(`/user/${id}`)
  },
  
  // 注意：登录、注册、登出、刷新token等认证相关功能
  // 已迁移到独立的认证服务器，请使用 @/api/auth.js 中的对应方法
}