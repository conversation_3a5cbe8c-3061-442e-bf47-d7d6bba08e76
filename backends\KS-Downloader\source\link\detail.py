from typing import TYPE_CHECKING
from httpx import get
from ..tools import capture_error_request, retry_request, wait
from ..variable import TIMEOUT

if TYPE_CHECKING:
    from ..manager import Manager


class DetailPage:
    def __init__(self, manager: "Manager"):
        self.client = manager.client
        self.headers = manager.pc_headers
        self.console = manager.console
        self.retry = manager.max_retry

    async def run(self, url: str, proxy: str = "", cookie: str = "") -> str:
        return await self.request_url(url, proxy, cookie)

    @retry_request
    @capture_error_request
    async def request_url(
        self,
        url: str,
        proxy: str = "",
        cookie: str = "",
    ) -> str:
        headers = self.headers.copy()
        # 设置移动端User-Agent，模拟iPhone Safari访问
        headers["User-Agent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"
        
        # 处理 Cookie：如果传入了 cookie 参数，使用传入的 cookie
        if cookie:
            headers["Cookie"] = cookie
            # 打印调试信息确认 cookie 已设置
            print(f"[DEBUG] 设置Cookie长度: {len(cookie)}")
            print(f"[DEBUG] Cookie前100字符: {cookie[:100]}...")
        else:
            print("[DEBUG] 未传入Cookie参数")
        if proxy:
            response = get(
                url,
                headers=headers,
                proxy=proxy,
                follow_redirects=True,
                verify=False,
                timeout=TIMEOUT,
            )
        else:
            response = await self.client.get(
                url,
                headers=headers,
            )
        await wait()
        response.raise_for_status()
        return response.text
