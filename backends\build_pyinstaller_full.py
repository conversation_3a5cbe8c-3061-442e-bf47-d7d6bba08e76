#!/usr/bin/env python3 最终打包
"""
使用 PyInstaller 打包完整版本
包含 KS-Downloader 和所有功能模块
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def generate_ks_downloader_imports():
    """生成 KS-Downloader 的基础依赖导入（不包含 KS-Downloader 本身）"""
    imports = []

    # 只添加 KS-Downloader 的外部依赖，不添加 KS-Downloader 本身的模块
    # 这些依赖在 KS-Downloader 的代码中被使用
    external_deps = [
        "uvicorn",
        "fastapi",
        "fastapi.responses",
        "aiofiles",
        "aiosqlite",
        "emoji",
        "lxml",
        "pyyaml",
        "rich",
        "rookiepy",
        "requests",
        "urllib3",
        "asyncio",
        "pathlib",
        "json",
        "re",
        "os",
        "sys",
        "logging",
        "time",
        "datetime",
        "sqlite3",
        "base64",
        "hashlib",
        "uuid",
        "traceback"
    ]

    for dep in external_deps:
        imports.append(f"--hidden-import={dep}")

    print(f"✓ 生成了 {len(imports)} 个 KS-Downloader 依赖导入")
    return imports

def install_pyinstaller():
    """安装 PyInstaller"""
    print("检查 PyInstaller...")
    
    try:
        result = subprocess.run([
            "..\\venv\\Scripts\\python.exe", "-c", "import PyInstaller; print(PyInstaller.__version__)"
        ], check=True, capture_output=True, text=True)
        print(f"✓ PyInstaller 已安装: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError:
        print("安装 PyInstaller...")
        try:
            result = subprocess.run([
                "..\\venv\\Scripts\\python.exe", "-m", "pip", "install", "pyinstaller"
            ], check=True, capture_output=True, text=True)
            print("✓ PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller 安装失败: {e}")
            return False

def build_with_pyinstaller():
    """使用 PyInstaller 进行完整打包"""
    print("开始 PyInstaller 完整打包...")

    # 生成 KS-Downloader 隐藏导入
    ks_imports = generate_ks_downloader_imports()

    # PyInstaller 命令
    pyinstaller_cmd = [
        "..\\venv\\Scripts\\python.exe", "-m", "PyInstaller",
        "--onedir",  # 创建目录而不是单文件
        "--console",  # 保留控制台以便调试
        "--name=xiaochao-media-system",
        "--icon=logo.ico",
        "--distpath=out_pyinstaller_full",
        "--workpath=build_pyinstaller_full",
        "--specpath=.",
        "-y",  # 自动覆盖输出目录
        
        # 添加数据文件
        "--add-data=database.db;.",
        "--add-data=conf.py;.",
        "--add-data=ffmpeg_config.py;.",
        "--add-data=logo.ico;.",
        "--add-data=KS-Downloader;KS-Downloader",  # 将整个 KS-Downloader 目录作为数据文件
        
        # 添加非 Python 数据目录
        "--add-data=cookiesFile;cookiesFile",
        "--add-data=videoFile;videoFile",
        "--add-data=ffmpeg;ffmpeg",
        
        # 隐藏导入 - 项目模块
        "--hidden-import=services",
        "--hidden-import=services.detail_enquire_service",
        "--hidden-import=services.file_service",
        "--hidden-import=services.account_service",
        "--hidden-import=services.stats_service",
        "--hidden-import=services.log_service",
        "--hidden-import=services.ab_processing_service",
        "--hidden-import=services.system_service",

        # utils 模块
        "--hidden-import=utils",
        "--hidden-import=utils.base_social_media",
        "--hidden-import=utils.constant",
        "--hidden-import=utils.files_times",
        "--hidden-import=utils.ks_signature",
        "--hidden-import=utils.log",
        "--hidden-import=utils.network",

        # myUtils 模块
        "--hidden-import=myUtils",
        "--hidden-import=myUtils.auth",
        "--hidden-import=myUtils.login",
        "--hidden-import=myUtils.postVideo",

        # uploader 模块
        "--hidden-import=uploader",
        "--hidden-import=uploader.baijiahao_uploader",
        "--hidden-import=uploader.baijiahao_uploader.main",
        "--hidden-import=uploader.bilibili_uploader",
        "--hidden-import=uploader.bilibili_uploader.main",
        "--hidden-import=uploader.douyin_uploader",
        "--hidden-import=uploader.douyin_uploader.main",
        "--hidden-import=uploader.ks_uploader",
        "--hidden-import=uploader.ks_uploader.main",
        "--hidden-import=uploader.tencent_uploader",
        "--hidden-import=uploader.tencent_uploader.main",
        "--hidden-import=uploader.tk_uploader",
        "--hidden-import=uploader.tk_uploader.main",
        "--hidden-import=uploader.tk_uploader.main_chrome",
        "--hidden-import=uploader.tk_uploader.tk_config",
        "--hidden-import=uploader.xhs_uploader",
        "--hidden-import=uploader.xhs_uploader.main",
        "--hidden-import=uploader.xhs_uploader.xhs_login_qrcode",
        "--hidden-import=uploader.xiaohongshu_uploader",
        "--hidden-import=uploader.xiaohongshu_uploader.main",
        
        # 隐藏导入 - 系统模块
        "--hidden-import=sqlite3",
        "--hidden-import=logging",
        "--hidden-import=json",
        "--hidden-import=pathlib",
        "--hidden-import=asyncio",
        "--hidden-import=subprocess",
        "--hidden-import=threading",
        "--hidden-import=multiprocessing",
        
        # 隐藏导入 - Flask 相关
        "--hidden-import=flask",
        "--hidden-import=flask_cors",
        "--hidden-import=requests",
        "--hidden-import=urllib3",
        
        # 隐藏导入 - KS-Downloader 相关
        "--hidden-import=trio",
        "--hidden-import=httpx",
        "--hidden-import=pydantic",
        "--hidden-import=attrs",
        "--hidden-import=uvicorn",
        "--hidden-import=uvicorn.main",
        "--hidden-import=uvicorn.server",
        "--hidden-import=uvicorn.config",
        "--hidden-import=fastapi",
        "--hidden-import=fastapi.applications",
        "--hidden-import=fastapi.routing",
        "--hidden-import=aiofiles",
        "--hidden-import=aiosqlite",
        "--hidden-import=emoji",
        "--hidden-import=lxml",
        "--hidden-import=pyyaml",
        "--hidden-import=rich",
        "--hidden-import=rookiepy",
        
        # 隐藏导入 - Playwright 相关
        "--hidden-import=playwright",
        "--hidden-import=playwright.sync_api",
        "--hidden-import=playwright.async_api",
        
        # 排除一些不需要的模块以减小体积
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        "--exclude-module=pandas",

        # 主文件
        "main.py"
    ]

    # 添加动态生成的 KS-Downloader 隐藏导入
    pyinstaller_cmd.extend(ks_imports)
    
    print("执行命令:", " ".join(pyinstaller_cmd[:10]) + "... (命令太长，已截断)")
    
    try:
        result = subprocess.run(pyinstaller_cmd, check=True, capture_output=True, text=True)
        print("PyInstaller 打包成功!")
        return True
    except subprocess.CalledProcessError as e:
        print("PyInstaller 打包失败!")
        print("错误输出:", e.stderr[-1500:] if e.stderr else "无错误输出")
        return False

def post_build_setup():
    """构建后设置"""
    print("执行构建后设置...")

    dist_dir = Path("out_pyinstaller_full/xiaochao-media-system")
    if not dist_dir.exists():
        print("❌ 打包目录不存在")
        return False

    # 修复数据库文件复制问题
    fix_database_file(dist_dir)

    # 复制 KS-Downloader（作为数据文件）
    copy_ks_downloader_data(dist_dir)

    # 复制 Playwright 浏览器（如果存在）
    copy_playwright_browsers(dist_dir)

    # 复制必要目录到根目录
    copy_directories_to_root(dist_dir)

    # 创建配置文件
    create_config_files(dist_dir)

    # 创建启动脚本
    create_launch_scripts(dist_dir)

    return True

def fix_database_file(dist_dir):
    """修复数据库文件复制问题"""
    print("修复数据库文件...")

    source_db = Path("database.db")
    target_db = dist_dir / "database.db"

    if source_db.exists():
        if target_db.exists() and target_db.stat().st_size == 0:
            # 如果目标数据库文件存在但为空，删除它
            target_db.unlink()
            print(f"✓ 删除空的数据库文件: {target_db}")

        if not target_db.exists() or target_db.stat().st_size == 0:
            # 复制数据库文件
            import shutil
            shutil.copy2(source_db, target_db)
            print(f"✓ 复制数据库文件: {source_db} -> {target_db}")
            print(f"✓ 数据库文件大小: {target_db.stat().st_size} bytes")
        else:
            print(f"✓ 数据库文件已存在: {target_db} ({target_db.stat().st_size} bytes)")
    else:
        print(f"❌ 源数据库文件不存在: {source_db}")

def copy_ks_downloader_data(dist_dir):
    """复制 KS-Downloader 的数据文件（非 Python 文件）"""
    print("复制 KS-Downloader 数据文件...")

    source_ks = Path("KS-Downloader")
    target_ks = dist_dir / "_internal" / "KS-Downloader"

    if not source_ks.exists():
        print(f"❌ KS-Downloader 源目录不存在: {source_ks}")
        return

    # 创建目标目录
    target_ks.mkdir(exist_ok=True)

    # 复制非 Python 文件
    for item in source_ks.rglob("*"):
        if item.is_file():
            # 跳过 Python 文件（这些已经被编译了）
            if item.suffix in ['.py', '.pyc']:
                continue
            # 跳过 __pycache__ 目录
            if '__pycache__' in item.parts:
                continue

            # 计算相对路径
            rel_path = item.relative_to(source_ks)
            target_file = target_ks / rel_path

            # 创建目标目录
            target_file.parent.mkdir(parents=True, exist_ok=True)

            # 复制文件
            shutil.copy2(item, target_file)

    print(f"✓ 复制 KS-Downloader 数据文件到: {target_ks}")

def copy_playwright_browsers(dist_dir):
    """复制 Playwright 浏览器"""
    print("处理 Playwright 浏览器...")
    
    # 检查是否有 Playwright 浏览器
    playwright_paths = [
        Path("..") / "venv" / "Lib" / "site-packages" / "playwright" / "driver",
        Path.home() / "AppData" / "Local" / "ms-playwright"
    ]
    
    for playwright_path in playwright_paths:
        if playwright_path.exists():
            try:
                dest = dist_dir / "playwright"
                if dest.exists():
                    shutil.rmtree(dest)
                shutil.copytree(playwright_path, dest)
                print(f"✓ 复制 Playwright: {playwright_path}")
                break
            except Exception as e:
                print(f"⚠ Playwright 复制失败: {e}")

def copy_directories_to_root(dist_dir):
    """复制必要目录从 _internal 到根目录"""
    print("复制必要目录到根目录...")

    internal_dir = dist_dir / "_internal"

    # 需要复制到根目录的目录列表
    directories_to_copy = ["cookiesFile"]

    for dir_name in directories_to_copy:
        source_dir = internal_dir / dir_name
        target_dir = dist_dir / dir_name

        if source_dir.exists():
            try:
                # 如果目标目录已存在，先删除
                if target_dir.exists():
                    shutil.rmtree(target_dir)

                # 复制整个目录
                shutil.copytree(source_dir, target_dir)
                print(f"✓ 复制目录: {dir_name} -> 根目录")

            except Exception as e:
                print(f"⚠ 复制目录 {dir_name} 失败: {e}")
        else:
            print(f"⚠ 源目录不存在: {source_dir}")

def create_config_files(dist_dir):
    """创建配置文件"""
    print("创建配置文件...")
    
    # 创建运行时配置
    config_content = """# 运行时配置
# 这个文件在打包后的程序中使用

import os
import sys

# 获取程序运行目录
if getattr(sys, 'frozen', False):
    # 打包后的程序
    BASE_DIR = os.path.dirname(sys.executable)
else:
    # 开发环境
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Playwright 配置
PLAYWRIGHT_PATH = os.path.join(BASE_DIR, "playwright")
if os.path.exists(PLAYWRIGHT_PATH):
    os.environ["PLAYWRIGHT_BROWSERS_PATH"] = PLAYWRIGHT_PATH

print(f"运行时配置加载完成，BASE_DIR: {BASE_DIR}")
"""
    
    config_path = dist_dir / "runtime_config.py"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✓ 创建运行时配置: {config_path}")

def create_launch_scripts(dist_dir):
    """创建启动脚本"""
    print("创建启动脚本...")
    
    # 主启动脚本
    main_script = """@echo off
chcp 65001 >nul
echo ========================================
echo 小超媒体管理系统 - 完整版
echo ========================================
echo.

REM 切换到程序目录
cd /d "%~dp0"

REM 设置环境变量
set PYTHONPATH=%~dp0
set PYTHONIOENCODING=utf-8

echo 当前目录: %CD%
echo 启动主程序...
echo 访问地址: http://localhost:5000
echo 按 Ctrl+C 停止服务器
echo.

xiaochao-media-system.exe

echo.
echo ========================================
if errorlevel 1 (
    echo 程序异常退出，错误代码: %errorlevel%
    echo.
    echo 可能的解决方案:
    echo 1. 检查端口 5000 是否被占用
    echo 2. 确保所有依赖文件完整
    echo 3. 查看错误日志
) else (
    echo 程序正常退出
)
echo ========================================
pause
"""
    
    # 调试启动脚本
    debug_script = """@echo off
chcp 65001 >nul
echo ========================================
echo 小超媒体管理系统 - 调试模式
echo ========================================
echo.

REM 切换到程序目录
cd /d "%~dp0"

REM 设置调试环境变量
set PYTHONPATH=%~dp0
set PYTHONIOENCODING=utf-8
set DEBUG=1

echo 当前目录: %CD%
echo 调试模式启动...
echo 将显示详细的错误信息
echo.

xiaochao-media-system.exe

echo.
echo ========================================
echo 调试模式结束
echo ========================================
pause
"""
    
    # 写入脚本文件
    main_script_path = dist_dir / "启动.bat"
    debug_script_path = dist_dir / "调试启动.bat"
    
    with open(main_script_path, 'w', encoding='gbk') as f:
        f.write(main_script)
    
    with open(debug_script_path, 'w', encoding='gbk') as f:
        f.write(debug_script)
    
    print(f"✓ 创建启动脚本: {main_script_path}")
    print(f"✓ 创建调试脚本: {debug_script_path}")

def main():
    """主函数"""
    print("=" * 70)
    print("小超媒体管理系统 - PyInstaller 完整版打包工具")
    print("=" * 70)
    
    if not os.path.exists("main.py"):
        print("错误: 未找到 main.py")
        return
    
    if not os.path.exists("KS-Downloader"):
        print("错误: 未找到 KS-Downloader 目录")
        return
    
    # 执行步骤
    steps = [
        ("安装 PyInstaller", install_pyinstaller),
        ("PyInstaller 打包", build_with_pyinstaller),
        ("构建后设置", post_build_setup),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*25} {step_name} {'='*25}")
        if not step_func():
            print(f"❌ 步骤失败: {step_name}")
            return
        print(f"✓ {step_name} 完成")
    
    print("\n" + "="*70)
    print("🎉 PyInstaller 完整版打包完成!")
    print("📁 程序目录: out_pyinstaller_full/xiaochao-media-system/")
    print("🚀 主启动脚本: out_pyinstaller_full/xiaochao-media-system/启动.bat")
    print("🔧 调试启动脚本: out_pyinstaller_full/xiaochao-media-system/调试启动.bat")
    print()
    print("💡 测试步骤:")
    print("   1. 运行启动脚本")
    print("   2. 访问 http://localhost:5000")
    print("   3. 测试视频下载和 AB 去重功能")
    print("   4. 如有问题，使用调试启动脚本查看详细错误")
    print("=" * 70)

if __name__ == "__main__":
    main()
