import sqlite3
import os
from pathlib import Path

def main():
    # 数据库文件路径（如果不存在会自动创建）
    BASE_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
    db_path = BASE_DIR / "database.db"
    print("使用的数据库路径:", db_path)
    # 连接到SQLite数据库（如果文件不存在则会自动创建）
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 创建账号记录表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS user_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type INTEGER NOT NULL,
        filePath TEXT NOT NULL,  -- 存储文件路径
        userName TEXT NOT NULL,
        status INTEGER DEFAULT 0,
        avatar TEXT,  -- 允许为空
        auto_publish INTEGER DEFAULT 1  -- 自动发布开关，默认启用
    )
    ''')

    # 创建文件记录表
    cursor.execute('''CREATE TABLE IF NOT EXISTS file_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT, -- 唯一标识每条记录
        filename TEXT NOT NULL,               -- 文件名
        filesize REAL,                     -- 文件大小（单位：MB）
        upload_time DATETIME DEFAULT CURRENT_TIMESTAMP, -- 上传时间，默认当前时间
        file_path TEXT,                        -- 文件路径
        cover_image TEXT                      -- 封面图路径或URL
    )
    ''')

    # 创建KS-Downloader的detail表
    cursor.execute('''CREATE TABLE IF NOT EXISTS detail (
        collection_time TEXT,
        photoType TEXT,
        authorID TEXT,
        name TEXT,
        userSex TEXT,
        detailID TEXT PRIMARY KEY,
        caption TEXT,
        coverUrl TEXT,
        duration TEXT,
        realLikeCount INTEGER,
        shareCount INTEGER,
        commentCount INTEGER,
        timestamp TEXT,
        viewCount TEXT,
        download TEXT,
        shopping_data TEXT
    )
    ''')

    # 提交更改
    conn.commit()
    print("表创建成功")
    # 关闭连接
    conn.close()

if __name__ == '__main__':
    main()