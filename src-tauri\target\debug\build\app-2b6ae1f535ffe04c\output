cargo:rerun-if-changed=src/test/ks_sig3.jar
cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rerun-if-changed=tauri.conf.json
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=obrew
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_xiaochaomedia
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=D:\peoject\test\src-tauri\target\debug\build\app-2b6ae1f535ffe04c\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=D:\peoject\test\src-tauri\target\debug\build\app-2b6ae1f535ffe04c\out\resource.lib
