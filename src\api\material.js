import { http } from "@/utils/request";
import { httpAxios } from "@/utils/axiosRequest";

// 素材管理API
export const materialApi = {
  // 获取所有素材
  getAllMaterials: () => {
    return http.get("/getFiles");
  },

  // 上传素材
  uploadMaterial: (formData) => {
    // 使用http.upload方法，它已经配置了正确的Content-Type
    return httpAxios.upload("/uploadSave", formData);
  },

  // 删除素材
  deleteMaterial: (id) => {
    return http.get(`/deleteFile?id=${id}`);
  },

  // 下载素材
  downloadMaterial: (filePath) => {
    return `${
      import.meta.env.VITE_API_BASE_URL || "http://localhost:5409"
    }/download/${filePath}`;
  },

  // 获取素材预览URL
  getMaterialPreviewUrl: (filename) => {
    return `${
      import.meta.env.VITE_API_BASE_URL || "http://localhost:5409"
    }/getFile?filename=${filename}`;
  },

  // ab帧
  changeMaterialAB: (data) => {
    return http.post("/changeAB", data);
  },

  // ab帧去重
  changeABBySecondaryPath: (data) => {
    return http.post("/changeABBySecondaryPath", data);
  },

  // ab帧文件夹去重
  changeABByFolder: (data) => {
    return http.post("/changeABByFolder", data);
  },

  // 批量删除素材
  deleteMaterials: (ids) => {
    return http.post("/deleteFiles", { ids });
  },
  // 获取服务路径
  getBasePath: () => {
    return http.get("/getCwd");
  },
  // openFileInExplorer
  openFolder: (data) => {
    return http.post("/openFileInExplorer", data );
  },

  // openFileInExplorer
  updateMaterialCoverImage: (data) => {
    return http.post("/updateMaterialCoverImage", data );
  },

  // 快手作品详情查询
  detailEnquire: (data) => {
    return http.post("/detailEnquire", data);
  },

  // 处理视频链接接口 (新接口)
  processVideoLinks: (data) => {
    return http.post("/processVideoLinks", data);
  },

  // 发布视频接口 (新接口)
  publishVideos: (data) => {
    return http.post("/publishVideos", data);
  },

  // 添加购物车 http://localhost:5409/addGoodsToShowcase
  addGoodsToShowcase: (data) => {
    return http.post("/addGoodsToShowcase", data);
  },

  // 测试封面生成
  testCoverGeneration: (data) => {
    return http.post("/testCoverGeneration", data);
  },
};
