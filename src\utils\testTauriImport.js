// 测试 Tauri 导入是否正常工作
import { invoke } from '@tauri-apps/api/core';

/**
 * 测试 Tauri invoke 函数是否可用
 */
export async function testTauriInvoke() {
  try {
    console.log("测试 Tauri invoke 函数...");
    
    // 测试一个简单的 Rust 函数调用
    const result = await invoke('sig_py', { 
      queryParams: { test: "value" }, 
      dataParams: { test2: "value2" } 
    });
    
    console.log("<PERSON><PERSON> invoke 测试成功:", result);
    return { success: true, result };
  } catch (error) {
    console.error("<PERSON><PERSON> invoke 测试失败:", error);
    return { success: false, error: error.message };
  }
}

/**
 * 检查所有 Rust utils 方法是否可用
 */
export async function checkAllRustMethods() {
  const methods = [
    'sig_py',
    'token_py', 
    'extract_signature',
    'get_sig3',
    'check_java_available',
    'get_jar_path',
    'batch_get_sig3',
    'validate_signature'
  ];
  
  const results = {};
  
  for (const method of methods) {
    try {
      console.log(`检查方法: ${method}`);
      
      // 根据不同方法提供不同的测试参数
      let testParams;
      switch (method) {
        case 'sig_py':
          testParams = { queryParams: { test: "1" }, dataParams: { test: "2" } };
          break;
        case 'token_py':
          testParams = { sig: "test_sig", clientSalt: "test_salt" };
          break;
        case 'extract_signature':
          testParams = { text: "##test##" };
          break;
        case 'get_sig3':
          testParams = { param: "test_param" };
          break;
        case 'batch_get_sig3':
          testParams = { params: ["test1", "test2"] };
          break;
        case 'validate_signature':
          testParams = { signature: "abcdef1234567890abcdef1234567890" };
          break;
        default:
          testParams = {};
      }
      
      const result = await invoke(method, testParams);
      results[method] = { success: true, result };
      console.log(`✓ ${method} 可用`);
    } catch (error) {
      results[method] = { success: false, error: error.message };
      console.log(`✗ ${method} 失败:`, error.message);
    }
  }
  
  return results;
}

export default {
  testTauriInvoke,
  checkAllRustMethods
};
