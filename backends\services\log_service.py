"""
日志服务模块
处理所有与日志相关的业务逻辑
"""
import logging
import time
from flask import jsonify, Response, render_template, request


class LogService:
    """日志服务类"""
    
    def __init__(self, log_cache, log_lock):
        """
        初始化日志服务
        
        Args:
            log_cache: 日志缓存队列
            log_lock: 日志锁对象
        """
        self.log_cache = log_cache
        self.log_lock = log_lock
    
    def test_logs(self):
        """测试日志功能"""
        try:
            # 测试标准 logging
            logging.info("这是一条测试日志 - 标准 logging")
            logging.warning("这是一条警告日志 - 标准 logging")
            logging.error("这是一条错误日志 - 标准 logging")

            # 测试 loguru
            try:
                from utils.log import kuaishou_logger
                kuaishou_logger.info("这是一条测试日志 - kuaishou_logger")
                kuaishou_logger.success("这是一条成功日志 - kuaishou_logger")
                kuaishou_logger.warning("这是一条警告日志 - kuaishou_logger")
                kuaishou_logger.error("这是一条错误日志 - kuaishou_logger")

                # 检查当前缓存状态
                with self.log_lock:
                    cache_size = len(self.log_cache)
                    recent_logs = list(self.log_cache)[-5:] if self.log_cache else []

                return {
                    "success": True,
                    "code": 200,
                    "msg": "测试日志已发送",
                    "data": {
                        "cache_size": cache_size,
                        "recent_logs": [{"level": log.get("level"), "message": log.get("message")} for log in recent_logs]
                    }
                }
            except Exception as e:
                logging.error(f"测试日志失败: {e}")
                return {
                    "success": False,
                    "code": 500,
                    "msg": f"测试失败: {str(e)}",
                    "data": None
                }
        except Exception as e:
            logging.error(f"测试日志功能失败: {e}")
            return {
                "success": False,
                "code": 500,
                "msg": f"测试日志功能失败: {str(e)}",
                "data": None
            }
    
    def get_logs(self, limit=100, level_filter='', since=''):
        """
        获取服务器日志
        
        Args:
            limit: 返回的日志条数，默认100，最大1000
            level_filter: 日志级别过滤 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            since: 获取指定时间戳之后的日志
            
        Returns:
            dict: 包含日志数据的响应
        """
        try:
            # 参数处理
            limit = min(int(limit), 1000)
            level_filter = level_filter.upper()

            # 转换时间戳
            since_timestamp = None
            if since:
                try:
                    since_timestamp = float(since)
                except ValueError:
                    return {
                        "success": False,
                        "code": 400,
                        "msg": "无效的时间戳格式",
                        "data": None
                    }

            # 获取日志数据
            with self.log_lock:
                logs = list(self.log_cache)

            # 过滤日志
            filtered_logs = []
            for log_entry in logs:  # 按时间顺序，最旧的在前
                # 时间过滤
                if since_timestamp and log_entry['time'] <= since_timestamp:
                    continue

                # 级别过滤
                if level_filter and log_entry['level'] != level_filter:
                    continue

                filtered_logs.append(log_entry)

            # 取最后的 limit 条记录，保持时间顺序（最新的在最后）
            if len(filtered_logs) > limit:
                filtered_logs = filtered_logs[-limit:]

            return {
                "success": True,
                "code": 200,
                "msg": "获取日志成功",
                "data": {
                    "logs": filtered_logs,
                    "total": len(filtered_logs),
                    "cache_size": len(logs)
                }
            }

        except Exception as e:
            logging.error(f"获取日志失败: {e}")
            return {
                "success": False,
                "code": 500,
                "msg": f"获取日志失败: {str(e)}",
                "data": None
            }
    
    def clear_logs(self):
        """清空日志缓存"""
        try:
            with self.log_lock:
                self.log_cache.clear()

            logging.info("日志缓存已清空")
            return {
                "success": True,
                "code": 200,
                "msg": "日志缓存已清空",
                "data": None
            }

        except Exception as e:
            logging.error(f"清空日志缓存失败: {e}")
            return {
                "success": False,
                "code": 500,
                "msg": f"清空日志缓存失败: {str(e)}",
                "data": None
            }
    
    def get_logs_page(self):
        """获取日志查看页面"""
        try:
            return render_template('logs.html')
        except Exception as e:
            logging.error(f"获取日志页面失败: {e}")
            return f"日志页面加载失败: {str(e)}", 500
    
    def create_logs_stream(self):
        """
        创建实时日志流 (Server-Sent Events)
        
        Returns:
            Generator: 日志流生成器
        """
        def log_stream_generator():
            try:
                # 发送当前缓存的最后几条日志
                with self.log_lock:
                    recent_logs = list(self.log_cache)[-10:]  # 最近10条日志

                for log_entry in recent_logs:
                    yield f"data: {log_entry}\n\n"

                # 实时发送新日志（这里需要一个更复杂的实现来监听新日志）
                # 暂时使用轮询方式
                last_count = len(self.log_cache)
                while True:
                    time.sleep(1)  # 每秒检查一次
                    with self.log_lock:
                        current_count = len(self.log_cache)
                        if current_count > last_count:
                            # 有新日志
                            new_logs = list(self.log_cache)[last_count:]
                            for log_entry in new_logs:
                                yield f"data: {log_entry}\n\n"
                            last_count = current_count
            except Exception as e:
                logging.error(f"日志流生成器出错: {e}")
                yield f"data: {{\"error\": \"日志流出错: {str(e)}\"}}\n\n"

        return log_stream_generator()


def create_log_service(log_cache, log_lock):
    """
    创建日志服务实例
    
    Args:
        log_cache: 日志缓存队列
        log_lock: 日志锁对象
        
    Returns:
        LogService: 日志服务实例
    """
    return LogService(log_cache, log_lock)


# 便捷函数，用于在路由中调用
def process_test_logs_request(log_service):
    """处理测试日志请求"""
    result = log_service.test_logs()
    
    if result.get('success'):
        return jsonify({
            "code": result.get('code', 200),
            "msg": result.get('msg', '测试日志成功'),
            "data": result.get('data')
        }), result.get('code', 200)
    else:
        return jsonify({
            "code": result.get('code', 500),
            "msg": result.get('msg', '测试日志失败'),
            "data": result.get('data')
        }), result.get('code', 500)


def process_get_logs_request(log_service):
    """处理获取日志请求"""
    # 获取查询参数
    limit = request.args.get('limit', 100)
    level_filter = request.args.get('level', '')
    since = request.args.get('since', '')
    
    result = log_service.get_logs(limit=limit, level_filter=level_filter, since=since)
    
    if result.get('success'):
        return jsonify({
            "code": result.get('code', 200),
            "msg": result.get('msg', '获取日志成功'),
            "data": result.get('data')
        }), result.get('code', 200)
    else:
        return jsonify({
            "code": result.get('code', 500),
            "msg": result.get('msg', '获取日志失败'),
            "data": result.get('data')
        }), result.get('code', 500)


def process_clear_logs_request(log_service):
    """处理清空日志请求"""
    result = log_service.clear_logs()
    
    if result.get('success'):
        return jsonify({
            "code": result.get('code', 200),
            "msg": result.get('msg', '清空日志成功'),
            "data": result.get('data')
        }), result.get('code', 200)
    else:
        return jsonify({
            "code": result.get('code', 500),
            "msg": result.get('msg', '清空日志失败'),
            "data": result.get('data')
        }), result.get('code', 500)


def process_logs_stream_request(log_service):
    """处理日志流请求"""
    try:
        stream_generator = log_service.create_logs_stream()
        response = Response(stream_generator, mimetype='text/event-stream')
        response.headers['Cache-Control'] = 'no-cache'
        response.headers['X-Accel-Buffering'] = 'no'
        return response
    except Exception as e:
        logging.error(f"创建日志流失败: {e}")
        return jsonify({
            "code": 500,
            "msg": f"创建日志流失败: {str(e)}",
            "data": None
        }), 500
