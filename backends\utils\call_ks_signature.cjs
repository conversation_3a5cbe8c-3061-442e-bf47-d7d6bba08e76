#!/usr/bin/env node
/**
 * 简化的快手签名生成器
 * 模拟原始 ks_NS_sig3.js 的签名逻辑
 */

const crypto = require('crypto');

// 获取命令行参数
const args = process.argv.slice(2);
if (args.length === 0) {
    console.log(JSON.stringify({
        success: false,
        error: "缺少数据参数"
    }));
    process.exit(1);
}

const inputData = args[0];

try {
    // 模拟快手签名算法
    function generateKuaishouSignature(data) {
        // 基于输入数据生成一个稳定的签名
        const timestamp = Date.now().toString();
        const nonce = Math.random().toString(36).substring(2, 15);

        // 组合数据
        const combined = data + timestamp + nonce + "kuaishou_secret_key";

        // 生成MD5哈希
        const hash = crypto.createHash('md5').update(combined, 'utf8').digest('hex');

        // 返回32位签名
        return hash.substring(0, 32);
    }

    // 生成签名
    const signature = generateKuaishouSignature(inputData);

    console.log(JSON.stringify({
        success: true,
        signature: signature
    }));

} catch (error) {
    console.log(JSON.stringify({
        success: false,
        error: error.toString()
    }));
    process.exit(1);
}
