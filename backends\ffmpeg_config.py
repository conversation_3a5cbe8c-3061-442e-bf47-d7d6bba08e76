#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FFmpeg 配置模块
统一管理 FFmpeg 路径配置，供所有模块使用
"""

import shutil
from pathlib import Path

# 获取 backends 目录的绝对路径
BACKENDS_DIR = Path(__file__).parent

def get_ffmpeg_bin():
    """
    获取 ffmpeg 可执行文件路径
    优先使用系统 PATH 中的 ffmpeg，如果没有则使用本地 ffmpeg 目录
    """
    return shutil.which("ffmpeg") or str(BACKENDS_DIR / "ffmpeg" / "ffmpeg.exe")

def get_ffprobe_bin():
    """
    获取 ffprobe 可执行文件路径
    优先使用系统 PATH 中的 ffprobe，如果没有则使用本地 ffmpeg 目录
    """
    return shutil.which("ffprobe") or str(BACKENDS_DIR / "ffmpeg" / "ffprobe.exe")

def get_ffmpeg_dir():
    """
    获取 ffmpeg 目录路径
    """
    return BACKENDS_DIR / "ffmpeg"

def check_ffmpeg_exists():
    """
    检查 ffmpeg 是否存在
    """
    ffmpeg_path = get_ffmpeg_bin()
    return Path(ffmpeg_path).exists()

def check_ffprobe_exists():
    """
    检查 ffprobe 是否存在
    """
    ffprobe_path = get_ffprobe_bin()
    return Path(ffprobe_path).exists()

# 导出常用的路径
FFMPEG_BIN = get_ffmpeg_bin()
FFPROBE_BIN = get_ffprobe_bin()
FFMPEG_DIR = get_ffmpeg_dir()
