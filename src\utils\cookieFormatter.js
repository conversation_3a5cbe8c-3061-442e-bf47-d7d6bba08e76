/**
 * Cookie 格式化工具
 * 将 JSON 格式的 cookie 数据转换为 HTTP Header 格式
 */

/**
 * 将 JSON 格式的 cookie 数据转换为 HTTP Cookie 头格式
 * @param {string} cookieJsonString - JSON 字符串格式的 cookie 数据
 * @returns {string} - 格式化后的 Cookie 头字符串
 */
export const formatCookieToHeader = (cookieJsonString) => {
  try {
    // 解析 JSON 字符串
    const cookieData = JSON.parse(cookieJsonString);
    
    if (!cookieData.cookies || !Array.isArray(cookieData.cookies)) {
      console.warn("Cookie 数据格式不正确，缺少 cookies 数组");
      return "";
    }

    // 提取所有 cookie 并格式化为 name=value 格式
    const cookiePairs = cookieData.cookies
      .filter(cookie => {
        // 过滤有效的 cookie（必须有 name 和 value）
        return cookie.name && cookie.value !== undefined;
      })
      .map(cookie => {
        // 格式化为 name=value
        return `${cookie.name}=${cookie.value}`;
      });

    // 用分号和空格连接所有 cookie
    const cookieHeader = cookiePairs.join("; ");
    
    console.log(`格式化 Cookie 成功，共 ${cookiePairs.length} 个 cookie`);
    console.log(`Cookie 头长度: ${cookieHeader.length} 字符`);
    
    return cookieHeader;
    
  } catch (error) {
    console.error("解析 Cookie JSON 失败:", error);
    console.error("原始数据:", cookieJsonString);
    return "";
  }
};

/**
 * 根据域名过滤 cookie
 * @param {string} cookieJsonString - JSON 字符串格式的 cookie 数据
 * @param {string} targetDomain - 目标域名（如 "kuaishou.com"）
 * @returns {string} - 过滤后的 Cookie 头字符串
 */
export const formatCookieForDomain = (cookieJsonString, targetDomain) => {
  try {
    const cookieData = JSON.parse(cookieJsonString);
    
    if (!cookieData.cookies || !Array.isArray(cookieData.cookies)) {
      return "";
    }

    // 过滤指定域名的 cookie
    const domainCookies = cookieData.cookies
      .filter(cookie => {
        if (!cookie.name || cookie.value === undefined) {
          return false;
        }
        
        // 检查 cookie 的域名是否匹配目标域名
        const cookieDomain = cookie.domain || "";
        return cookieDomain.includes(targetDomain) || 
               targetDomain.includes(cookieDomain.replace(/^\./, ""));
      })
      .map(cookie => `${cookie.name}=${cookie.value}`);

    const cookieHeader = domainCookies.join("; ");
    
    console.log(`为域名 ${targetDomain} 格式化 Cookie，共 ${domainCookies.length} 个 cookie`);
    
    return cookieHeader;
    
  } catch (error) {
    console.error("为域名过滤 Cookie 失败:", error);
    return "";
  }
};

/**
 * 获取特定名称的 cookie 值
 * @param {string} cookieJsonString - JSON 字符串格式的 cookie 数据
 * @param {string} cookieName - cookie 名称
 * @returns {string|null} - cookie 值，如果不存在返回 null
 */
export const getCookieValue = (cookieJsonString, cookieName) => {
  try {
    const cookieData = JSON.parse(cookieJsonString);
    
    if (!cookieData.cookies || !Array.isArray(cookieData.cookies)) {
      return null;
    }

    const targetCookie = cookieData.cookies.find(cookie => cookie.name === cookieName);
    return targetCookie ? targetCookie.value : null;
    
  } catch (error) {
    console.error("获取 Cookie 值失败:", error);
    return null;
  }
};

/**
 * 调试函数：打印 cookie 信息
 * @param {string} cookieJsonString - JSON 字符串格式的 cookie 数据
 */
export const debugCookieInfo = (cookieJsonString) => {
  try {
    const cookieData = JSON.parse(cookieJsonString);
    
    console.log("=== Cookie 调试信息 ===");
    console.log("总 Cookie 数量:", cookieData.cookies?.length || 0);
    
    if (cookieData.cookies) {
      // 按域名分组显示
      const domainGroups = {};
      cookieData.cookies.forEach(cookie => {
        const domain = cookie.domain || "未知域名";
        if (!domainGroups[domain]) {
          domainGroups[domain] = [];
        }
        domainGroups[domain].push(cookie.name);
      });
      
      console.log("按域名分组:");
      Object.entries(domainGroups).forEach(([domain, names]) => {
        console.log(`  ${domain}: ${names.join(", ")}`);
      });
    }
    
    // 显示格式化后的 Cookie 头
    const formattedCookie = formatCookieToHeader(cookieJsonString);
    console.log("格式化后的 Cookie 头长度:", formattedCookie.length);
    console.log("Cookie 头预览:", formattedCookie);
    
  } catch (error) {
    console.error("调试 Cookie 信息失败:", error);
  }
};
