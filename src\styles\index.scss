// 导入重置样式
@use './reset.scss';

// 导入变量
@use './variables.scss' as *;

// 全局样式
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  color: $text-primary;
  background-color: $bg-color-page;
}

#app {
  min-height: 100vh;
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

// 间距工具类
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.m-1 { margin: $spacing-xs; }
.mt-1 { margin-top: $spacing-xs; }
.mr-1 { margin-right: $spacing-xs; }
.mb-1 { margin-bottom: $spacing-xs; }
.ml-1 { margin-left: $spacing-xs; }

.m-2 { margin: $spacing-sm; }
.mt-2 { margin-top: $spacing-sm; }
.mr-2 { margin-right: $spacing-sm; }
.mb-2 { margin-bottom: $spacing-sm; }
.ml-2 { margin-left: $spacing-sm; }

.m-3 { margin: $spacing-md; }
.mt-3 { margin-top: $spacing-md; }
.mr-3 { margin-right: $spacing-md; }
.mb-3 { margin-bottom: $spacing-md; }
.ml-3 { margin-left: $spacing-md; }

.m-4 { margin: $spacing-lg; }
.mt-4 { margin-top: $spacing-lg; }
.mr-4 { margin-right: $spacing-lg; }
.mb-4 { margin-bottom: $spacing-lg; }
.ml-4 { margin-left: $spacing-lg; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.p-1 { padding: $spacing-xs; }
.pt-1 { padding-top: $spacing-xs; }
.pr-1 { padding-right: $spacing-xs; }
.pb-1 { padding-bottom: $spacing-xs; }
.pl-1 { padding-left: $spacing-xs; }

.p-2 { padding: $spacing-sm; }
.pt-2 { padding-top: $spacing-sm; }
.pr-2 { padding-right: $spacing-sm; }
.pb-2 { padding-bottom: $spacing-sm; }
.pl-2 { padding-left: $spacing-sm; }

.p-3 { padding: $spacing-md; }
.pt-3 { padding-top: $spacing-md; }
.pr-3 { padding-right: $spacing-md; }
.pb-3 { padding-bottom: $spacing-md; }
.pl-3 { padding-left: $spacing-md; }

.p-4 { padding: $spacing-lg; }
.pt-4 { padding-top: $spacing-lg; }
.pr-4 { padding-right: $spacing-lg; }
.pb-4 { padding-bottom: $spacing-lg; }
.pl-4 { padding-left: $spacing-lg; }