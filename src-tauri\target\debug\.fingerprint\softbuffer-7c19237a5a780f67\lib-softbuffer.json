{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 15815798674084965833, "deps": [[376837177317575824, "build_script_build", false, 14003076817354691149], [4143744114649553716, "raw_window_handle", false, 3598648047324117099], [7314894124883917868, "log", false, 628205322640765158], [10281541584571964250, "windows_sys", false, 17331437252017536261]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-7c19237a5a780f67\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}