/**
 * 快手登录工具
 * 提供快手登录相关的数据和逻辑操作
 */

import { fetch } from "@tauri-apps/plugin-http";

/**
 * 生成动态时间戳的 Cookie 字符串
 * @returns {string} 包含当前时间戳的 Cookie 字符串
 */
const generateDynamicCookie = () => {
  const now = Date.now();
  const hmTimestamp = Math.floor(now / 1000); // Hm_lvt 和 Hm_lpvt 使用秒级时间戳
  const ktraceTimestamp = now; // ktrace-context 使用毫秒级时间戳
  const didvTimestamp = now; // didv 使用毫秒级时间戳

  // 动态生成 ktrace-context，使用当前时间戳
  // 原始格式: 1|************************************.时间戳.MzYwMDM2|************************************.时间戳.MzYwMDM3|0|graphql-server|webservice|false|NA
  const ktraceContext = `1|************************************.${ktraceTimestamp}.MzYwMDM2|************************************.${ktraceTimestamp}.MzYwMDM3|0|graphql-server|webservice|false|NA`;

  return `kpf=PC_WEB; clientid=3; ktrace-context=${ktraceContext}; bUserId=*************; Hm_lvt_86a27b7db2c5c0ae37fee4a8a35033ee=${hmTimestamp}; Hm_lpvt_86a27b7db2c5c0ae37fee4a8a35033ee=${hmTimestamp}; HMACCOUNT=72FEDC74A8C520FE; kwpsecproductname=kuaishou-growth-offSite-h5-ssr; kwfv1=PnGU+9+Y8008S+nH0U+0mjPf8fP08f+98f+nLlwnrIP9+0G0G7+/z08/8jP/ZEw/80Gf+D8/PUPeHFPfGhPfLE+0qFPBpf8ez0P/qIPnHAwe4fG/S0w/rF8Bp08/WEGfLA+0GIPAz0w/Z780D9PAH9PRADEG/cEPAHh8ePU+/G78BclP9rl8nGl+0m08eQjP/mSPnzY8/LE8nLUwB+SPnGI8c==; did=web_208dff47b2d74faca1d43e8e2d087f44; didv=${didvTimestamp}; kpn=KUAISHOU_VISION`;
};

/**
 * 获取快手主页 DID Cookie
 * @returns {Promise<Object>} Cookie 信息对象
 */
export const getKuaishouDid = async () => {
  try {
    console.log("正在请求快手主页获取 DID...");
    
    const response = await fetch("https://www.kuaishou.com/?isHome=1", {
      method: "GET",
      headers: {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        // "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6",
        "Cache-Control": "no-cache",
        "DNT": "1",
        "Pragma": "no-cache",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "User-Agent": "%E5%BF%AB%E6%89%8B%E6%9E%81%E9%80%9F%E7%89%88/3921 CFNetwork/1399 Darwin/22.1.0",
        "cookie": generateDynamicCookie()
      }
    });
console.log("返回信息:",await response.text());
    // 提取所有 Set-Cookie 响应头
    const setCookieResult = {};
    
    // 遍历所有响应头，查找 set-cookie 相关的头
    for (const [key, value] of response.headers.entries()) {
      if (key.toLowerCase() === 'set-cookie') {
        // 解析 Set-Cookie 头，提取 cookie 名称和值
        const cookies = Array.isArray(value) ? value : [value];
        cookies.forEach(cookieString => {
          // 解析每个 cookie 字符串 (格式: name=value; path=/; domain=...)
          const cookieParts = cookieString.split(';')[0]; // 只取第一部分 name=value
          const [cookieName, cookieValue] = cookieParts.split('=', 2);
          if (cookieName && cookieValue) {
            setCookieResult[cookieName.trim()] = cookieValue.trim();
          }
        });
        console.log(`Set-Cookie 头 - ${key}: ${value}`);
      }
    }
    
    return setCookieResult;
  } catch (error) {
    console.error("获取 DID 失败:", error);
    console.error("错误详情:", error.message);
    console.error("错误堆栈:", error.stack);
    return {};
  }
};

/**
 * 请求二维码接口
 * @param {Object} cookieData - Cookie 数据
 * @returns {Promise<Object|null>} 二维码数据或 null
 */
export const requestQrCode = async (cookieData) => {
  try {
    const did = cookieData.did;
    const didv = Date.now().toString();
    console.log("生成的时间戳 didv:", didv);    
    if (!did) {
      throw new Error("未获取到有效的 DID 信息");
    }

    console.log("正在请求二维码，DID:", did, "DIDV:", didv);

    const response = await fetch("https://id.kuaishou.com/rest/c/infra/ks/qr/start", {
      method: "POST",
      headers: {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6",
        "cache-control": "no-cache",
        "content-type": "application/x-www-form-urlencoded",
        "dnt": "1",
        "origin": "https://www.kuaishou.com",
        "pragma": "no-cache",
        "referer": "https://www.kuaishou.com/?isHome=1",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "cookie": `did=${did}; didv=${didv}`,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
      },
      body: "sid=kuaishou.server.webday7&channelType=UNKNOWN&encryptHeaders="
    });

    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`);
    }

    // 先获取响应文本，然后尝试解析 JSON
    const responseText = await response.text();
    console.log("二维码响应原始文本:", responseText);
    
    let qrData;
    try {
      qrData = JSON.parse(responseText);
    } catch (parseError) {
      console.error("JSON 解析失败:", parseError);
      console.error("响应内容:", responseText);
      throw new Error(`响应不是有效的 JSON 格式: ${parseError.message}`);
    }
    
    console.log("二维码响应数据:", qrData);

    if (qrData.result === 1 && qrData.imageData) {
      return {
        ...qrData,
        cookieData: { ...cookieData, didv: didv }
      };
    } else {
      throw new Error("获取二维码失败");
    }

  } catch (error) {
    console.error("请求二维码失败:", error);
    throw error;
  }
};

/**
 * 检查扫码结果
 * @param {Object} qrData - 二维码数据
 * @param {Object} cookieData - Cookie 数据
 * @returns {Promise<Object|null>} 扫码结果或 null
 */
export const checkScanResult = async (qrData, cookieData) => {
  try {
    const response = await fetch("https://id.kuaishou.com/rest/c/infra/ks/qr/scanResult", {
      method: "POST",
      headers: {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6",
        "cache-control": "no-cache",
        "content-type": "application/x-www-form-urlencoded",
        "dnt": "1",
        "origin": "https://www.kuaishou.com",
        "pragma": "no-cache",
        "referer": "https://www.kuaishou.com/?isHome=1",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "cookie": `did=${cookieData.did}; didv=${cookieData.didv}`,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
      },
      body: `qrLoginToken=${qrData.qrLoginToken}&qrLoginSignature=${qrData.qrLoginSignature}&channelType=UNKNOWN&encryptHeaders=`
    });
    
    if (response.ok) {
      const responseText = await response.text();
      console.log("扫码结果原始响应:", responseText);
      
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error("扫码结果 JSON 解析失败:", parseError);
        console.error("响应内容:", responseText);
        return null;
      }
      
      console.log("扫码结果:", result);
      return result;
    } else {
      console.error("扫码结果请求失败:", response.status);
      return null;
    }
  } catch (error) {
    console.error("检查扫码结果失败:", error);
    return null;
  }
};

/**
 * 获取 qrToken
 * @param {Object} qrData - 二维码数据
 * @param {Object} cookieData - Cookie 数据
 * @returns {Promise<Object|null>} qrToken 结果或 null
 */
export const getQrToken = async (qrData, cookieData) => {
  try {
    console.log("正在获取 qrToken...");
    
    const response = await fetch("https://id.kuaishou.com/rest/c/infra/ks/qr/acceptResult", {
      method: "POST",
      headers: {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6",
        "Cache-Control": "no-cache",
        "DNT": "1",
        "Origin": "https://www.kuaishou.com",
        "Pragma": "no-cache",
        "Referer": "https://www.kuaishou.com/?isHome=1",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "Content-Type": "application/x-www-form-urlencoded",
        "cookie": `did=${cookieData.did}; didv=${cookieData.didv}`,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
      },
      body: `qrLoginToken=${qrData.qrLoginToken}&qrLoginSignature=${qrData.qrLoginSignature}&sid=kuaishou.server.webday7&channelType=UNKNOWN&encryptHeaders=`
    });

    if (!response.ok) {
      throw new Error(`获取 qrToken 请求失败: ${response.status}`);
    }

    const responseText = await response.text();
    console.log("qrToken 响应原始文本:", responseText);
    
    let result;
    try {
      result = JSON.parse(responseText);
    } catch (parseError) {
      console.error("qrToken JSON 解析失败:", parseError);
      console.error("响应内容:", responseText);
      throw new Error(`qrToken 响应不是有效的 JSON 格式: ${parseError.message}`);
    }
    
    console.log("qrToken 响应数据:", result);

    if (result.result === 1 && result.qrToken) {
      console.log("获取 qrToken 成功:", result.qrToken);
      return result;
    } else {
      throw new Error("获取 qrToken 失败");
    }

  } catch (error) {
    console.error("获取 qrToken 失败:", error);
    throw error;
  }
};

/**
 * 完成最终登录，获取完整的 cookie
 * @param {Object} qrTokenResult - qrToken 结果
 * @param {Object} cookieData - Cookie 数据
 * @returns {Promise<Object|null>} 登录结果或 null
 */
export const completeLogin = async (qrTokenResult, cookieData) => {
  try {
    console.log("正在完成最终登录...");
    
    const response = await fetch("https://id.kuaishou.com/pass/kuaishou/login/qr/callback", {
      method: "POST",
      headers: {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6",
        "Cache-Control": "no-cache",
        "DNT": "1",
        "Origin": "https://www.kuaishou.com",
        "Pragma": "no-cache",
        "Referer": "https://www.kuaishou.com/?isHome=1",
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "Content-Type": "application/x-www-form-urlencoded",
        "cookie": `did=${cookieData.did}; didv=${cookieData.didv}`,
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
      },
      body: `qrToken=${encodeURIComponent(qrTokenResult.qrToken)}&sid=${qrTokenResult.sid}&channelType=UNKNOWN&encryptHeaders=`
    });

    if (!response.ok) {
      throw new Error(`完成登录请求失败: ${response.status}`);
    }

    console.log("登录响应状态:", response.status);
    console.log("登录响应头:", response.headers);

    // 提取 Set-Cookie 头信息
    const setCookies = {};
    for (const [key, value] of response.headers.entries()) {
      if (key.toLowerCase() === 'set-cookie') {
        console.log(`Set-Cookie 头 - ${key}: ${value}`);
        
        // 解析 Set-Cookie 头，提取 cookie 名称和值
        const cookies = Array.isArray(value) ? value : [value];
        cookies.forEach(cookieString => {
          // 解析每个 cookie 字符串 (格式: name=value; path=/; domain=...)
          const cookieParts = cookieString.split(';')[0]; // 只取第一部分 name=value
          const [cookieName, cookieValue] = cookieParts.split('=', 2);
          if (cookieName && cookieValue) {
            setCookies[cookieName.trim()] = cookieValue.trim();
          }
        });
      }
    }

    console.log("提取到的 Set-Cookie 信息:", setCookies);

    if (Object.keys(setCookies).length === 0) {
      console.log("未获取到 Set-Cookie 头，尝试从响应体获取信息");
    }

    // 读取响应体
    const responseText = await response.text();
    console.log("登录响应体:", responseText);

    // 返回完整的登录信息
    const loginData = {
      loginTime: Date.now(),
      qrTokenResult: qrTokenResult,
      cookies: {
        ...cookieData,
        ...setCookies // 合并新的 cookie
      },
      responseHeaders: Object.fromEntries(response.headers.entries()),
      responseBody: responseText
    };
    
    console.log("完整登录信息:", loginData);
    return loginData;

  } catch (error) {
    console.error("完成登录失败:", error);
    throw error;
  }
};

/**
 * 登录状态检查类
 * 用于管理登录过程中的状态检查
 */
export class LoginStatusChecker {
  constructor() {
    this.isChecking = false;
    this.attempts = 0;
    this.maxAttempts = 60; // 最多检测60次（3分钟）
    this.interval = 3000; // 每3秒检测一次
    this.timeoutId = null;
  }

  /**
   * 开始检查扫码状态
   * @param {Object} qrData - 二维码数据
   * @param {Object} cookieData - Cookie 数据
   * @param {Function} onScanSuccess - 扫码成功回调
   * @param {Function} onQrExpired - 二维码过期回调
   * @param {Function} onError - 错误回调
   */
  startChecking(qrData, cookieData, onScanSuccess, onQrExpired, onError) {
    this.isChecking = true;
    this.attempts = 0;
    
    const checkScan = async () => {
      if (!this.isChecking) return;
      
      this.attempts++;
      console.log(`检测扫码结果第 ${this.attempts} 次...`);

      try {
        const scanResult = await checkScanResult(qrData, cookieData);
        
        if (!scanResult) {
          if (this.attempts < this.maxAttempts) {
            this.timeoutId = setTimeout(checkScan, this.interval);
          } else {
            console.log("检测扫码超时");
            if (onError) onError("检测扫码超时，请重新获取二维码");
          }
          return;
        }

        // 用户确认登录
        if (scanResult.result === 1) {
          console.log("扫码确认成功");
          this.stopChecking();
          if (onScanSuccess) onScanSuccess(scanResult);
          return;
        } else if (scanResult.result === 707) {
          // 二维码已过期
          console.log("二维码已过期");
          this.stopChecking();
          if (onQrExpired) onQrExpired();
          return;
        } else {
          // 其他状态，继续检测
          console.log("扫码状态:", scanResult.result, "等待用户操作...");
          if (this.attempts < this.maxAttempts) {
            this.timeoutId = setTimeout(checkScan, this.interval);
          }
        }

      } catch (error) {
        console.error("检测扫码出错:", error);
        if (this.attempts < this.maxAttempts) {
          this.timeoutId = setTimeout(checkScan, this.interval);
        } else {
          this.stopChecking();
          if (onError) onError("检测扫码失败: " + error.message);
        }
      }
    };

    // 开始检测
    this.timeoutId = setTimeout(checkScan, this.interval);
  }

 

  /**
   * 停止检查
   */
  stopChecking() {
    this.isChecking = false;
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
}
