{"rustc": 16591470773350601817, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 15657897354478470176, "path": 3770938576108535350, "deps": [[507245148068099358, "ring", false, 6293109935866183401], [5070769681332304831, "once_cell", false, 1077203143867369845], [5501164552881223878, "pki_types", false, 14327559253002116693], [6528079939221783635, "zeroize", false, 11940008866800934211], [9027827595814673957, "build_script_build", false, 4249149349321727298], [12989347533245466967, "<PERSON><PERSON><PERSON>", false, 7605426816470332247], [17003143334332120809, "subtle", false, 8628043409105096420]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-cbefd3673a05368a\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}