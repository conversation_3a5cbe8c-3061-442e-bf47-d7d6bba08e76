{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"devtools\", \"http-range\", \"objc-exception\", \"protocol-asset\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webview-data-url\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 18361179771189010965, "deps": [[40386456601120721, "percent_encoding", false, 9389606893816955030], [1200537532907108615, "url<PERSON><PERSON>n", false, 16893322144751960030], [1569213597676578307, "tauri_macros", false, 15983402054886262744], [1861048441542724925, "bytes", false, 15734092130910943032], [2069998946850810971, "build_script_build", false, 6829209256351337886], [2999978091831213404, "serde_repr", false, 4924174900153693276], [3150220818285335163, "url", false, 11966484956960746278], [3747475527459805754, "tauri_runtime_wry", false, 13928186781824824231], [4143744114649553716, "raw_window_handle", false, 3598648047324117099], [4668130461107581794, "anyhow", false, 16902183809371375433], [4725822363599429376, "muda", false, 7043822633875258164], [4919829919303820331, "serialize_to_javascript", false, 18087705374996854987], [7314894124883917868, "log", false, 628205322640765158], [7670211519503158651, "getrandom", false, 14516070059475055349], [7801942656278641025, "tauri_utils", false, 38280291934596112], [8145704335312028338, "glob", false, 15754072080550429892], [8256202458064874477, "dirs", false, 12332571090925556481], [8324636962323428845, "serde_json", false, 12397612473663235593], [8866577183823226611, "http_range", false, 1901439558154600407], [8992330329689023070, "reqwest", false, 2290250006770050810], [9116001059600962126, "webview2_com", false, 2264998229827461466], [9276336542470079068, "window_vibrancy", false, 17886770761236374929], [9538054652646069845, "tokio", false, 2051528350863639385], [10229185211513642314, "mime", false, 17767382888373566784], [10629569228670356391, "futures_util", false, 5529326595165766766], [10967960060725374459, "serde", false, 15592400294397051589], [11989259058781683633, "dunce", false, 7272876513159322961], [13077543566650298139, "heck", false, 8091741224224843662], [13160085343524438957, "windows", false, 15661530581133800806], [17057645011604283103, "thiserror", false, 8021663526431293538], [17756946674516200488, "tauri_runtime", false, 1028180213599424892], [17860019243264344128, "http", false, 9568133891094124003]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-b5830e9ecffb6a94\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}