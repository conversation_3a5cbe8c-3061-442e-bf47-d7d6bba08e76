// Tauri fetch 调试工具
import { fetch } from "@tauri-apps/plugin-http";

/**
 * 测试基本的 fetch 功能
 */
export async function testBasicFetch() {
  console.log("=== 测试基本 fetch 功能 ===");
  
  try {
    // 测试简单的 GET 请求
    console.log("1. 测试简单 GET 请求...");
    const response = await fetch("https://httpbin.org/get", {
      method: "GET",
      headers: {
        "User-Agent": "Tauri-Test/1.0"
      }
    });
    
    console.log("Response status:", response.status);
    console.log("Response ok:", response.ok);
    console.log("Response headers:", Object.fromEntries(response.headers.entries()));
    
    const text = await response.text();
    console.log("Response text length:", text.length);
    console.log("Response text preview:", text.substring(0, 200));
    
    return { success: true, status: response.status, textLength: text.length };
  } catch (error) {
    console.error("基本 fetch 测试失败:", error);
    return { success: false, error: error.message, stack: error.stack };
  }
}

/**
 * 测试 POST 请求
 */
export async function testPostFetch() {
  console.log("=== 测试 POST 请求 ===");
  
  try {
    const testData = { test: "data", timestamp: Date.now() };
    
    console.log("2. 测试 POST 请求...");
    const response = await fetch("https://httpbin.org/post", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      body: JSON.stringify(testData)
    });
    
    console.log("POST Response status:", response.status);
    console.log("POST Response ok:", response.ok);
    
    const text = await response.text();
    console.log("POST Response text length:", text.length);
    
    let jsonData = {};
    try {
      jsonData = JSON.parse(text);
      console.log("POST Response JSON parsed successfully");
      console.log("POST Response data:", jsonData.json);
    } catch (parseError) {
      console.error("POST Response JSON parse error:", parseError);
    }
    
    return { success: true, status: response.status, data: jsonData };
  } catch (error) {
    console.error("POST fetch 测试失败:", error);
    return { success: false, error: error.message, stack: error.stack };
  }
}

/**
 * 测试超时和错误处理
 */
export async function testFetchTimeout() {
  console.log("=== 测试超时和错误处理 ===");
  
  try {
    console.log("3. 测试超时请求...");
    
    // 创建一个带超时的 AbortController
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时
    
    const response = await fetch("https://httpbin.org/delay/3", {
      method: "GET",
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    console.log("Timeout test response status:", response.status);
    const text = await response.text();
    console.log("Timeout test completed successfully");
    
    return { success: true, status: response.status };
  } catch (error) {
    console.error("超时测试失败:", error);
    return { success: false, error: error.message, type: error.name };
  }
}

/**
 * 测试错误状态码处理
 */
export async function testErrorStatusCodes() {
  console.log("=== 测试错误状态码处理 ===");
  
  const testCodes = [400, 401, 403, 404, 500];
  const results = {};
  
  for (const code of testCodes) {
    try {
      console.log(`4. 测试状态码 ${code}...`);
      
      const response = await fetch(`https://httpbin.org/status/${code}`, {
        method: "GET"
      });
      
      console.log(`Status ${code} - Response status:`, response.status);
      console.log(`Status ${code} - Response ok:`, response.ok);
      
      const text = await response.text();
      console.log(`Status ${code} - Response text length:`, text.length);
      
      results[code] = {
        success: true,
        status: response.status,
        ok: response.ok,
        textLength: text.length
      };
    } catch (error) {
      console.error(`状态码 ${code} 测试失败:`, error);
      results[code] = {
        success: false,
        error: error.message
      };
    }
  }
  
  return results;
}

/**
 * 模拟 authRequest 的请求模式
 */
export async function testAuthRequestPattern() {
  console.log("=== 测试 authRequest 请求模式 ===");
  
  try {
    console.log("5. 模拟 authRequest 请求...");
    
    const testUrl = "https://httpbin.org/post";
    const testData = {
      username: "test",
      password: "test123"
    };
    
    const headers = {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": "Bearer test-token"
    };
    
    console.log("Request URL:", testUrl);
    console.log("Request headers:", headers);
    console.log("Request data:", testData);
    
    const response = await fetch(testUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(testData)
    });
    
    console.log("Auth pattern response status:", response.status);
    console.log("Auth pattern response ok:", response.ok);
    console.log("Auth pattern response headers:", Object.fromEntries(response.headers.entries()));
    
    // 这里是关键点 - 测试 response.text() 是否会中断
    console.log("开始读取响应体...");
    const text = await response.text();
    console.log("响应体读取完成，长度:", text.length);
    
    let jsonData = {};
    try {
      jsonData = JSON.parse(text);
      console.log("JSON 解析成功");
    } catch (parseError) {
      console.error("JSON 解析失败:", parseError);
      console.log("原始响应文本:", text.substring(0, 500));
    }
    
    return {
      success: true,
      status: response.status,
      textLength: text.length,
      data: jsonData
    };
  } catch (error) {
    console.error("authRequest 模式测试失败:", error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
}

/**
 * 运行所有测试
 */
export async function runAllFetchTests() {
  console.log("🚀 开始 Tauri fetch 诊断测试...");
  
  const results = {
    basic: null,
    post: null,
    timeout: null,
    errorCodes: null,
    authPattern: null
  };
  
  try {
    results.basic = await testBasicFetch();
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    
    results.post = await testPostFetch();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    results.timeout = await testFetchTimeout();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    results.errorCodes = await testErrorStatusCodes();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    results.authPattern = await testAuthRequestPattern();
    
    console.log("✅ 所有测试完成");
    console.log("📊 测试结果汇总:", results);
    
    return results;
  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error);
    return { ...results, globalError: error.message };
  }
}

/**
 * 检查 Tauri HTTP 插件版本和配置
 */
export async function checkTauriHttpInfo() {
  console.log("=== 检查 Tauri HTTP 插件信息 ===");
  
  try {
    // 检查 fetch 函数是否存在
    console.log("fetch 函数类型:", typeof fetch);
    console.log("fetch 函数:", fetch.toString().substring(0, 100));
    
    // 检查 Response 对象
    const testResponse = new Response("test", { status: 200 });
    console.log("Response 构造函数可用:", !!testResponse);
    
    // 检查 Headers 对象
    const testHeaders = new Headers({ "test": "value" });
    console.log("Headers 构造函数可用:", !!testHeaders);
    
    return {
      fetchAvailable: typeof fetch === 'function',
      responseAvailable: typeof Response === 'function',
      headersAvailable: typeof Headers === 'function'
    };
  } catch (error) {
    console.error("检查 Tauri HTTP 信息失败:", error);
    return { error: error.message };
  }
}

export default {
  testBasicFetch,
  testPostFetch,
  testFetchTimeout,
  testErrorStatusCodes,
  testAuthRequestPattern,
  runAllFetchTests,
  checkTauriHttpInfo
};
