/**
 * 卡密存储工具
 * 提供卡密的安全存储、读取和清除功能
 */

// 存储键名
const CARD_KEY_STORAGE_KEY = 'remembered_card_key'
const REMEMBER_CARD_KEY_FLAG = 'remember_card_key_flag'
const CARD_KEY_TIMESTAMP = 'card_key_timestamp'

// 卡密缓存过期时间（默认30天）
const CARD_KEY_EXPIRE_TIME = 30 * 24 * 60 * 60 * 1000

/**
 * 简单的字符串编码（注意：这不是加密，只是简单混淆）
 * @param {string} str - 要编码的字符串
 * @returns {string} 编码后的字符串
 */
const encodeString = (str) => {
  try {
    // 使用base64编码，并添加简单的字符替换
    const encoded = btoa(encodeURIComponent(str))
    return encoded.split('').reverse().join('') // 反转字符串
  } catch (error) {
    console.error('编码失败:', error)
    return str
  }
}

/**
 * 简单的字符串解码
 * @param {string} encodedStr - 编码后的字符串
 * @returns {string} 解码后的字符串
 */
const decodeString = (encodedStr) => {
  try {
    // 反转字符串并解码
    const reversed = encodedStr.split('').reverse().join('')
    return decodeURIComponent(atob(reversed))
  } catch (error) {
    console.error('解码失败:', error)
    throw new Error('卡密数据损坏')
  }
}

/**
 * 检查卡密是否过期
 * @returns {boolean} 是否过期
 */
const isCardKeyExpired = () => {
  try {
    const timestamp = localStorage.getItem(CARD_KEY_TIMESTAMP)
    if (!timestamp) return true
    
    const saveTime = parseInt(timestamp, 10)
    const now = Date.now()
    
    return (now - saveTime) > CARD_KEY_EXPIRE_TIME
  } catch (error) {
    console.error('检查过期时间失败:', error)
    return true
  }
}

/**
 * 保存卡密到本地存储
 * @param {string} cardKey - 卡密
 * @param {boolean} remember - 是否记住卡密
 * @returns {boolean} 保存是否成功
 */
export const saveCardKey = (cardKey, remember = false) => {
  try {
    if (remember && cardKey) {
      const encoded = encodeString(cardKey)
      const timestamp = Date.now().toString()
      
      localStorage.setItem(CARD_KEY_STORAGE_KEY, encoded)
      localStorage.setItem(REMEMBER_CARD_KEY_FLAG, 'true')
      localStorage.setItem(CARD_KEY_TIMESTAMP, timestamp)
      
      console.log('卡密已保存到本地存储')
      return true
    } else {
      // 如果不记住卡密，清除已保存的卡密
      clearCardKey()
      return true
    }
  } catch (error) {
    console.error('保存卡密失败:', error)
    return false
  }
}

/**
 * 从本地存储加载卡密
 * @returns {Object} 包含卡密和记住状态的对象
 */
export const loadCardKey = () => {
  try {
    const rememberFlag = localStorage.getItem(REMEMBER_CARD_KEY_FLAG)
    
    if (rememberFlag !== 'true') {
      return { cardKey: '', remember: false }
    }
    
    // 检查是否过期
    if (isCardKeyExpired()) {
      console.log('保存的卡密已过期，自动清除')
      clearCardKey()
      return { cardKey: '', remember: false }
    }
    
    const encoded = localStorage.getItem(CARD_KEY_STORAGE_KEY)
    if (!encoded) {
      return { cardKey: '', remember: false }
    }
    
    const decoded = decodeString(encoded)
    console.log('已从本地存储加载卡密')
    
    return {
      cardKey: decoded,
      remember: true
    }
  } catch (error) {
    console.error('加载卡密失败:', error)
    // 如果解码失败，清除可能损坏的数据
    clearCardKey()
    return { cardKey: '', remember: false }
  }
}

/**
 * 清除保存的卡密
 * @returns {boolean} 清除是否成功
 */
export const clearCardKey = () => {
  try {
    localStorage.removeItem(CARD_KEY_STORAGE_KEY)
    localStorage.removeItem(REMEMBER_CARD_KEY_FLAG)
    localStorage.removeItem(CARD_KEY_TIMESTAMP)
    console.log('已清除保存的卡密')
    return true
  } catch (error) {
    console.error('清除卡密失败:', error)
    return false
  }
}

/**
 * 检查是否有保存的卡密
 * @returns {boolean} 是否有保存的卡密
 */
export const hasRememberedCardKey = () => {
  try {
    const rememberFlag = localStorage.getItem(REMEMBER_CARD_KEY_FLAG)
    const cardKey = localStorage.getItem(CARD_KEY_STORAGE_KEY)
    
    return rememberFlag === 'true' && !!cardKey && !isCardKeyExpired()
  } catch (error) {
    console.error('检查保存的卡密失败:', error)
    return false
  }
}

/**
 * 获取卡密保存的时间信息
 * @returns {Object} 时间信息
 */
export const getCardKeyInfo = () => {
  try {
    const timestamp = localStorage.getItem(CARD_KEY_TIMESTAMP)
    if (!timestamp) {
      return { saved: false }
    }
    
    const saveTime = parseInt(timestamp, 10)
    const now = Date.now()
    const elapsed = now - saveTime
    const remaining = CARD_KEY_EXPIRE_TIME - elapsed
    
    return {
      saved: true,
      saveTime: new Date(saveTime),
      elapsed: elapsed,
      remaining: remaining,
      expired: remaining <= 0
    }
  } catch (error) {
    console.error('获取卡密信息失败:', error)
    return { saved: false }
  }
}

/**
 * 更新卡密的过期时间（重新设置时间戳）
 * @returns {boolean} 更新是否成功
 */
export const refreshCardKeyTimestamp = () => {
  try {
    if (hasRememberedCardKey()) {
      const timestamp = Date.now().toString()
      localStorage.setItem(CARD_KEY_TIMESTAMP, timestamp)
      console.log('卡密时间戳已更新')
      return true
    }
    return false
  } catch (error) {
    console.error('更新卡密时间戳失败:', error)
    return false
  }
}

// 导出配置常量
export const CARD_KEY_CONFIG = {
  EXPIRE_TIME: CARD_KEY_EXPIRE_TIME,
  EXPIRE_DAYS: CARD_KEY_EXPIRE_TIME / (24 * 60 * 60 * 1000)
}
