{"rustc": 16591470773350601817, "features": "[\"default\", \"public_suffix\", \"serde\", \"serde_json\"]", "declared_features": "[\"default\", \"log_secure_cookie_values\", \"preserve_order\", \"public_suffix\", \"serde\", \"serde_json\", \"serde_ron\", \"wasm-bindgen\"]", "target": 8140962409157740669, "profile": 15657897354478470176, "path": 2761815167457554234, "deps": [[505596520502798227, "publicsuffix", false, 18110119132181365639], [1500251540180216438, "document_features", false, 13126019952285353382], [3150220818285335163, "url", false, 11966484956960746278], [6376232718484714452, "idna", false, 1215063186393379432], [6998442642502920999, "serde_derive", false, 49958301967797884], [7314894124883917868, "log", false, 628205322640765158], [8324636962323428845, "serde_json", false, 12397612473663235593], [10967960060725374459, "serde", false, 15592400294397051589], [14950883590652370704, "time", false, 14729349321939450737], [16727543399706004146, "cookie", false, 16853601063074676329]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\cookie_store-c4f0eeb97db5fff0\\dep-lib-cookie_store", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}