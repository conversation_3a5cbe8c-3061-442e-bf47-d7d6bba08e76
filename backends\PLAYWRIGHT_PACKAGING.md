# Playwright打包解决方案

## 问题描述

在使用Nuitka打包包含Playwright的Python应用时，经常遇到以下错误：
```
playwright._impl._errors.Error: launch: Executable doesn't exist at D:\peoject\test\backends\out\MAIN~1.DIS\playwright\driver\package\.local-browsers\winldd-1007\PrintDeps.exe
```

这是因为Playwright需要浏览器驱动程序，但Nuitka默认打包时没有正确包含这些文件。

## 解决方案

### 1. 环境检查

首先运行环境检查脚本：
```bash
python check_playwright.py
```

这将检查：
- Playwright安装状态
- 浏览器安装状态
- 依赖包状态
- 功能测试

### 2. 代码修改

#### A. 更新conf.py配置
- 增强了浏览器路径检测逻辑
- 支持多种浏览器位置
- 添加了系统浏览器回退机制

#### B. 更新login.py配置
- 创建了统一的浏览器启动配置函数
- 支持指定可执行文件路径
- 添加了详细的日志输出

### 3. 打包方法

#### 方法一：使用改进的打包脚本
```bash
python build_with_playwright.py
```

这个脚本会：
- 自动检测Playwright安装位置
- 包含必要的数据文件
- 创建启动脚本
- 复制额外的资源文件

#### 方法二：手动Nuitka命令
```bash
nuitka --standalone --show-progress \
  --follow-import-to=utils,src,myUtils \
  --output-dir=out \
  --windows-icon-from-ico=logo.ico \
  --include-package=playwright \
  --include-package-data=playwright \
  --include-data-dir=playwright=playwright \
  --disable-dll-dependency-cache \
  --assume-yes-for-downloads \
  --mingw64 \
  main.py
```

### 4. 浏览器配置策略

应用会按以下优先级查找浏览器：

1. **系统Chrome浏览器**
   - `C:\Program Files\Google\Chrome\Application\chrome.exe`
   - `C:\Program Files (x86)\Google\Chrome\Application\chrome.exe`

2. **系统Edge浏览器**
   - `C:\Program Files\Microsoft\Edge\Application\msedge.exe`
   - `C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe`

3. **Playwright浏览器**
   - 打包目录中的浏览器文件
   - 用户目录中的Playwright浏览器

### 5. 部署建议

#### A. 推荐方案：使用系统浏览器
- 在目标机器上安装Chrome浏览器
- 应用会自动检测并使用系统浏览器
- 打包文件更小，兼容性更好

#### B. 完整打包方案
- 将Playwright浏览器包含在打包文件中
- 文件较大但完全独立
- 适合无法安装浏览器的环境

### 6. 故障排除

#### 常见问题1：浏览器未找到
**症状**：`Executable doesn't exist`错误
**解决**：
1. 检查目标机器是否安装Chrome
2. 运行`check_playwright.py`检查环境
3. 查看应用日志中的浏览器检测信息

#### 常见问题2：权限问题
**症状**：浏览器启动失败
**解决**：
1. 以管理员权限运行应用
2. 检查防火墙设置
3. 添加`--no-sandbox`参数

#### 常见问题3：路径问题
**症状**：路径包含中文或特殊字符
**解决**：
1. 将应用放在英文路径下
2. 避免路径中包含空格和特殊字符

### 7. 调试方法

#### 启用详细日志
在`conf.py`中的浏览器检测部分会输出详细信息：
```
[INFO] 打包环境检测，base_dir: D:\app
[DEBUG] 检查浏览器目录: D:\app\.local-browsers
[INFO] 找到系统浏览器: C:\Program Files\Google\Chrome\Application\chrome.exe
```

#### 手动测试浏览器
```python
from playwright.sync_api import sync_playwright

with sync_playwright() as p:
    browser = p.chromium.launch(
        executable_path="C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        headless=True
    )
    page = browser.new_page()
    page.goto("https://www.baidu.com")
    print(page.title())
    browser.close()
```

### 8. 性能优化

#### 减少启动时间
- 使用系统浏览器而不是打包浏览器
- 启用浏览器缓存
- 减少不必要的浏览器参数

#### 减少包大小
- 只包含必要的Playwright组件
- 使用系统浏览器
- 排除不需要的语言包

### 9. 最佳实践

1. **开发阶段**
   - 使用`playwright install`安装浏览器
   - 测试所有浏览器相关功能
   - 运行环境检查脚本

2. **打包阶段**
   - 使用改进的打包脚本
   - 测试打包后的应用
   - 验证浏览器功能正常

3. **部署阶段**
   - 在目标机器上安装Chrome
   - 测试应用启动和浏览器功能
   - 提供故障排除指南

### 10. 更新日志

#### v1.0.0
- 初始版本，基本的Playwright支持

#### v1.1.0
- 增强浏览器检测逻辑
- 添加系统浏览器支持
- 创建统一的启动配置

#### v1.2.0
- 添加环境检查脚本
- 改进打包脚本
- 增强错误处理和日志

### 11. 技术支持

如果遇到问题，请：
1. 运行`check_playwright.py`检查环境
2. 查看应用日志中的浏览器检测信息
3. 确认目标机器的浏览器安装状态
4. 检查防火墙和权限设置
