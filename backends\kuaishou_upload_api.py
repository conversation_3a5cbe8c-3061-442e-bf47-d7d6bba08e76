#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手视频上传API模块
重构自原始代码，修复了变量命名和逻辑问题
"""

import json
import os
import time
import urllib.request
import requests
from typing import Optional, Dict, Any


class KuaishouUploader:
    """快手视频上传器"""
    
    def __init__(self):
        self.chunk_size = 4194304  # 4MB 分片大小
        self.opener = urllib.request.build_opener()
        
    def get_upload_token(self, api_ph: str, cookie: str) -> Optional[str]:
        """
        获取上传token
        
        Args:
            api_ph: 快手API签名
            cookie: 用户cookie
            
        Returns:
            上传token或None
        """
        try:
            data = {
                'kuaishou.web.cp.api_ph': api_ph,
                'uploadType': '1',
            }
            
            headers = {
                'Connection': 'keep-alive',
                'Content-Type': 'application/json;charset=utf-8',
                'Pragma': 'no-cache',
                'Referer': 'https://cp.kuaishou.com/article/publish/video',
                'Host': 'cp.kuaishou.com',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Cookie': cookie
            }
            
            data_bytes = json.dumps(data).encode()
            ns_sig3 = self._get_ns_sig3(data_bytes)
            
            params = {
                'uploadType': '1',
                '__NS_sig3': ns_sig3
            }
            
            response = requests.post(
                'https://cp.kuaishou.com/rest/cp/works/v2/video/pc/upload/pre',
                headers=headers,
                params=params,
                data=data_bytes
            )
            
            result = response.json()
            upload_token = result['data']['token']
            
            # 恢复上传功能
            resume_url = f'https://upload.kuaishouzt.com/api/upload/resume?upload_token={upload_token}'
            req = urllib.request.Request(resume_url)
            self.opener.open(req)
            
            print(f'恢复上传功能成功 {time.strftime("%Y-%m-%d %H:%M:%S")}')
            return upload_token
            
        except Exception as e:
            print(f'恢复上传功能失败: {e} {time.strftime("%Y-%m-%d %H:%M:%S")}')
            return None
    
    def upload_video_file(self, api_ph: str, cookie: str, upload_token: str, 
                         video_path: str, title: str, description: str) -> str:
        """
        上传视频文件
        
        Args:
            api_ph: 快手API签名
            cookie: 用户cookie
            upload_token: 上传token
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            
        Returns:
            'uploadOK' 或 'uploadErr'
        """
        try:
            # 读取视频文件
            with open(video_path, 'rb') as f:
                video_data = f.read()
            
            file_size = len(video_data)
            filename = os.path.basename(video_path)
            
            # 计算分片数量
            chunk_count = (file_size + self.chunk_size - 1) // self.chunk_size
            
            # 上传分片
            self._upload_chunks(video_data, file_size, chunk_count, upload_token, cookie)
            
            # 完成分片上传
            self._complete_upload(chunk_count, upload_token)
            
            # 完成文件上传
            file_info = self._finish_upload(api_ph, cookie, upload_token, filename, file_size)
            
            # 发布视频
            self._publish_video(api_ph, cookie, file_info, description)
            
            print(f'发布成功 {time.strftime("%Y-%m-%d %H:%M:%S")}')
            return 'uploadOK'
            
        except Exception as e:
            print(f'上传过程中出现错误: {e}')
            return 'uploadErr'
    
    def _upload_chunks(self, video_data: bytes, file_size: int, chunk_count: int, 
                      upload_token: str, cookie: str):
        """上传视频分片"""
        for i in range(chunk_count):
            start_pos = i * self.chunk_size
            end_pos = min((i + 1) * self.chunk_size, file_size)
            chunk_data = video_data[start_pos:end_pos]
            chunk_size = len(chunk_data)
            
            content_range = f'bytes {start_pos}-{end_pos-1}/{file_size}'
            
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                'Connection': 'keep-alive',
                'Content-Length': str(chunk_size),
                'Content-Range': content_range,
                'Content-Type': 'application/octet-stream',
                'Host': 'upload.kuaishouzt.com',
                'Origin': 'https://cp.kuaishou.com',
                'Referer': 'https://cp.kuaishou.com/article/publish/video?origin=www.kuaishou.com',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Cookie': cookie
            }
            
            url = f'https://upload.kuaishouzt.com/api/upload/fragment?upload_token={upload_token}&fragment_id={i}'
            req = urllib.request.Request(url, data=chunk_data, headers=headers, method='POST')
            self.opener.open(req)
    
    def _complete_upload(self, chunk_count: int, upload_token: str):
        """完成分片上传"""
        headers = {
            'Host': 'upload.kuaishouzt.com',
            'Connection': 'keep-alive',
            'Content-Length': '0',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Origin': 'https://cp.kuaishou.com',
            'Referer': 'https://cp.kuaishou.com/article/publish/video',
        }
        
        url = f'https://upload.kuaishouzt.com/api/upload/complete?fragment_count={chunk_count}&upload_token={upload_token}'
        req = urllib.request.Request(url, method='POST', headers=headers)
        self.opener.open(req)
    
    def _finish_upload(self, api_ph: str, cookie: str, upload_token: str, 
                      filename: str, file_size: int) -> Dict[str, Any]:
        """完成文件上传并获取文件信息"""
        data = {
            'fileLength': str(file_size),
            'fileName': filename,
            'fileType': 'video/mp4',
            'kuaishou.web.cp.api_ph': api_ph,
            'token': upload_token
        }
        
        data_bytes = json.dumps(data).encode()
        headers = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://cp.kuaishou.com',
            'Referer': 'https://cp.kuaishou.com/article/publish/video',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Cookie': cookie
        }
        
        ns_sig3 = self._get_ns_sig3(data_bytes)
        params = {'__NS_sig3': ns_sig3}
        
        response = requests.post(
            'https://cp.kuaishou.com/rest/cp/works/v2/video/pc/upload/finish',
            headers=headers,
            params=params,
            data=data_bytes
        )
        
        result = response.json()
        print(f'视频已上传到cp服务器: {time.strftime("%Y-%m-%d %H:%M:%S")}')
        
        return {
            'fileId': result['data']['fileId'],
            'coverKey': result['data']['coverKey'],
            'mediaId': result['data']['mediaId'],
            'photoIdStr': result['data']['photoIdStr'],
            'videoDuration': result['data']['videoDuration']
        }
    
    def _publish_video(self, api_ph: str, cookie: str, file_info: Dict[str, Any], description: str):
        """发布视频"""
        data = {
            "activityIds": [],
            "allowSameFrame": "true",
            "caption": description,
            "chapters": [],
            "collectionId": "",
            "coverCropped": "false",
            "coverKey": file_info['coverKey'],
            "coverTimeStamp": 0,
            "coverTitle": "",
            "coverType": 1,
            "declareInfo": {},
            "disableNearbyShow": "false",
            "domain": "",
            "downloadType": 1,
            "fileId": file_info['fileId'],
            "kuaishou.web.cp.api_ph": api_ph,
            "latitude": "",
            "longitude": "",
            "mediaId": file_info['mediaId'],
            "movieId": "",
            "notifyResult": 0,
            "openPrePreview": "false",
            "photoStatus": 1,
            "photoType": 0,
            "pkCoverKey": "",
            "poiId": 0,
            "previewUrlErrorMessage": "",
            "profileCoverKey": "",
            "projectId": "",
            "publishTime": 0,
            "recTagIdList": [],
            "riseQuality": "false",
            "secondDomain": "",
            "triggerH265": "false",
            "videoDuration": file_info['videoDuration'],
            "videoInfoMeta": ""
        }
        
        data_bytes = json.dumps(data).encode()
        headers = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Content-Length': str(len(data_bytes)),
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://cp.kuaishou.com',
            'Referer': 'https://cp.kuaishou.com/article/publish/video',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Cookie': cookie
        }
        
        req = urllib.request.Request(
            'https://cp.kuaishou.com/rest/cp/works/v2/video/pc/submit',
            data=data_bytes,
            headers=headers,
            method='POST'
        )
        
        response = self.opener.open(req)
        result = response.read().decode('utf-8')
        print(result)
    
    def _get_ns_sig3(self, data: bytes) -> str:
        """
        生成NS_sig3签名
        注意：这里需要实现具体的签名算法
        """
        # TODO: 实现具体的签名算法
        # 这里需要根据快手的具体签名规则来实现
        return "placeholder_signature"


# 使用示例
def upload_video_to_kuaishou(video_path: str, title: str, description: str, 
                           api_ph: str, cookie: str) -> str:
    """
    上传视频到快手
    
    Args:
        video_path: 视频文件路径
        title: 视频标题
        description: 视频描述
        api_ph: 快手API签名
        cookie: 用户cookie
        
    Returns:
        上传结果
    """
    uploader = KuaishouUploader()
    
    # 获取上传token
    upload_token = uploader.get_upload_token(api_ph, cookie)
    if not upload_token:
        return 'uploadErr'
    
    # 上传视频
    result = uploader.upload_video_file(api_ph, cookie, upload_token, video_path, title, description)
    return result


if __name__ == "__main__":
    # 测试代码
    video_path = "test_video.mp4"
    title = "测试视频"
    description = "这是一个测试视频"
    api_ph = "your_api_ph_here"
    cookie = "your_cookie_here"
    
    result = upload_video_to_kuaishou(video_path, title, description, api_ph, cookie)
    print(f"上传结果: {result}")
