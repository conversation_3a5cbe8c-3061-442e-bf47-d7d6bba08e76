# Tauri Python Sidecar

This is a Vue.js application that uses <PERSON><PERSON> to create a desktop application with a Python backend. The application demonstrates how to:

- Use Vue 3 with TypeScript
- Integrate with <PERSON><PERSON>'s API
- Communicate with a Python backend
- Handle sidecar processes
- Manage application state
- Use Tailwind CSS for styling

## Prerequisites

- Node.js (v16 or later)
- Rust (latest stable)
- Python 3.8 or later
- Tauri CLI

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

## Development

To run the application in development mode:

```bash
npm run tauri dev
```

## Building

To build the application:

```bash
npm run tauri build
```

## Features

- Connect to a Python backend server
- Start/stop sidecar processes
- Mock API endpoint testing
- Real-time log display
- Fullscreen toggle (F11)
- Dark mode support
- Responsive design

## Project Structure

- `src/` - Vue application source code
  - `App.vue` - Main application component
  - `main.ts` - Application entry point
  - `style.css` - Global styles
- `src-tauri/` - Tauri backend code
- `public/` - Static assets

## License

MIT
