{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 12687594469746301899, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[1322478694103194923, "build_script_build", false, 11392127864548163429], [2069998946850810971, "tauri", false, 9812321337395020363], [2253952315205409758, "tauri_plugin_shell", false, 18117750467636573173], [3834743577069889284, "tauri_plugin_dialog", false, 4665213478223899529], [8324636962323428845, "serde_json", false, 7393924985922554751], [10967960060725374459, "serde", false, 14771690952189957965], [12316149723851658110, "command_group", false, 4481664513630527362], [13890802266741835355, "tauri_plugin_fs", false, 18294253574106967446], [15441187897486245138, "tauri_plugin_http", false, 17129032619342218338], [18057500336552735601, "sysinfo", false, 4063723584716310849]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\app-0a9e8cde3639f580\\dep-bin-app", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}