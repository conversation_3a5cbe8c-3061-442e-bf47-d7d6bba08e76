{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 18215636366103738014, "deps": [[1615478164327904835, "pin_utils", false, 18172120003148911296], [2877347214279964928, "pin_project_lite", false, 10431634156803823253], [6955678925937229351, "slab", false, 15761857979239432215], [7620660491849607393, "futures_core", false, 5227853956270100733], [10565019901765856648, "futures_macro", false, 3253201937772118744], [16240732885093539806, "futures_task", false, 11017736951729003241]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-9a494dca45f64009\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}