<template>
  <div v-if="isLoading" class="auth-loading">
    <el-loading-directive
      v-loading="true"
      element-loading-text="正在验证登录状态..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    />
  </div>
  <slot v-else />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { verifyTokenApi } from '@/api/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()
const isLoading = ref(true)

onMounted(async () => {
  try {
    // 如果没有token，直接跳转到登录页
    if (!userStore.isLoggedIn) {
      router.push('/login')
      return
    }

    // 验证token有效性
    await verifyTokenApi()
    
    // token有效，继续
    isLoading.value = false
  } catch (error) {
    console.error('Token验证失败:', error)
    
    // token无效，清除用户信息并跳转到登录页
    userStore.logout()
    router.push('/login')
  }
})
</script>

<style scoped>
.auth-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}
</style>
