<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端颜色传递测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .api-preview {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h2 style="text-align: center; color: #333; margin-bottom: 30px;">
                🎨 前端颜色传递测试
            </h2>
            
            <div class="test-section">
                <h3>封面标题设置</h3>
                
                <el-form label-width="120px">
                    <el-form-item label="封面标题">
                        <el-input
                            v-model="formData.coverTitle"
                            type="textarea"
                            :autosize="{ minRows: 2, maxRows: 3 }"
                            placeholder="请输入封面标题文字"
                        />
                    </el-form-item>
                    
                    <el-form-item label="字体大小">
                        <el-input-number
                            v-model="formData.coverFontSize"
                            :min="20"
                            :max="120"
                            :step="2"
                            style="width: 150px"
                        />
                        <span style="margin-left: 8px; color: #666;">px</span>
                    </el-form-item>
                    
                    <el-form-item label="字体颜色">
                        <el-color-picker
                            v-model="formData.coverFontColor"
                            show-alpha
                            :predefine="fontColorPresets"
                            @change="onColorChange"
                        />
                        <span style="margin-left: 10px; color: #666;">{{ formData.coverFontColor }}</span>
                    </el-form-item>
                </el-form>
            </div>
            
            <div class="test-section">
                <h3>实时预览</h3>
                <div style="text-align: center; padding: 20px; background: #333; border-radius: 6px;">
                    <div 
                        :style="{ 
                            fontSize: formData.coverFontSize + 'px', 
                            color: formData.coverFontColor,
                            textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                            lineHeight: (formData.coverFontSize * 1.5) + 'px',
                            fontWeight: 'bold'
                        }"
                    >
                        {{ formData.coverTitle || '示例标题文字\n支持多行显示' }}
                    </div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>API 请求数据预览</h3>
                <div class="api-preview">{{ apiDataPreview }}</div>
                
                <el-button 
                    type="primary" 
                    @click="testApiCall" 
                    style="margin-top: 15px;"
                    :loading="testing"
                >
                    测试 API 调用
                </el-button>
                
                <div v-if="apiResult" style="margin-top: 15px;">
                    <h4>API 响应:</h4>
                    <div class="api-preview">{{ apiResult }}</div>
                </div>
            </div>
            
            <div class="test-section">
                <h3>颜色格式转换测试</h3>
                <div v-for="(color, index) in testColors" :key="index" style="margin-bottom: 10px;">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div 
                            style="width: 30px; height: 30px; border: 1px solid #ddd; border-radius: 4px;"
                            :style="{ backgroundColor: color }"
                        ></div>
                        <span style="font-family: monospace; min-width: 100px;">{{ color }}</span>
                        <span style="color: #666;">→</span>
                        <span style="font-family: monospace; color: #2563eb;">{{ convertColor(color) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                const formData = ref({
                    coverTitle: '测试标题\n中文字体',
                    coverFontSize: 72,
                    coverFontColor: '#FFFF00'
                });
                
                const testing = ref(false);
                const apiResult = ref('');
                
                const fontColorPresets = [
                    "#FFFF00", // 黄色
                    "#FFFFFF", // 白色
                    "#FF0000", // 红色
                    "#00FF00", // 绿色
                    "#0000FF", // 蓝色
                    "#FF6600", // 橙色
                    "#FF00FF", // 紫色
                    "#00FFFF", // 青色
                    "#000000", // 黑色
                    "#808080", // 灰色
                ];
                
                const testColors = [
                    '#FF0000', '#00FF00', '#0000FF', '#FFFF00',
                    '#FF6600', '#FF00FFAA', 'red', 'yellow'
                ];
                
                const apiDataPreview = computed(() => {
                    return JSON.stringify({
                        coverTitle: formData.value.coverTitle,
                        coverFontSize: formData.value.coverFontSize,
                        coverFontColor: formData.value.coverFontColor,
                        // 其他参数...
                        urls: ["https://example.com/video"],
                        cookie: "test_cookie"
                    }, null, 2);
                });
                
                const convertColor = (color) => {
                    if (color.startsWith('#') && color.length === 7) {
                        return `0x${color.slice(1)}`;
                    } else if (color.startsWith('#') && color.length === 9) {
                        return `0x${color.slice(1)}`;
                    } else {
                        return color;
                    }
                };
                
                const onColorChange = (value) => {
                    console.log('颜色变化:', value);
                    ElMessage.success(`颜色已更改为: ${value}`);
                };
                
                const testApiCall = async () => {
                    testing.value = true;
                    apiResult.value = '';
                    
                    try {
                        // 模拟 API 调用
                        const requestData = {
                            coverTitle: formData.value.coverTitle,
                            coverFontSize: formData.value.coverFontSize,
                            coverFontColor: formData.value.coverFontColor,
                            urls: ["https://example.com/video"],
                            cookie: "test_cookie"
                        };
                        
                        console.log('发送 API 请求:', requestData);
                        
                        // 模拟网络延迟
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        
                        // 模拟成功响应
                        const response = {
                            code: 200,
                            msg: "测试成功",
                            data: {
                                received_coverFontColor: formData.value.coverFontColor,
                                converted_color: convertColor(formData.value.coverFontColor),
                                font_size: formData.value.coverFontSize,
                                title: formData.value.coverTitle
                            }
                        };
                        
                        apiResult.value = JSON.stringify(response, null, 2);
                        ElMessage.success('API 测试成功！');
                        
                    } catch (error) {
                        console.error('API 测试失败:', error);
                        apiResult.value = `错误: ${error.message}`;
                        ElMessage.error('API 测试失败');
                    } finally {
                        testing.value = false;
                    }
                };

                return {
                    formData,
                    testing,
                    apiResult,
                    fontColorPresets,
                    testColors,
                    apiDataPreview,
                    convertColor,
                    onColorChange,
                    testApiCall
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
