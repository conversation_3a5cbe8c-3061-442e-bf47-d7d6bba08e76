// 测试 utils 模块的功能
#[cfg(test)]
mod tests {
    use crate::commands::utils::*;
    use std::collections::HashMap;

    #[test]
    fn test_sig_py_functionality() {
        let mut query_params = HashMap::new();
        query_params.insert("test_key".to_string(), "test_value".to_string());
        
        let mut data_params = HashMap::new();
        data_params.insert("data_key".to_string(), "data_value".to_string());
        
        let result = sig_py(query_params, data_params);
        
        // 验证结果是32位十六进制字符串（MD5）
        assert_eq!(result.len(), 32);
        assert!(result.chars().all(|c| c.is_ascii_hexdigit()));
        
        println!("sig_py result: {}", result);
    }

    #[test]
    fn test_token_py_functionality() {
        let sig = "test_signature".to_string();
        let client_salt = "test_salt".to_string();
        
        let result = token_py(sig, client_salt);
        
        // 验证结果是64位十六进制字符串（SHA256）
        assert_eq!(result.len(), 64);
        assert!(result.chars().all(|c| c.is_ascii_hexdigit()));
        
        println!("token_py result: {}", result);
    }

    #[test]
    fn test_extract_signature_functionality() {
        // 测试正常情况
        let text_with_signature = "Some text ##extracted_content## more text".to_string();
        let result = extract_signature(text_with_signature);
        assert_eq!(result, Some("extracted_content".to_string()));
        
        // 测试没有匹配的情况
        let text_without_signature = "No signature markers here".to_string();
        let result_none = extract_signature(text_without_signature);
        assert_eq!(result_none, None);
        
        // 测试空字符串
        let empty_text = "".to_string();
        let result_empty = extract_signature(empty_text);
        assert_eq!(result_empty, None);
        
        println!("extract_signature tests passed");
    }

    #[test]
    fn test_validate_signature_functionality() {
        // 测试有效签名
        assert!(validate_signature("abcdef1234567890abcdef1234567890".to_string()));
        assert!(validate_signature("ABCDEF1234567890ABCDEF1234567890".to_string()));
        
        // 测试无效签名
        assert!(!validate_signature("invalid_chars_xyz".to_string()));
        assert!(!validate_signature("short".to_string()));
        assert!(!validate_signature("".to_string()));
        
        println!("validate_signature tests passed");
    }

    #[tokio::test]
    async fn test_get_sig3_error_handling() {
        // 测试无效参数
        let result = get_sig3("invalid_param".to_string()).await;
        
        // 应该返回错误结果（因为JAR文件可能不存在或Java不可用）
        if !result.success {
            println!("Expected error: {}", result.error.unwrap_or("Unknown error".to_string()));
        }
        
        // 这个测试主要验证函数不会崩溃
        assert!(true);
    }

    #[test]
    fn test_check_java_available_functionality() {
        let java_available = check_java_available();
        println!("Java available: {}", java_available);
        
        // 这个测试不做断言，因为Java可能在某些环境中不可用
        // 主要是验证函数能正常执行
        assert!(true);
    }

    #[test]
    fn test_get_jar_path_functionality() {
        let jar_path_result = get_jar_path();
        
        match jar_path_result {
            Ok(path) => println!("JAR path: {}", path),
            Err(error) => println!("JAR path error: {}", error),
        }
        
        // 这个测试不做断言，因为JAR文件可能不存在
        // 主要是验证函数能正常执行
        assert!(true);
    }

    #[tokio::test]
    async fn test_batch_get_sig3_functionality() {
        let params = vec![
            "param1".to_string(),
            "param2".to_string(),
            "param3".to_string(),
        ];
        
        let results = batch_get_sig3(params).await;
        
        // 验证返回的结果数量正确
        assert_eq!(results.len(), 3);
        
        // 验证每个结果都有正确的结构
        for (i, result) in results.iter().enumerate() {
            println!("Batch result {}: success={}, error={:?}", 
                     i, result.success, result.error);
        }
        
        assert!(true);
    }
}

// 集成测试函数，可以在实际环境中调用
pub async fn run_integration_tests() {
    println!("=== 开始 Utils 模块集成测试 ===");
    
    // 测试基本功能
    let mut query_params = HashMap::new();
    query_params.insert("user_id".to_string(), "12345".to_string());
    query_params.insert("timestamp".to_string(), "1640995200".to_string());
    
    let mut data_params = HashMap::new();
    data_params.insert("action".to_string(), "login".to_string());
    data_params.insert("device".to_string(), "mobile".to_string());
    
    let sig = sig_py(query_params, data_params);
    println!("生成的签名: {}", sig);
    
    let token = token_py(sig.clone(), "client_salt_123".to_string());
    println!("生成的令牌: {}", token);
    
    // 测试签名提取
    let test_output = format!("Java output: ##{}## end", sig);
    if let Some(extracted) = extract_signature(test_output) {
        println!("提取的签名: {}", extracted);
    }
    
    // 检查Java环境
    let java_ok = check_java_available();
    println!("Java 可用性: {}", java_ok);
    
    // 检查JAR文件路径
    match get_jar_path() {
        Ok(path) => println!("JAR 文件路径: {}", path),
        Err(e) => println!("JAR 文件路径错误: {}", e),
    }
    
    // 测试实际的sig3生成（如果Java和JAR文件可用）
    if java_ok {
        let sig3_result = get_sig3("test_parameter".to_string()).await;
        if sig3_result.success {
            println!("Sig3 生成成功: {}", sig3_result.signature.unwrap_or("None".to_string()));
        } else {
            println!("Sig3 生成失败: {}", sig3_result.error.unwrap_or("Unknown error".to_string()));
        }
    }
    
    println!("=== Utils 模块集成测试完成 ===");
}

use std::collections::HashMap;
use crate::commands::utils::*;
