# 登录系统迁移指南

## 概述

本文档记录了从旧的登录系统迁移到新的独立认证服务器系统的所有变更。

## 主要变更

### 1. 服务器分离
- **旧系统**: 认证和业务API使用同一服务器
- **新系统**: 认证API使用独立的认证服务器，业务API使用主应用服务器

### 2. 请求工具分离
- **旧系统**: 所有API请求使用 `src/utils/request.js`
- **新系统**: 
  - 认证API使用 `src/utils/authRequest.js`
  - 业务API继续使用 `src/utils/request.js`

### 3. API端点变更
- **旧系统**: 认证API在 `src/api/user.js` 中定义
- **新系统**: 认证API迁移到 `src/api/auth.js`

## 已清理的旧代码

### 1. `src/utils/request.js` 清理
**移除的内容**:
- 直接导入和使用 `useUserStore`
- 在请求函数中直接操作 userStore

**保留的内容**:
- 通过 localStorage 获取 token
- 401错误处理（清除localStorage并跳转登录页）

**修改前**:
```javascript
import { useUserStore } from '@/stores/user'

async function request({ url, method = 'GET', data = null, params = null, headers = {} }) {
  const userStore = useUserStore()
  
  if (userStore.userInfo.access_token) {
    headers['Authorization'] = `Bearer ${userStore.userInfo.access_token}`
  }
  
  // 401错误处理
  if (response.status === 401) {
    userStore.logout()
    // ...
  }
}
```

**修改后**:
```javascript
import { MAIN_API_CONFIG } from '@/config/api'

const getUserToken = () => {
  try {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    return userInfo.access_token || null
  } catch {
    return null
  }
}

async function request({ url, method = 'GET', data = null, params = null, headers = {} }) {
  const token = getUserToken()
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  // 401错误处理
  if (response.status === 401) {
    localStorage.removeItem('userInfo')
    window.location.hash = '#/login'
    window.location.reload()
  }
}
```

### 2. `src/api/user.js` 清理
**移除的方法**:
- `login(data)` - 迁移到 `src/api/auth.js` 的 `loginApi(cardKey)`
- `register(data)` - 认证服务器不需要注册功能
- `logout()` - 迁移到 `src/api/auth.js` 的 `logoutApi()`
- `refreshToken()` - 迁移到 `src/api/auth.js` 的 `refreshTokenApi(refreshToken)`

**添加的注释**:
```javascript
// 注意：登录、注册、登出、刷新token等认证相关功能
// 已迁移到独立的认证服务器，请使用 @/api/auth.js 中的对应方法
```

## 新增的文件

### 1. `src/utils/authRequest.js`
专门用于认证服务器的请求工具，特点：
- 使用独立的认证服务器URL
- 专门的错误处理逻辑
- 不依赖 userStore（避免循环依赖）

### 2. `src/api/auth.js`
认证相关的API定义：
- `loginApi(cardKey)` - 卡密登录
- `refreshTokenApi(refreshToken)` - 刷新token
- `logoutApi()` - 登出
- `verifyTokenApi()` - 验证token

### 3. `src/config/api.js`
统一的API配置文件：
- 主应用服务器配置
- 认证服务器配置
- API端点定义
- 请求配置

### 4. `src/views/Login.vue`
全新的登录页面：
- 现代化UI设计
- 卡密验证
- 错误处理
- 响应式布局

## 环境变量配置

### 新增的环境变量
```env
# 认证服务器地址
VITE_AUTH_BASE_URL=http://localhost:8080
```

### 开发环境 (`.env.development`)
```env
VITE_API_BASE_URL=http://localhost:5409
VITE_AUTH_BASE_URL=http://localhost:8080
```

### 生产环境 (`.env.production`)
```env
VITE_API_BASE_URL=http://localhost:5409
VITE_AUTH_BASE_URL=https://auth.yourdomain.com
```

## 路由变更

### 新增路由
- `/login` - 登录页面

### 路由守卫
- 所有业务页面需要登录认证
- 未登录自动跳转到登录页
- 已登录访问登录页自动跳转首页

## 状态管理变更

### `src/stores/user.js` 增强
- 自动token刷新机制
- localStorage持久化
- 定时检查token状态
- 自动登出处理

## API调用方式变更

### 认证相关API
**旧方式**:
```javascript
import { userApi } from '@/api/user'

// 登录
await userApi.login({ username, password })

// 登出
await userApi.logout()
```

**新方式**:
```javascript
import { loginApi, logoutApi } from '@/api/auth'

// 登录
await loginApi(cardKey)

// 登出
await logoutApi()
```

### 业务API
**保持不变**:
```javascript
import { http } from '@/utils/request'

// 业务API调用方式不变
await http.get('/api/business-endpoint')
```

## 兼容性说明

### 向后兼容
- 所有业务API调用方式保持不变
- 现有的业务逻辑无需修改
- token认证机制保持一致

### 不兼容变更
- 认证API端点地址变更
- 登录参数从用户名密码改为卡密
- 认证服务器地址需要单独配置

## 测试检查清单

### 功能测试
- [ ] 登录页面正常显示
- [ ] 卡密验证正常工作
- [ ] 登录成功后跳转首页
- [ ] 未登录访问业务页面自动跳转登录页
- [ ] 已登录访问登录页自动跳转首页
- [ ] Token自动刷新正常工作
- [ ] 登出功能正常工作
- [ ] 401错误自动处理

### 网络测试
- [ ] 认证服务器连接正常
- [ ] 主应用服务器连接正常
- [ ] 网络错误处理正常
- [ ] 超时处理正常

### 数据测试
- [ ] 用户信息正确保存到localStorage
- [ ] 页面刷新后登录状态保持
- [ ] Token过期后自动清除
- [ ] 多标签页登录状态同步

## 故障排除

### 常见问题
1. **认证服务器连接失败**
   - 检查 `VITE_AUTH_BASE_URL` 配置
   - 确认认证服务器正常运行

2. **Token刷新失败**
   - 检查refresh_token是否有效
   - 确认认证服务器刷新接口正常

3. **登录后立即跳转到登录页**
   - 检查token格式是否正确
   - 确认localStorage存储正常

4. **业务API调用失败**
   - 检查token是否正确添加到请求头
   - 确认主应用服务器正常运行

### 调试方法
1. 查看浏览器控制台日志
2. 检查Network面板的请求详情
3. 查看localStorage中的用户信息
4. 确认环境变量配置正确
