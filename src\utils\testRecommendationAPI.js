// 测试推荐 API 的独立脚本
import { invoke } from '@tauri-apps/api/core';

/**
 * 测试推荐 API - 使用 test-7-31.py 的逻辑和 Rust utils 方法
 * @param {Object} testData - 测试数据
 * @returns {Promise<Object>} - API 响应结果
 */
export async function testRecommendationAPIStandalone(testData = {}) {
  try {
    console.log("开始测试商品推荐接口...");

    // 默认测试数据
    const defaultData = {
      photoId: '5216857383469532746',
      userId: '4808775036',
      pageSource: '0',
      displayType: '3',
      client_key: '3c2cd3f3'
    };

    // 合并测试数据
    const data = { ...defaultData, ...testData };

    // 根据 test-7-31.py 设置请求头
    const headers = {
      "User-Agent": "kwai-android aegon/3.18.0",
      "Content-Type": "application/x-www-form-urlencoded",
      "Accept-Language": "zh-cn",
      "X-REQUESTID": "175137438817733451",
      "X-Client-Info": "model=SM-S9260;os=Android;nqe-score=34;network=WIFI;signal-strength=4;"
    };

    // 根据 test-7-31.py 设置 cookies
    const cookies = {
      "kuaishou.api_st": "Cg9rdWFpc2hvdS5hcGkuc3QSoAGyQr5_Pxnjcm0SZq8ikMsn3acDSjCQZqq-p9vfzoLnXW5OGHXH4G9LEhXfIKI_LJk2KNPmZYT1tt1l4Q_zET5jJK7sXwoDbjdCA-_YVZ38bHJiMNWYuJyYnkKqrlsRV5IpFHK0MjvPvh1N5Te-DQCl8aFWUf-j7ClLSBwHDnUaCdtfmQ4DQOHfvAt6FemPsnhlecbJEIrxMr0weFsMKR_JGhJvFHrrKslACKKghHE7NlCD-YgiIHn9vQ8gP9PW2smphT9q6vKdBy6RfR4ti1k4lGgSX5NeKAUwAQ",
      "token": "6314caebe22b42dbba9b4185beebffd3-4879352811"
    };

    // 根据 test-7-31.py 设置查询参数
    const params = {
      "earphoneMode": "1",
      "mod": "Samsung(SM-S9260)",
      "appver": "10.10.10.28300",
      "isp": "CMCC",
      "language": "zh-cn",
      "ud": "3166429095",
      "did_tag": "0",
      "egid": "DFPE6BFB11F8AF9B59EF311E914B3763C6243FCEF5A4D5F36E23934860814118",
      "net": "WIFI",
      "kcv": "1599",
      "app": "0",
      "kpf": "ANDROID_PHONE",
      "bottom_navigation": "false",
      "ver": "10.10",
      "oDid": "ANDROID_752b3dcfda26f942",
      "android_os": "0",
      "boardPlatform": "aosp-user",
      "kpn": "KUAISHOU",
      "androidApiLevel": "28",
      "newOc": "ALI_CPD,666",
      "slh": "0",
      "country_code": "cn",
      "nbh": "0",
      "hotfix_ver": "",
      "did_gt": "1751320183207",
      "keyconfig_state": "2",
      "cdid_tag": "5",
      "sys": "ANDROID_9",
      "max_memory": "192",
      "cold_launch_time_ms": "1751373895175",
      "oc": "ALI_CPD,666",
      "sh": "1920",
      "ddpi": "480",
      "deviceBit": "0",
      "browseType": "4",
      "socName": "Qualcomm MSM8998",
      "is_background": "0",
      "c": "ALI_CPD,666",
      "sw": "1080",
      "ftt": "",
      "abi": "arm32",
      "userRecoBit": "0",
      "device_abi": "",
      "totalMemory": "3945",
      "grant_browse_type": "AUTHORIZED",
      "iuid": "",
      "rdid": "ANDROID_5f011e02f6f2798b",
      "sbh": "72",
      "darkMode": "false",
      "did": "7221BE95-A362-460A-A2C3-AA0E8498A79D"
    };

    console.log("开始生成签名...");
    
    // 1. 使用 Rust utils 生成 MD5 签名
    const sig = await invoke('sig_py', { queryParams: params, dataParams: data });
    console.log('生成的 sig:', sig);

    // 2. 构建 encurl 用于生成 sig3
    const encurl = '/rest/n/feed/selection/profile/position' + sig;
    console.log('构建的 encurl:', encurl);

    // 3. 使用 JAR 文件生成 sig3
    const sig3Result = await invoke('get_sig3', { param: encurl });
    console.log('sig3 生成结果:', sig3Result);
    
    if (!sig3Result.success) {
      throw new Error(`生成 sig3 失败: ${sig3Result.error}`);
    }
    const sig3 = sig3Result.signature;

    // 4. 生成 token
    const client_salt = '808e3d5a9fe2cf0e7342c0151d2fab85';
    const token = await invoke('token_py', { sig: sig, clientSalt: client_salt });
    console.log('生成的 token:', token);

    // 5. 将生成的签名添加到参数中
    params['sig'] = sig;
    params['__NS_sig3'] = sig3;
    params['__NStokensig'] = token;

    // 6. 构建请求
    const url = "https://api3.ksapisrv.com/rest/n/feed/selection/profile/position";
    const queryString = new URLSearchParams(params).toString();
    const fullUrl = `${url}?${queryString}`;
    const requestBody = new URLSearchParams(data);

    // 构建 Cookie 字符串
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    console.log("发送请求到:", fullUrl);
    console.log("请求体:", requestBody.toString());

    // 7. 发送请求
    const response = await fetch(fullUrl, {
      method: "POST",
      headers: {
        ...headers,
        "Cookie": cookieString
      },
      body: requestBody.toString()
    });

    console.log("响应状态:", response.status);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
    }

    const responseData = await response.json();
    console.log("API 响应数据:", responseData);

    return {
      success: true,
      status: response.status,
      data: responseData,
      signatures: {
        sig,
        sig3,
        token
      },
      requestInfo: {
        url: fullUrl,
        body: requestBody.toString(),
        headers,
        cookies
      }
    };

  } catch (error) {
    console.error("测试推荐 API 失败:", error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
}

/**
 * 验证 Rust utils 方法的功能
 */
export async function validateRustUtils() {
  try {
    console.log("开始验证 Rust utils 方法...");

    // 测试数据
    const testQueryParams = {
      "user_id": "12345",
      "timestamp": "1640995200"
    };
    
    const testDataParams = {
      "action": "login",
      "device": "mobile"
    };

    // 1. 测试 sig_py
    console.log("测试 sig_py...");
    const sig = await invoke('sig_py', { 
      queryParams: testQueryParams, 
      dataParams: testDataParams 
    });
    console.log("sig_py 结果:", sig);

    // 2. 测试 token_py
    console.log("测试 token_py...");
    const token = await invoke('token_py', { 
      sig: sig, 
      clientSalt: "test_salt" 
    });
    console.log("token_py 结果:", token);

    // 3. 测试 extract_signature
    console.log("测试 extract_signature...");
    const extracted = await invoke('extract_signature', { 
      text: "Java output: ##test_signature_123## end" 
    });
    console.log("extract_signature 结果:", extracted);

    // 4. 测试 Java 环境检查
    console.log("测试 Java 环境检查...");
    const javaAvailable = await invoke('check_java_available');
    console.log("Java 可用性:", javaAvailable);

    if (javaAvailable) {
      // 5. 测试 JAR 路径获取
      console.log("测试 JAR 路径获取...");
      try {
        const jarPath = await invoke('get_jar_path');
        console.log("JAR 路径:", jarPath);
      } catch (error) {
        console.warn("JAR 路径获取失败:", error);
      }

      // 6. 测试 get_sig3
      console.log("测试 get_sig3...");
      const sig3Result = await invoke('get_sig3', { param: "test_parameter" });
      console.log("get_sig3 结果:", sig3Result);
    } else {
      console.warn("Java 不可用，跳过 JAR 相关测试");
    }

    return {
      success: true,
      results: {
        sig,
        token,
        extracted,
        javaAvailable
      }
    };

  } catch (error) {
    console.error("验证 Rust utils 失败:", error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 导出默认函数
export default {
  testRecommendationAPIStandalone,
  validateRustUtils
};
