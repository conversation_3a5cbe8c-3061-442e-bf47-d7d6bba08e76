#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版本的 Nuitka 打包脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def build_with_nuitka():
    """使用修复参数的 Nuitka 打包"""
    
    # 确保在正确的目录
    script_dir = Path(__file__).parent.absolute()
    os.chdir(script_dir)
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python 可执行文件: {sys.executable}")
    
    # 修复版本的 Nuitka 命令
    nuitka_cmd = [
        sys.executable, "-m", "nuitka",
        "--standalone",
        "--onefile",
        "--output-dir=out2",  # 使用新的输出目录
        "--output-filename=xiaochao-server.exe",
        
        # 修复 DLL 检测问题的参数
        "--assume-yes-for-downloads",
        "--disable-dll-dependency-cache",  # 禁用 DLL 依赖缓存
        "--force-dll-dependency-cache-update",  # 强制更新 DLL 依赖缓存
        
        # 包含必要的数据文件
        "--include-data-dir=cookies=cookies",
        "--include-data-dir=cookiesFile=cookiesFile", 
        "--include-data-dir=videoFile=videoFile",
        "--include-data-dir=logs=logs",
        "--include-data-dir=ffmpeg=ffmpeg",
        "--include-data-dir=utils=utils",
        "--include-data-file=database.db=database.db",
        
        # 包含 Playwright 相关文件
        "--include-package=playwright",
        "--include-package-data=playwright",
        
        # 其他必要的包
        "--include-package=flask",
        "--include-package=flask_cors",
        "--include-package=requests",
        "--include-package=loguru",
        # 暂时移除 lxml 以避免 Nuitka bug
        # "--include-package=lxml",
        
        # 优化参数
        "--enable-plugin=anti-bloat",
        "--show-progress",
        "--show-memory",
        
        # 避免某些已知问题的参数
        "--disable-ccache",  # 禁用 ccache
        "--jobs=1",  # 使用单线程编译，避免并发问题
        
        "main.py"
    ]
    
    print("开始 Nuitka 打包...")
    print("命令:", " ".join(nuitka_cmd))
    print("-" * 50)
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'  # 设置 Python IO 编码
        env['PYTHONLEGACYWINDOWSSTDIO'] = '1'  # 使用传统 Windows stdio
        
        result = subprocess.run(
            nuitka_cmd,
            cwd=script_dir,
            env=env,
            capture_output=False,  # 直接显示输出
            text=True
        )
        
        if result.returncode == 0:
            print("\n" + "=" * 50)
            print(" Nuitka 打包成功!")
            
            # 检查输出文件
            output_dir = script_dir / "out2"
            if output_dir.exists():
                print(f"输出目录: {output_dir}")
                for item in output_dir.iterdir():
                    print(f"  - {item.name}")
            
        else:
            print(f"\n Nuitka 打包失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f" 打包过程中发生错误: {e}")
        return False
    
    return True

def clean_previous_builds():
    """清理之前的构建文件"""
    script_dir = Path(__file__).parent.absolute()
    
    # 要清理的目录和文件
    cleanup_items = [
        "out2",
        "main.build",
        "main.dist", 
        "main.onefile-build",
        "nuitka-crash-report.xml"
    ]
    
    print("清理之前的构建文件...")
    for item_name in cleanup_items:
        item_path = script_dir / item_name
        if item_path.exists():
            if item_path.is_dir():
                import shutil
                shutil.rmtree(item_path)
                print(f"  删除目录: {item_name}")
            else:
                item_path.unlink()
                print(f"  删除文件: {item_name}")

if __name__ == "__main__":
    print("=" * 50)
    print("修复版本的 Nuitka 打包脚本")
    print("=" * 50)
    
    # 清理之前的构建
    clean_previous_builds()
    
    # 开始打包
    success = build_with_nuitka()
    
    if success:
        print("\n 打包完成!")
    else:
        print("\n 打包失败，请检查错误信息")
        sys.exit(1)
