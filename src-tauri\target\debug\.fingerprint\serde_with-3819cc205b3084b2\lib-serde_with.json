{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5290030462671737236, "path": 2907578704194751729, "deps": [[6998442642502920999, "serde_derive", false, 49958301967797884], [10967960060725374459, "serde", false, 15592400294397051589], [11754224277704155611, "serde_with_macros", false, 5603782766577511892]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-3819cc205b3084b2\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}