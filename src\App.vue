<template>
  <div id="app">
    <!-- 登录页面 -->
    <div v-if="!showMainLayout" class="login-wrapper">
      <router-view />
    </div>

    <!-- 主应用布局 -->
    <el-container v-else>
      <el-aside :width="isCollapse ? '64px' : '200px'">
        <div class="sidebar">
          <div class="logo">
            <img
              v-show="isCollapse"
              src="./assets/logo.svg"
              alt="Logo"
              class="logo-img"
            />
            <h2 v-show="!isCollapse">小超媒体运营系统</h2>
          </div>
          <el-menu
            :router="true"
            :default-active="activeMenu"
            :collapse="isCollapse"
            class="sidebar-menu"
            background-color="var(--el-bg-color-overlay, #001529)"
            text-color="var(--el-menu-text-color, #fff)"
            active-text-color="var(--el-color-primary, #7C3AED)"
            :style="{
              '--el-menu-active-bg-color': 'rgba(124,58,237,0.15)',
            }"
          >
            <el-menu-item index="/">
              <el-icon><HomeFilled /></el-icon>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/account-management">
              <el-icon><User /></el-icon>
              <span>账号管理</span>
            </el-menu-item>
            <el-menu-item index="/material-management">
              <el-icon><Picture /></el-icon>
              <span>素材管理</span>
            </el-menu-item>
            <el-menu-item index="/publish-center">
              <el-icon><Upload /></el-icon>
              <span>发布中心</span>
            </el-menu-item>
            <!-- <el-menu-item index="/website">
              <el-icon><Monitor /></el-icon>
              <span>网站</span>
            </el-menu-item>
            <el-menu-item index="/data">
              <el-icon><DataAnalysis /></el-icon>
              <span>数据</span>
            </el-menu-item> -->
          </el-menu>
        </div>
      </el-aside>
      <el-container>
        <el-header>
          <div class="header-content">
            <div class="header-left">
              <el-icon class="toggle-sidebar" @click="toggleSidebar"
                ><Fold
              /></el-icon>
            </div>
            <div class="header-right">
              <el-dropdown @command="handleUserCommand">
                <span class="user-info">
                  <el-icon><User /></el-icon>
                  <span class="username">{{ userStore.userInfo.user_info?.name || '用户' }}</span>
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="logout">
                      <el-icon><SwitchButton /></el-icon>
                      退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>
        <el-main class="main-content">
          <router-view />
        </el-main>
        <el-footer height="auto">
          <!-- LogPanel 移到 Teleport 中 -->
        </el-footer>
      </el-container>
    </el-container>

    <!-- 使用 Teleport 将 LogPanel 挂载到 body 下，确保不被 loading 遮盖 -->
    <Teleport to="body" v-if="showMainLayout">
      <LogPanel />
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, Teleport } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  HomeFilled,
  User,
  Monitor,
  DataAnalysis,
  Fold,
  Picture,
  Upload,
  ArrowDown,
  SwitchButton,
} from "@element-plus/icons-vue";
import { addListener, launch } from "devtools-detector";
import { startSidecar } from "@/utils/sidecar";
import { invoke } from "@tauri-apps/api/core";
import LogPanel from "@/components/LogPanel.vue";
import { useUserStore } from "@/stores/user";

if (import.meta.env.DEV !== true) {
  console.warn("开发模式下，右键菜单已被禁用。请在生产环境中使用。");
  document.addEventListener("contextmenu", (event) => event.preventDefault());
  addListener(async (isOpen) =>
    isOpen ? await invoke("close_main_window") : ""
  );
  launch();
}

onMounted(async () => {
  startSidecar();

  // 初始化用户信息
  const userStore = useUserStore();
  userStore.restoreUserInfo();
});

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 当前激活的菜单项
const activeMenu = computed(() => {
  return route.path;
});

// 侧边栏折叠状态
const isCollapse = ref(false);

// 计算属性：是否显示主布局（非登录页）
const showMainLayout = computed(() => {
  return route.name !== 'Login';
});

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value;
};

// 处理用户下拉菜单命令
const handleUserCommand = (command) => {
  switch (command) {
    case 'logout':
      handleLogout();
      break;
  }
};

// 处理登出
const handleLogout = async () => {
  try {
    userStore.logout();
    ElMessage.success('已退出登录');
    // 跳转到登录页
    router.push('/login');
  } catch (error) {
    console.error('登出失败:', error);
    ElMessage.error('登出失败');
  }
};


</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

#app {
  min-height: 100vh;
}

.el-container {
  height: 100vh;
}

.el-aside {
  background-color: var(--el-bg-color-overlay, #001529);
  color: var(--el-menu-text-color, #fff);
  height: 100vh;
  overflow: hidden;
  transition: width 0.3s;

  .sidebar {
    display: flex;
    flex-direction: column;
    height: 100%;

    .logo {
      height: 60px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      background-color: var(--el-bg-color, #002140);
      overflow: hidden;

      .logo-img {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }

      h2 {
        color: var(--el-menu-text-color, #fff);
        font-size: 16px;
        font-weight: 600;
        white-space: nowrap;
        margin: 0;
      }
    }

    .sidebar-menu {
      border-right: none;
      flex: 1;

      // 高亮菜单项背景色
      :deep(.el-menu-item.is-active) {
        background-color: var(
          --el-menu-active-bg-color,
          rgba(124, 58, 237, 0.15)
        ) !important;
      }

      // 悬浮菜单项背景色
      :deep(.el-menu-item:hover) {
        background-color: rgba(124, 58, 237, 0.1) !important;
        color: var(--el-color-primary, #7c3aed) !important;
      }

      .el-menu-item {
        display: flex;
        align-items: center;

        .el-icon {
          margin-right: 10px;
          font-size: 18px;
        }
      }
    }
  }
}

.el-footer {
  background-color: #f2f3f5;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0;
  height: 0;
}
.el-header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0;
  height: 60px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 16px;

    .header-left {
      .toggle-sidebar {
        font-size: 20px;
        cursor: pointer;
        color: $text-regular;

        &:hover {
          color: $primary-color;
        }
      }
    }

    .header-right {
      .user-dropdown {
        display: flex;
        align-items: center;
        cursor: pointer;

        .username {
          margin: 0 8px;
          color: $text-regular;
        }

        .el-icon {
          font-size: 12px;
          color: $text-secondary;
        }
      }
    }
  }
}

.el-main {
  background-color: $bg-color-page;
  padding: 20px;
  padding-bottom: 80px; /* 为 LogPanel 预留空间 */
  overflow-y: auto;
}

.main-content {
  margin-bottom: 40px;
}

/* 登录页面样式 */
.login-wrapper {
  width: 100%;
  height: 100vh;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #333;
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.username {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}
</style>
