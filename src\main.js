import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './stores'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './styles/index.scss'
// 引入 Element Plus 中文语言包
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 引入 Element Plus 主题定制
import 'element-plus/theme-chalk/dark/css-vars.css' // 可选：暗色主题变量

const app = createApp(App)

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 通过 CSS 变量自定义主题色（推荐 Element Plus 2.x 及以上）
const style = document.createElement('style')
style.innerHTML = `
  :root {
    --el-color-primary: #7C3AED;
    --el-color-success: #22D3EE;
    --el-color-warning: #FBBF24;
    --el-color-danger:  #F43F5E;
    --el-color-info:    #6366F1;
  }
  [data-theme="dark"] {
    --el-color-primary: #7C3AED;
    --el-color-success: #22D3EE;
    --el-color-warning: #FBBF24;
    --el-color-danger:  #F43F5E;
    --el-color-info:    #6366F1;
  }
`
document.head.appendChild(style)

app.use(ElementPlus, {
  locale: zhCn,
})
app.use(router)
app.use(pinia)
app.mount('#app')