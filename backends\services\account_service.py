"""
账号管理服务模块
处理所有与用户账号相关的业务逻辑
"""

import sqlite3
import logging
from pathlib import Path
from flask import jsonify
import asyncio

from myUtils.auth import check_cookie
from conf import COOKIES_DIR


class AccountService:
    """账号管理服务类"""
    
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        self.db_path = self.base_dir / "database.db"
    
    def get_valid_accounts(self, check_cookie_param='0'):
        """
        获取有效账号列表，可选择是否校验Cookie
        """
        do_check_cookie = check_cookie_param == '1'
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row  # 允许通过字段名访问
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM user_info')
                rows = cursor.fetchall()
                data = []
                
                logging.info("\n📋 当前数据表内容：")
                for row in rows:
                    logging.info(dict(row))
                    row = dict(row)
                    
                    if do_check_cookie:
                        # 检查 cookie
                        try:
                            # 尝试获取当前事件循环
                            loop = asyncio.get_event_loop()
                            if loop.is_running():
                                # 如果事件循环正在运行，创建新的线程来运行异步函数
                                import concurrent.futures
                                import threading
                                
                                def run_in_thread():
                                    new_loop = asyncio.new_event_loop()
                                    asyncio.set_event_loop(new_loop)
                                    try:
                                        return new_loop.run_until_complete(check_cookie(row["type"], row["filePath"]))
                                    finally:
                                        new_loop.close()
                                
                                with concurrent.futures.ThreadPoolExecutor() as executor:
                                    future = executor.submit(run_in_thread)
                                    flag = future.result()
                            else:
                                # 如果事件循环没有运行，可以直接使用 asyncio.run
                                flag = asyncio.run(check_cookie(row["type"], row["filePath"]))
                        except RuntimeError:
                            # 如果没有事件循环，创建新的
                            flag = asyncio.run(check_cookie(row["type"], row["filePath"]))
                        except Exception as e:
                            logging.error(f"检查 cookie 时出错: {e}")
                            flag = False
                        
                        # 如果无效，更新 status 字段
                        if not flag:
                            cursor.execute(
                                'UPDATE user_info SET status = ? WHERE id = ?',
                                (0, row["id"])
                            )
                            conn.commit()
                            row["status"] = 0
                    data.append(row)
                
                return {
                    "success": True,
                    "code": 200,
                    "msg": None,
                    "data": data
                }
        except Exception as e:
            logging.error(f"获取有效账号失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }
    
    def get_all_accounts(self):
        """
        获取所有账号信息，不校验 cookie 是否有效
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM user_info')
                rows = cursor.fetchall()
                data = [dict(row) for row in rows]
                
                return {
                    "success": True,
                    "code": 200,
                    "msg": None,
                    "data": data
                }
        except Exception as e:
            logging.error(f"获取所有账号失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }
    
    def get_account_with_cookie(self):
        """
        获取一条账号信息，不校验 cookie 是否有效，但返回 cookie 文件内容
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM user_info LIMIT 1')
                row = cursor.fetchone()

                if not row:
                    return {
                        "success": True,
                        "code": 200,
                        "msg": "没有找到账号信息",
                        "data": None
                    }

                row_dict = dict(row)

                # 读取 cookie 文件内容
                cookie_content = None
                if row_dict.get("filePath"):
                    cookie_file_path = COOKIES_DIR / row_dict["filePath"]
                    try:
                        if cookie_file_path.exists():
                            with open(cookie_file_path, 'r', encoding='utf-8') as f:
                                cookie_content = f.read().strip()
                        else:
                            logging.warning(f"Cookie 文件不存在: {cookie_file_path}")
                    except Exception as e:
                        logging.error(f"读取 cookie 文件失败: {cookie_file_path}, 错误: {e}")
                        cookie_content = None

                # 添加 cookie 内容到返回数据
                row_dict["cookieContent"] = cookie_content

                return {
                    "success": True,
                    "code": 200,
                    "msg": None,
                    "data": row_dict
                }

        except Exception as e:
            logging.error(f"获取账号Cookie信息失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }
    
    def get_kuaishou_accounts_with_cookie(self):
        """
        获取快手账号（type=4）并且启用了自动发布（auto_publish=1）的账号，包含 cookie 内容
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM user_info WHERE type = ? AND auto_publish = ?', (4, 1))
                rows = cursor.fetchall()
                
                accounts_with_cookie = []
                
                for row in rows:
                    row_dict = dict(row)
                    
                    # 读取 cookie 文件内容
                    cookie_content = ''
                    if row_dict.get("filePath"):
                        cookie_file_path = COOKIES_DIR / row_dict["filePath"]
                        try:
                            if cookie_file_path.exists():
                                with open(cookie_file_path, 'r', encoding='utf-8') as f:
                                    file_content = f.read().strip()
                                    # 如果是 JSON 格式的 cookie 文件，需要解析提取 cookie 字符串
                                    if file_content.startswith('{'):
                                        import json
                                        cookie_data = json.loads(file_content)
                                        if 'cookies' in cookie_data:
                                            # 将 cookies 转换为 cookie 字符串格式
                                            cookie_parts = []
                                            for cookie_item in cookie_data['cookies']:
                                                cookie_parts.append(f"{cookie_item['name']}={cookie_item['value']}")
                                            cookie_content = '; '.join(cookie_parts)
                                        else:
                                            cookie_content = file_content
                                    else:
                                        cookie_content = file_content
                            else:
                                logging.warning(f"Cookie 文件不存在: {cookie_file_path}")
                        except Exception as e:
                            logging.error(f"读取 cookie 文件失败: {cookie_file_path}, 错误: {e}")
                            cookie_content = ''
                    
                    # 添加 cookie 内容到返回数据
                    row_dict["cookieContent"] = cookie_content
                    accounts_with_cookie.append(row_dict)
                
                return {
                    "success": True,
                    "code": 200,
                    "msg": None,
                    "data": accounts_with_cookie
                }
                
        except Exception as e:
            logging.error(f"获取快手账号Cookie信息失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }
    
    def delete_account(self, account_id):
        """
        删除账号
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 查询要删除的记录
                cursor.execute("SELECT * FROM user_info WHERE id = ?", (account_id,))
                record = cursor.fetchone()

                if not record:
                    return {
                        "success": False,
                        "code": 404,
                        "msg": "account not found",
                        "data": None
                    }

                record = dict(record)

                # 删除数据库记录
                cursor.execute("DELETE FROM user_info WHERE id = ?", (account_id,))
                conn.commit()

                return {
                    "success": True,
                    "code": 200,
                    "msg": "account deleted successfully",
                    "data": None
                }

        except Exception as e:
            logging.error(f"删除账号失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }
    
    def update_userinfo(self, user_id, type_value, user_name=None):
        """
        更新用户信息
        """
        try:
            if not user_id:
                return {
                    "success": False,
                    "code": 400,
                    "msg": "用户ID不能为空",
                    "data": None
                }

            db_path = Path(self.base_dir / "database.db")
            with sqlite3.connect(db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 根据 userName 是否传递决定更新哪些字段
                if user_name is not None:
                    cursor.execute('''
                        UPDATE user_info
                        SET type = ?,
                            userName = ?
                        WHERE id = ?;
                    ''', (type_value, user_name, user_id))
                else:
                    cursor.execute('''
                        UPDATE user_info
                        SET type = ?
                        WHERE id = ?;
                    ''', (type_value, user_id))
                
                conn.commit()
                
                if cursor.rowcount == 0:
                    return {
                        "success": False,
                        "code": 404,
                        "msg": "用户不存在",
                        "data": None
                    }

            logging.info(f"用户信息更新成功: ID={user_id}, type={type_value}, userName={user_name}")
            return {
                "success": True,
                "code": 200,
                "msg": "account update successfully",
                "data": None
            }
            
        except Exception as e:
            logging.error(f"更新用户信息失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": "update failed!",
                "data": None
            }

    def update_auto_publish(self, user_id, auto_publish):
        """
        更新账号的自动发布状态
        """
        try:
            if not user_id:
                return {
                    "success": False,
                    "code": 400,
                    "msg": "用户ID不能为空",
                    "data": None
                }

            # 确保 auto_publish 是有效的值 (0 或 1)
            if auto_publish not in [0, 1]:
                return {
                    "success": False,
                    "code": 400,
                    "msg": "自动发布状态值无效，必须为0或1",
                    "data": None
                }

            db_path = Path(self.base_dir / "database.db")
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE user_info
                    SET auto_publish = ?
                    WHERE id = ?;
                ''', (auto_publish, user_id))
                
                conn.commit()
                
                if cursor.rowcount == 0:
                    return {
                        "success": False,
                        "code": 404,
                        "msg": "用户不存在",
                        "data": None
                    }

            logging.info(f"账号自动发布状态更新成功: ID={user_id}, auto_publish={auto_publish}")
            return {
                "success": True,
                "code": 200,
                "msg": "账号自动发布状态更新成功",
                "data": None
            }
            
        except Exception as e:
            logging.error(f"更新账号自动发布状态失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": "更新失败",
                "data": None
            }


# 便捷函数供路由调用
def get_valid_accounts_handler(base_dir, check_cookie_param='0'):
    """获取有效账号处理函数"""
    service = AccountService(base_dir)
    return service.get_valid_accounts(check_cookie_param)


def get_all_accounts_handler(base_dir):
    """获取所有账号处理函数"""
    service = AccountService(base_dir)
    return service.get_all_accounts()


def get_account_with_cookie_handler(base_dir):
    """获取带Cookie的账号处理函数"""
    service = AccountService(base_dir)
    return service.get_account_with_cookie()


def get_kuaishou_accounts_with_cookie_handler(base_dir):
    """获取快手账号并包含Cookie内容的处理函数"""
    service = AccountService(base_dir)
    return service.get_kuaishou_accounts_with_cookie()


def delete_account_handler(base_dir, account_id):
    """删除账号处理函数"""
    service = AccountService(base_dir)
    return service.delete_account(account_id)


def update_userinfo_handler(base_dir, user_id, type_value, user_name=None):
    """更新用户信息处理函数"""
    service = AccountService(base_dir)
    return service.update_userinfo(user_id, type_value, user_name)


def update_auto_publish_handler(base_dir, user_id, auto_publish):
    """更新账号自动发布状态处理函数"""
    service = AccountService(base_dir)
    return service.update_auto_publish(user_id, auto_publish)
