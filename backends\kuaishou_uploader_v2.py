#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手视频上传器 V2
改进版本，包含更好的错误处理、日志记录和代码结构
"""

import json
import logging
import os
import time
from pathlib import Path
from typing import Optional, Dict, Any, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

try:
    from kuaishou_config import KUAISHOU_CONFIG, LOGGING_CONFIG, RETRY_CONFIG
except ImportError:
    from backends.kuaishou_config import KUAISHOU_CONFIG, LOGGING_CONFIG, RETRY_CONFIG

try:
    from utils.ks_signature import get_ns_sig3
except ImportError:
    from backends.utils.ks_signature import get_ns_sig3


class KuaishouUploaderV2:
    """快手视频上传器 V2"""
    
    def __init__(self, log_level: str = 'INFO'):
        """初始化上传器"""
        self._setup_logging(log_level)
        self._setup_session()
        self.config = KUAISHOU_CONFIG
        
    def _setup_logging(self, level: str):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, level),
            format=LOGGING_CONFIG['format'],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG['file'], encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def _setup_session(self):
        """设置HTTP会话"""
        self.session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=RETRY_CONFIG['max_attempts'],
            backoff_factor=RETRY_CONFIG['backoff_factor'],
            status_forcelist=RETRY_CONFIG['status_forcelist']
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
    def validate_video_file(self, video_path: str) -> Tuple[bool, str]:
        """
        验证视频文件
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            path = Path(video_path)
            
            # 检查文件是否存在
            if not path.exists():
                return False, "文件不存在"
                
            # 检查文件格式
            if path.suffix.lower() not in self.config['VIDEO']['SUPPORTED_FORMATS']:
                return False, f"不支持的文件格式: {path.suffix}"
                
            # 检查文件大小
            file_size = path.stat().st_size
            if file_size > self.config['VIDEO']['MAX_SIZE']:
                return False, f"文件过大: {file_size} bytes"
                
            if file_size == 0:
                return False, "文件为空"
                
            return True, ""
            
        except Exception as e:
            return False, f"文件验证失败: {str(e)}"
    
    def get_upload_token(self, api_ph: str, cookie: str) -> Optional[str]:
        """
        获取上传token
        
        Args:
            api_ph: 快手API签名
            cookie: 用户cookie
            
        Returns:
            上传token或None
        """
        try:
            url = f"{self.config['BASE_URL']}{self.config['ENDPOINTS']['UPLOAD_PRE']}"
            
            data = {
                'kuaishou.web.cp.api_ph': api_ph,
                'uploadType': '1',
            }
            
            headers = {
                'Content-Type': self.config['HEADERS']['CONTENT_TYPE_JSON'],
                'User-Agent': self.config['HEADERS']['USER_AGENT'],
                'Accept': self.config['HEADERS']['ACCEPT'],
                'Origin': self.config['HEADERS']['ORIGIN'],
                'Referer': self.config['HEADERS']['REFERER'],
                'Cookie': cookie
            }
            self.logger.error(f"headers: {headers}")
            # 生成签名
            data_bytes = json.dumps(data).encode('utf-8')
            ns_sig3 = self._generate_signature(data_bytes)
            
            params = {
                'uploadType': '1',
                '__NS_sig3': ns_sig3
            }
            
            response = self.session.post(
                url,
                headers=headers,
                params=params,
                json=data,
                timeout=self.config['UPLOAD']['TIMEOUT']
            )
            
            response.raise_for_status()
            result = response.json()

            # 检查是否有认证错误
            if result.get('result') == 109:
                login_url = result.get('loginUrl', '')
                self.logger.error(f"认证失败，需要重新登录: {login_url}")
                raise RuntimeError(f"Cookie已过期，需要重新登录。登录地址: {login_url}")

            if 'data' in result and 'token' in result['data']:
                upload_token = result['data']['token']
                self.logger.info(f"获取上传token成功: {upload_token[:20]}...")

                # 恢复上传功能
                self._resume_upload(upload_token)
                return upload_token
            else:
                self.logger.error(f"获取上传token失败: {result}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取上传token异常: {str(e)}")
            return None
    
    def _resume_upload(self, upload_token: str):
        """恢复上传功能"""
        try:
            url = f"{self.config['UPLOAD_URL']}{self.config['ENDPOINTS']['UPLOAD_RESUME']}"
            params = {'upload_token': upload_token}
            
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            self.logger.info("恢复上传功能成功")
            
        except Exception as e:
            self.logger.warning(f"恢复上传功能失败: {str(e)}")
    
    def upload_video(self, video_path: str, title: str, description: str,
                    api_ph: str, cookie: str) -> Dict[str, Any]:
        """
        上传视频
        
        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            api_ph: 快手API签名
            cookie: 用户cookie
            
        Returns:
            上传结果字典
        """
        result = {
            'success': False,
            'message': '',
            'data': None
        }
        
        try:
            # 验证视频文件
            is_valid, error_msg = self.validate_video_file(video_path)
            if not is_valid:
                result['message'] = error_msg
                return result
            
            # 获取上传token
            upload_token = self.get_upload_token(api_ph, cookie)
            if not upload_token:
                result['message'] = "获取上传token失败"
                return result
            
            # 上传视频文件
            file_info = self._upload_file(api_ph, video_path, upload_token, cookie)
            if not file_info:
                result['message'] = "视频文件上传失败"
                return result
            
            # 发布视频
            publish_result = self._publish_video(api_ph, cookie, file_info, title, description)
            if publish_result:
                result['success'] = True
                result['message'] = "视频上传并发布成功"
                result['data'] = file_info
            else:
                result['message'] = "视频发布失败"
                
        except Exception as e:
            self.logger.error(f"上传视频异常: {str(e)}")
            result['message'] = f"上传过程中出现异常: {str(e)}"
        
        return result
    
    def _upload_file(self, api_ph: str, video_path: str, upload_token: str, cookie: str) -> Optional[Dict[str, Any]]:
        """上传文件"""
        try:
            with open(video_path, 'rb') as f:
                video_data = f.read()
            
            file_size = len(video_data)
            filename = os.path.basename(video_path)
            chunk_size = self.config['UPLOAD']['CHUNK_SIZE']
            chunk_count = (file_size + chunk_size - 1) // chunk_size
            
            self.logger.info("=== 快手视频上传流程开始 ===")
            self.logger.info(f"文件: {filename}")
            self.logger.info(f"文件大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            self.logger.info(f"分片大小: {chunk_size:,} bytes ({chunk_size/1024/1024:.2f} MB)")
            self.logger.info(f"总分片数: {chunk_count}")

            self.logger.info("=== 步骤2: 分片上传 ===")
            # 上传分片
            for i in range(chunk_count):
                if not self._upload_chunk(video_data, i, file_size, chunk_size, upload_token, cookie):
                    self.logger.error(f"分片 {i+1}/{chunk_count} 上传失败，终止上传")
                    return None
            
            # 完成分片上传
            self.logger.info("=== 步骤3: 完成分片上传 ===")
            if not self._complete_upload(chunk_count, upload_token):
                return None

            # 完成文件上传 (获取fileId等信息)
            self.logger.info("=== 步骤4: 完成文件上传并获取文件信息 ===")
            self.logger.info("注意: 这一步是必需的，用于获取fileId等发布视频所需的信息")
            return self._finish_upload(api_ph, filename, file_size, upload_token, cookie)
            
        except Exception as e:
            self.logger.error(f"文件上传异常: {str(e)}")
            return None
    
    def _upload_chunk(self, video_data: bytes, chunk_index: int, file_size: int,
                     chunk_size: int, upload_token: str, cookie: str) -> bool:
        """上传单个分片"""
        try:
            start_pos = chunk_index * chunk_size
            end_pos = min((chunk_index + 1) * chunk_size, file_size)
            chunk_data = video_data[start_pos:end_pos]
            actual_chunk_size = len(chunk_data)

            # 修复 Content-Range 计算，确保格式正确
            content_range = f'bytes {start_pos}-{end_pos-1}/{file_size}'

            self.logger.debug(f"分片 {chunk_index}: {start_pos}-{end_pos-1}/{file_size}, 实际大小: {actual_chunk_size}")
            
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                'Connection': 'keep-alive',
                'Content-Length': str(actual_chunk_size),
                'Content-Range': content_range,
                'Content-Type': 'application/octet-stream',
                'Host': 'upload.kuaishouzt.com',
                'Origin': 'https://cp.kuaishou.com',
                'Referer': 'https://cp.kuaishou.com/article/publish/video?origin=www.kuaishou.com',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Cookie': cookie
            }
            
            url = f"{self.config['UPLOAD_URL']}{self.config['ENDPOINTS']['UPLOAD_FRAGMENT']}"
            params = {
                'upload_token': upload_token,
                'fragment_id': chunk_index
            }
            
            response = self.session.post(
                url,
                params=params,
                headers=headers,
                data=chunk_data,
                timeout=self.config['UPLOAD']['TIMEOUT']
            )

            response.raise_for_status()

            # 打印分片上传结果 - 对应原始代码的 html = opener.open(req, 文件切割).read()
            total_chunks = (file_size + chunk_size - 1) // chunk_size
            progress = ((chunk_index + 1) / total_chunks) * 100

            try:
                # 获取原始响应内容
                html_content = response.content
                self.logger.info(f"分片 {chunk_index + 1}/{total_chunks} - 原始响应: {html_content}")

                # 尝试解码为文本
                if html_content:
                    try:
                        text_content = html_content.decode('utf-8')
                        self.logger.info(f"分片 {chunk_index + 1}/{total_chunks} - 响应文本: {text_content}")
                    except:
                        pass

                # 尝试解析响应内容
                try:
                    response_data = response.json() if response.content else {}
                    self.logger.info(f"分片 {chunk_index + 1}/{total_chunks} 上传成功 ({progress:.1f}%) - JSON响应: {response_data}")
                except:
                    # 如果无法解析JSON，只打印基本信息
                    self.logger.info(f"分片 {chunk_index + 1}/{total_chunks} 上传成功 ({progress:.1f}%) - 状态码: {response.status_code}")
            except Exception as e:
                self.logger.error(f"处理分片响应时出错: {e}")

            return True
            
        except Exception as e:
            self.logger.error(f"分片 {chunk_index} 上传失败: {str(e)}")
            return False
    
    def _complete_upload(self, chunk_count: int, upload_token: str) -> bool:
        """完成分片上传"""
        try:
            url = f"{self.config['UPLOAD_URL']}{self.config['ENDPOINTS']['UPLOAD_COMPLETE']}"
            params = {
                'fragment_count': chunk_count,
                'upload_token': upload_token
            }

            # 添加短暂延迟，避免请求过于频繁
            import time
            time.sleep(1)

            response = self.session.post(url, params=params, timeout=10)
            response.raise_for_status()

            # 打印完成分片上传的结果 - 对应原始代码的 html = opener.open(req).read()
            try:
                # 获取原始响应内容
                html_content = response.content
                self.logger.info(f"完成分片上传 - 原始响应内容: {html_content}")

                # 尝试解码为文本
                if html_content:
                    try:
                        text_content = html_content.decode('utf-8')
                        self.logger.info(f"完成分片上传 - 响应文本: {text_content}")
                    except:
                        self.logger.info(f"完成分片上传 - 无法解码响应内容")

                # 尝试解析为JSON
                try:
                    response_data = response.json() if response.content else {}
                    self.logger.info(f"完成分片上传 - JSON响应: {response_data}")
                except:
                    self.logger.info(f"完成分片上传 - 非JSON响应")

            except Exception as e:
                self.logger.error(f"处理完成分片上传响应时出错: {e}")
                self.logger.info(f"完成分片上传 - 状态码: {response.status_code}")

            return True

        except Exception as e:
            self.logger.error(f"完成分片上传失败: {str(e)}")
            return False
    
    def _finish_upload(self, api_ph: str, filename: str, file_size: int, upload_token: str, cookie: str) -> Optional[Dict[str, Any]]:
        """完成文件上传，带重试机制"""
        max_retries = 3
        retry_delay = 2  # 秒

        for attempt in range(max_retries):
            try:
                url = f"{self.config['BASE_URL']}{self.config['ENDPOINTS']['UPLOAD_FINISH']}"

                # 确保参数格式与浏览器完全一致
                data = {
                    'token': upload_token,
                    'fileName': filename,
                    'fileType': 'video/mp4',
                    'fileLength': file_size,  # 修复：使用数字类型，不是字符串
                    'kuaishou.web.cp.api_ph': api_ph
                }

                headers = {
                    'Content-Type': self.config['HEADERS']['CONTENT_TYPE_JSON'],
                    'User-Agent': self.config['HEADERS']['USER_AGENT'],
                    'Accept': self.config['HEADERS']['ACCEPT'],
                    'Origin': self.config['HEADERS']['ORIGIN'],
                    'Referer': self.config['HEADERS']['REFERER'],
                    'Cookie': cookie
                }

                # 生成签名
                data_bytes = json.dumps(data).encode('utf-8')
                ns_sig3 = self._generate_signature(data_bytes)

                params = {'__NS_sig3': ns_sig3}

                self.logger.info(f"尝试完成文件上传 (第 {attempt + 1}/{max_retries} 次)")

                response = self.session.post(
                    url,
                    headers=headers,
                    params=params,
                    json=data,
                    timeout=self.config['UPLOAD']['TIMEOUT']
                )

                response.raise_for_status()
                result = response.json()

                self.logger.info(f"文件上传完成，响应: {result}")

                # 检查快手的错误码
                result_code = result.get('result')
                if result_code == 1:
                    # 成功
                    data_section = result.get('data', {})
                    file_info = {
                        'fileId': data_section.get('fileId'),
                        'coverKey': data_section.get('coverKey'),
                        'mediaId': data_section.get('mediaId'),
                        'photoIdStr': data_section.get('photoIdStr'),
                        'videoDuration': data_section.get('videoDuration')
                    }

                    self.logger.info(f"提取的文件信息: {file_info}")

                    # 检查关键字段
                    if not file_info.get('fileId'):
                        self.logger.error(f"未获取到 fileId，完整响应: {result}")
                        return None

                    return file_info

                elif result_code == 500002:
                    # 服务器临时错误，可以重试
                    self.logger.warning(f"服务器临时错误 (500002)，第 {attempt + 1} 次尝试失败")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                        continue
                    else:
                        self.logger.error(f"重试 {max_retries} 次后仍然失败: {result}")
                        return None
                else:
                    # 其他错误，不重试
                    self.logger.error(f"文件上传失败，错误码: {result_code}, 响应: {result}")
                    return None

            except Exception as e:
                self.logger.error(f"完成文件上传异常 (第 {attempt + 1} 次): {str(e)}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    return None

        return None

    def _publish_video(self, api_ph: str, cookie: str, file_info: Dict[str, Any],
                      title: str, description: str) -> bool:
        """发布视频"""
        try:
            url = f"{self.config['BASE_URL']}{self.config['ENDPOINTS']['VIDEO_SUBMIT']}"

            data = {
                "kuaishou.web.cp.api_ph": api_ph,
                "caption": description,
                "coverKey": file_info.get('coverKey', ''),
                "fileId": file_info.get('fileId', ''),
                "mediaId": file_info.get('mediaId', ''),
                "videoDuration": file_info.get('videoDuration', 0),
                "photoStatus": 1,
                "photoType": 0,
                "publishTime": 0
            }

            headers = {
                'Content-Type': self.config['HEADERS']['CONTENT_TYPE_JSON'],
                'User-Agent': self.config['HEADERS']['USER_AGENT'],
                'Accept': self.config['HEADERS']['ACCEPT'],
                'Origin': self.config['HEADERS']['ORIGIN'],
                'Referer': self.config['HEADERS']['REFERER'],
                'Cookie': cookie
            }

            # 生成签名
            data_bytes = json.dumps(data).encode('utf-8')
            ns_sig3 = self._generate_signature(data_bytes)

            params = {'__NS_sig3': ns_sig3}

            response = self.session.post(
                url,
                headers=headers,
                params=params,
                json=data,
                timeout=self.config['UPLOAD']['TIMEOUT']
            )

            response.raise_for_status()

            # 打印发布视频的结果 - 对应原始代码的 html = opener.open(req, data).read()
            try:
                # 获取原始响应内容
                html_content = response.content
                self.logger.info(f"视频发布 - 原始响应: {html_content}")

                # 尝试解码为文本
                if html_content:
                    try:
                        text_content = html_content.decode('utf-8')
                        self.logger.info(f"视频发布 - 响应文本: {text_content}")
                    except:
                        pass

                # 解析JSON响应
                result = response.json()
                self.logger.info(f"视频发布 - JSON响应: {result}")

                # 检查发布结果
                if result.get('result') == 1:
                    self.logger.info("视频发布成功")
                    return True
                else:
                    self.logger.error(f"视频发布失败: {result}")
                    return False

            except Exception as e:
                self.logger.error(f"处理视频发布响应时出错: {e}")
                return False

        except Exception as e:
            self.logger.error(f"视频发布失败: {str(e)}")
            return False
    
    def _generate_signature(self, data: bytes) -> str:
        """生成API签名"""
        try:
            return get_ns_sig3(data)
        except Exception as e:
            self.logger.error(f"签名生成失败: {e}")
            return "fallback_signature"


# 使用示例
if __name__ == "__main__":
    uploader = KuaishouUploaderV2()
    
    result = uploader.upload_video(
        video_path="test_video.mp4",
        title="测试视频",
        description="这是一个测试视频",
        api_ph="your_api_ph_here",
        cookie="your_cookie_here"
    )
    
    print(f"上传结果: {result}")
