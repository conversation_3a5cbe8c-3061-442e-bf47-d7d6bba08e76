#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import gzip
import json
import urllib.parse
from typing import Optional

def parse_simple_protobuf(data: bytes) -> dict:
    """
    简单的 protobuf 解析器，用于提取字符串字段
    """
    result = {}
    i = 0

    while i < len(data):
        try:
            # 读取 varint (字段编号和类型)
            tag, i = read_varint(data, i)
            if tag is None:
                break

            field_number = tag >> 3
            wire_type = tag & 0x7

            if wire_type == 0:  # Varint
                value, i = read_varint(data, i)
                if value is not None:
                    result[f"field_{field_number}_int"] = value

            elif wire_type == 2:  # Length-delimited (string/bytes)
                length, i = read_varint(data, i)
                if length is not None and i + length <= len(data):
                    value_bytes = data[i:i+length]
                    i += length

                    # 尝试解码为字符串
                    try:
                        value_str = value_bytes.decode('utf-8')
                        result[f"field_{field_number}_string"] = value_str
                    except UnicodeDecodeError:
                        result[f"field_{field_number}_bytes"] = value_bytes.hex()
            else:
                # 跳过其他类型
                break

        except Exception as e:
            print(f"Protobuf 解析错误: {e}")
            break

    return result

def read_varint(data: bytes, start: int) -> tuple:
    """读取 varint 编码的整数"""
    result = 0
    shift = 0
    pos = start

    while pos < len(data):
        byte = data[pos]
        result |= (byte & 0x7F) << shift
        pos += 1

        if (byte & 0x80) == 0:
            return result, pos

        shift += 7
        if shift >= 64:
            return None, pos

    return None, pos

def decode_force_public(encoded_data: str) -> Optional[dict]:
    """
    解码 forcePublic 参数
    
    Args:
        encoded_data: URL编码的base64+gzip数据
        
    Returns:
        解码后的数据，如果失败返回None
    """
    try:
        print(f"原始数据: {encoded_data}")
        
        # 1. URL解码
        url_decoded = urllib.parse.unquote(encoded_data)
        print(f"URL解码后: {url_decoded}")
        
        # 2. Base64解码
        base64_decoded = base64.b64decode(url_decoded)
        print(f"Base64解码后长度: {len(base64_decoded)} bytes")
        print(f"Base64解码后前20字节: {base64_decoded[:20]}")
        
        # 3. Gzip解压缩
        try:
            gzip_decompressed = gzip.decompress(base64_decoded)
            print(f"Gzip解压后长度: {len(gzip_decompressed)} bytes")
            print(f"Gzip解压后原始数据: {gzip_decompressed}")

            # 分析二进制数据，寻找可读字符串
            print("\n=== 二进制数据分析 ===")
            readable_strings = []
            current_string = ""

            for i, byte in enumerate(gzip_decompressed):
                if 32 <= byte <= 126:  # 可打印ASCII字符
                    current_string += chr(byte)
                else:
                    if len(current_string) >= 3:  # 至少3个字符才认为是有意义的字符串
                        readable_strings.append((i - len(current_string), current_string))
                    current_string = ""

            # 处理最后的字符串
            if len(current_string) >= 3:
                readable_strings.append((len(gzip_decompressed) - len(current_string), current_string))

            print(f"发现的可读字符串:")
            for pos, string in readable_strings:
                print(f"  位置 {pos}: '{string}'")

            # 尝试解析为 Protocol Buffers 格式
            print(f"\n=== Protocol Buffers 分析 ===")
            try:
                # 手动解析简单的 protobuf 字段
                protobuf_data = parse_simple_protobuf(gzip_decompressed)
                if protobuf_data:
                    print(f"Protobuf 解析结果: {protobuf_data}")
                    return protobuf_data
            except Exception as e:
                print(f"Protobuf 解析失败: {e}")

            # 尝试解析为JSON
            try:
                json_data = json.loads(gzip_decompressed.decode('utf-8'))
                print(f"JSON解析成功: {json_data}")
                return json_data
            except json.JSONDecodeError:
                print(f"不是JSON格式")
                return {
                    "raw_data": gzip_decompressed.hex(),
                    "readable_strings": [s for _, s in readable_strings],
                    "analysis": "可能是 Protocol Buffers 或其他二进制格式"
                }
                
        except gzip.BadGzipFile:
            print("不是gzip格式，尝试直接解析为文本")
            try:
                text_data = base64_decoded.decode('utf-8', errors='ignore')
                print(f"直接文本解析: {text_data}")
                return {"text": text_data}
            except Exception as e:
                print(f"文本解析失败: {e}")
                return None
                
    except Exception as e:
        print(f"解码失败: {e}")
        return None

def analyze_kwai_url(url: str):
    """分析完整的快手URL"""
    print("=== 快手URL分析 ===")
    print(f"完整URL: {url}")
    print()
    
    # 解析URL
    from urllib.parse import urlparse, parse_qs
    parsed = urlparse(url)
    params = parse_qs(parsed.query)
    
    print(f"协议: {parsed.scheme}")
    print(f"路径: {parsed.path}")
    print()
    
    # 分析关键参数
    key_params = [
        'forcePublic', 'selectedPhotoId', 'webShareToken', 
        'shareId', 'shareToken', 'serverExtraInfo'
    ]
    
    for param in key_params:
        if param in params:
            value = params[param][0]
            print(f"{param}: {value}")
            
            # 特殊处理某些参数
            if param == 'forcePublic':
                print("  解码 forcePublic:")
                decoded = decode_force_public(value)
                if decoded:
                    print(f"    解码结果: {decoded}")
                print()
                
            elif param == 'serverExtraInfo':
                print("  解码 serverExtraInfo:")
                try:
                    # 先base64解码
                    decoded_bytes = base64.b64decode(value)
                    decoded_str = decoded_bytes.decode('utf-8')
                    decoded_json = json.loads(decoded_str)
                    print(f"    解码结果: {decoded_json}")
                except Exception as e:
                    print(f"    解码失败: {e}")
                    # 尝试直接URL解码
                    try:
                        decoded = json.loads(urllib.parse.unquote(value))
                        print(f"    URL解码结果: {decoded}")
                    except:
                        print(f"    URL解码也失败")
                print()

            elif param == 'ug_st':
                print("  解码 ug_st:")
                try:
                    decoded = json.loads(urllib.parse.unquote(value))
                    print(f"    解码结果: {decoded}")
                except Exception as e:
                    print(f"    解码失败: {e}")
                print()

if __name__ == "__main__":
    # 测试数据
    force_public_data = "H4sIAAAAAAAA%2F%2BNQEmCQmHT12pMfLEaSXOzFGYlFqZ4pQnyGFqYGBhbGxoYmRgbmRgDAODIZJgAAAA%3D%3D"
    
    print("=== forcePublic 解码分析 ===")
    result = decode_force_public(force_public_data)
    print(f"最终结果: {result}")
    print()
    
    # 分析完整URL
    kwai_url = """kwai://work/5192369066199894133?forcePublic=H4sIAAAAAAAA%2F%2BNQEmCQmHT12pMfLEaSXOzFGYlFqZ4pQnyGFqYGBhbGxoYmRgbmRgDAODIZJgAAAA%3D%3D&enableSlidePlay=true&selectedPhotoId=5192369066199894133&path=%2Frest%2Fn%2FopenShare%2Ffeed%2Fview%2Flist&utm_source=shareBack&serverExtraInfo=eyJwaG90b0lkIjoiNTE5MjM2OTA2NjE5OTg5NDEzMyJ9&mt_product=H5_OUTSIDE_CLIENT_SHARE&tokenBlock=1&webShareToken=%2523%2523X567Y8xXOMV27J%2523%2523&ug_st=%7B%22did%22%3A%22web_ee6adc237b8644098c1aa0b3126d25d0%22%2C%22_ug_tId_%22%3A%22nifn0B1UMPRu%22%2C%22sid%22%3A%2218500833142072%22%2C%22mt_product%22%3A%22H5_OUTSIDE_CLIENT_SHARE%22%2C%22dp_project_tag%22%3A1%2C%22_ug_cId_%22%3A%22gp9nJAAMN4S8%22%7D&shareId=18500833142072&shareToken=X4weRSAbM4wg2eh&openFrom=%7B%22url%22%3A%22https%3A%2F%2Fv.m.chenzhongtech.com%2Ffw%2Fphoto%2F3xtupjugguug9h2%3Fcc%3Dshare_copylink%26followRefer%3D151%26shareMethod%3DTOKEN%26docId%3D9%26kpn%3DKUAISHOU%26subBiz%3DBROWSE_SLIDE_PHOTO%26photoId%3D3xtupjugguug9h2%26shareId%3D18500833142072%26shareToken%3DX4weRSAbM4wg2eh%26shareResourceType%3DPHOTO_OTHER%26userId%3D3xxh7bifwy6xjca%26plcEntryInfo%3DErABnhXXIe7o24LIrR6MIAnFPm5%252B4QREMRiz1iJ%252FJm7h2zcrMYL1QV3hKRO1VMMnkdxVlgfIh1NaA38PgF%252FBrU0o0TatUJREe8BS7kZGhoWJyXg5Z0RelxYEIQJIVDU4M4iejvJQNDfvqoBknmdNIdSy2zX4%252FCUB49dWTBo9744jvT7bHTOOyFqOqZETaXcT8ACEzaK6%252FXvbb57xkgRE3RI6EkD8vV1FjwVx4UverWRodd8oBTAG%26shareType%3D1%26et%3D1_u%252F0_sp55%26shareMode%3DAPP%26efid%3D3x5namdk9s6f6fi%26originShareId%3D18500833142072%26appType%3D21%26shareObjectId%3D5192369066199894133%26shareUrlOpened%3D0%26timestamp%3D1753870583626%22%2C%22cc%22%3A%22share_copylink%22%2C%22fid%22%3A%222273898762%22%2C%22did%22%3A%22web_ee6adc237b8644098c1aa0b3126d25d0%22%2C%22from%22%3A%22ios%22%2C%22channel%22%3A%22share%22%2C%22page%22%3A%22PHOTO_SHARE_PAGE%22%2C%22extParams%22%3A%22%7B%5C%22_ug_cId_%5C%22%3A%5C%22gp9nJAAMN4S8%5C%22%2C%5C%22_ug_tId_%5C%22%3A%5C%22nifn0B1UMPRu%5C%22%2C%5C%22share_id%5C%22%3A%5C%2218500833142072%5C%22%2C%5C%22popular_work_id%5C%22%3A%5C%225192369066199894133%5C%22%2C%5C%22launch_source%5C%22%3A%5C%22scheme%5C%22%7D%22%2C%22position%22%3A%22PLC_INLET%22%7D"""
    
    analyze_kwai_url(kwai_url)
