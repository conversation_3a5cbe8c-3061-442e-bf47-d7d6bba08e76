{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 12687594469746301899, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[1322478694103194923, "build_script_build", false, 14307359762726302535], [2069998946850810971, "tauri", false, 4696865449918420331], [2253952315205409758, "tauri_plugin_shell", false, 1881472142852141206], [3834743577069889284, "tauri_plugin_dialog", false, 1522930870045499590], [5236433071915784494, "sha2", false, 2792282897436168300], [8324636962323428845, "serde_json", false, 12397612473663235593], [9451456094439810778, "regex", false, 17332207901517935861], [10967960060725374459, "serde", false, 15592400294397051589], [12316149723851658110, "command_group", false, 17611919884377982464], [13890802266741835355, "tauri_plugin_fs", false, 1328601874217192913], [15441187897486245138, "tauri_plugin_http", false, 1636980705034680370], [17121285809968213651, "md5", false, 5315397796688613237], [18057500336552735601, "sysinfo", false, 1146679867181774340]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\app-4e7dc2d6835c5d74\\dep-bin-app", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}