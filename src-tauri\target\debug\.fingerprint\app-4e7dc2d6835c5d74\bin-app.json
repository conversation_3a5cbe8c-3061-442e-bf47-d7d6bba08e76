{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 12687594469746301899, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[1322478694103194923, "build_script_build", false, 11792008394123159189], [2069998946850810971, "tauri", false, 2359083582281520459], [2253952315205409758, "tauri_plugin_shell", false, 17876016975016071570], [3834743577069889284, "tauri_plugin_dialog", false, 13729015880524497802], [8324636962323428845, "serde_json", false, 12397612473663235593], [10967960060725374459, "serde", false, 15592400294397051589], [12316149723851658110, "command_group", false, 17611919884377982464], [13890802266741835355, "tauri_plugin_fs", false, 13019603157732819930], [15441187897486245138, "tauri_plugin_http", false, 4664669455727383592], [18057500336552735601, "sysinfo", false, 1146679867181774340]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\app-4e7dc2d6835c5d74\\dep-bin-app", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}