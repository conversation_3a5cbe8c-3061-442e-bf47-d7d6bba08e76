#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手上传配置文件
"""

# 快手API配置
KUAISHOU_CONFIG = {
    # 基础URL
    'BASE_URL': 'https://cp.kuaishou.com',
    'UPLOAD_URL': 'https://upload.kuaishouzt.com',
    
    # API端点
    'ENDPOINTS': {
        'UPLOAD_PRE': '/rest/cp/works/v2/video/pc/upload/pre',
        'UPLOAD_FINISH': '/rest/cp/works/v2/video/pc/upload/finish',
        'VIDEO_SUBMIT': '/rest/cp/works/v2/video/pc/submit',
        'UPLOAD_RESUME': '/api/upload/resume',
        'UPLOAD_FRAGMENT': '/api/upload/fragment',
        'UPLOAD_COMPLETE': '/api/upload/complete',
    },
    
    # 上传配置
    'UPLOAD': {
        'CHUNK_SIZE': 4194304,  # 4MB
        'MAX_RETRIES': 3,
        'TIMEOUT': 30,
    },
    
    # 请求头配置
    'HEADERS': {
        'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
        'ACCEPT': 'application/json, text/plain, */*',
        'ACCEPT_LANGUAGE': 'zh-CN,zh;q=0.9',
        'CONTENT_TYPE_JSON': 'application/json;charset=UTF-8',
        'CONTENT_TYPE_OCTET': 'application/octet-stream',
        'ORIGIN': 'https://cp.kuaishou.com',
        'REFERER': 'https://cp.kuaishou.com/article/publish/video',
    },
    
    # 视频配置
    'VIDEO': {
        'SUPPORTED_FORMATS': ['.mp4', '.avi', '.mov', '.mkv'],
        'MAX_SIZE': 2 * 1024 * 1024 * 1024,  # 2GB
        'MIN_DURATION': 1,  # 1秒
        'MAX_DURATION': 600,  # 10分钟
    },
    
    # 错误码
    'ERROR_CODES': {
        'SUCCESS': 0,
        'INVALID_TOKEN': 1001,
        'UPLOAD_FAILED': 1002,
        'FILE_TOO_LARGE': 1003,
        'INVALID_FORMAT': 1004,
        'NETWORK_ERROR': 1005,
    }
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'kuaishou_upload.log'
}

# 重试配置
RETRY_CONFIG = {
    'max_attempts': 3,
    'backoff_factor': 2,
    'status_forcelist': [500, 502, 503, 504]
}
