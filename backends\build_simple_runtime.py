#!/usr/bin/env python3
"""
简化的 Nuitka 打包脚本 - 专门解决运行时断言错误
避免复杂的依赖检测和 DLL 问题
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def prepare_build_environment():
    """准备构建环境"""
    print("准备构建环境...")
    
    # 清理旧的构建文件
    build_dirs = ["build", "out", "dist"]
    for build_dir in build_dirs:
        if os.path.exists(build_dir):
            print(f"清理旧的构建目录: {build_dir}")
            try:
                if os.name == "nt":
                    result = subprocess.run(
                        ["powershell", "-Command", f"Remove-Item -Recurse -Force '{build_dir}' -ErrorAction SilentlyContinue"],
                        capture_output=True,
                        text=True
                    )
                else:
                    shutil.rmtree(build_dir)
            except Exception as e:
                print(f"警告: 清理目录 {build_dir} 时出错: {e}")
    
    # 确保必要的目录存在
    os.makedirs("out", exist_ok=True)
    print("构建环境准备完成")
    return True

def build_with_nuitka():
    """使用 Nuitka 进行打包 - 简化版本"""
    print("开始 Nuitka 打包...")
    
    # 最简化的 Nuitka 命令
    nuitka_cmd = [
        "nuitka.cmd" if os.name == "nt" else "nuitka",
        "--standalone",
        "--show-progress",
        "--output-dir=out",
        "--windows-icon-from-ico=logo.ico",
        
        # 禁用可能有问题的功能
        "--disable-dll-dependency-cache",
        "--assume-yes-for-downloads",
        "--mingw64",
        
        # 最基本的跟踪
        "--nofollow-imports",
        "--follow-import-to=services",
        "--follow-import-to=utils",
        "--follow-import-to=myUtils",
        "--follow-import-to=uploader",
        
        # 基本系统包
        "--follow-import-to=sqlite3",
        "--follow-import-to=asyncio",
        "--follow-import-to=logging",
        
        # 包含基本包
        "--include-package=sqlite3",
        "--include-package=asyncio",
        "--include-package=logging",
        "--include-package=json",
        
        # 禁用优化以避免问题
        "--plugin-disable=anti-bloat",
        
        # 主文件
        "main.py"
    ]
    
    print("执行命令:", " ".join(nuitka_cmd))
    
    try:
        result = subprocess.run(nuitka_cmd, check=True, capture_output=True, text=True)
        print("Nuitka 打包成功!")
        if result.stdout:
            print("标准输出:", result.stdout[-1000:])  # 只显示最后1000字符
        return True
    except subprocess.CalledProcessError as e:
        print("Nuitka 打包失败!")
        print("错误输出:", e.stderr)
        if e.stdout:
            print("标准输出:", e.stdout[-1000:])
        return False

def post_build_setup():
    """构建后的设置"""
    print("执行构建后设置...")
    
    dist_dir = Path("out/main.dist")
    if not dist_dir.exists():
        print("构建目录不存在!")
        return False
    
    # 复制基本文件
    basic_files = [
        "logo.ico",
        "cookiesFile",
        "videoFile",
        "database.db",
        "ffmpeg",
        "services",
        "utils",
        "myUtils",
        "uploader"
    ]
    
    for file_path in basic_files:
        if os.path.exists(file_path):
            try:
                if os.path.isdir(file_path):
                    dest = dist_dir / file_path
                    if dest.exists():
                        shutil.rmtree(dest)
                    shutil.copytree(file_path, dest)
                    print(f"复制目录: {file_path} -> {dest}")
                else:
                    shutil.copy2(file_path, dist_dir)
                    print(f"复制文件: {file_path} -> {dist_dir}")
            except Exception as e:
                print(f"复制失败 {file_path}: {e}")
    
    # 创建简单的启动脚本
    create_simple_launch_script(dist_dir)
    
    return True

def create_simple_launch_script(dist_dir):
    """创建简单的启动脚本"""
    script_content = """@echo off
echo ========================================
echo 小超媒体管理系统启动中...
echo ========================================
echo.

echo 启动主程序...
main.exe

echo.
echo ========================================
if errorlevel 1 (
    echo 程序异常退出，错误代码: %errorlevel%
    echo 请检查上方的错误信息
) else (
    echo 程序正常退出
)
echo ========================================
pause
"""
    
    script_path = dist_dir / "启动.bat"
    with open(script_path, 'w', encoding='gbk') as f:
        f.write(script_content)
    
    print(f"创建启动脚本: {script_path}")

def main():
    """主函数"""
    print("=" * 70)
    print("小超媒体管理系统 - 简化版打包工具")
    print("专门解决运行时断言错误")
    print("=" * 70)
    
    # 检查是否在正确的目录
    if not os.path.exists("main.py"):
        print("错误: 未找到 main.py 文件，请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 检查 services 目录
    if not os.path.exists("services"):
        print("错误: 未找到 services 目录")
        sys.exit(1)
    
    # 检查 Nuitka 是否安装
    nuitka_cmd = "nuitka.cmd" if os.name == "nt" else "nuitka"
    try:
        result = subprocess.run([nuitka_cmd, "--version"], check=True, capture_output=True, text=True)
        print("✓ Nuitka 已安装")
        print(f"  版本: {result.stdout.strip().split()[0]}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到 Nuitka，请先安装: pip install nuitka")
        sys.exit(1)
    
    # 执行构建步骤
    steps = [
        ("准备构建环境", prepare_build_environment),
        ("Nuitka 打包", build_with_nuitka),
        ("构建后设置", post_build_setup),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*25} {step_name} {'='*25}")
        if not step_func():
            print(f"❌ 步骤失败: {step_name}")
            sys.exit(1)
        print(f"✓ {step_name} 完成")
    
    print("\n" + "="*70)
    print("🎉 打包完成!")
    print("📁 可执行文件位置: out/main.dist/main.exe")
    print("🚀 启动脚本位置: out/main.dist/启动.bat")
    print("💡 如果仍有运行时错误，请检查依赖版本兼容性")
    print("=" * 70)

if __name__ == "__main__":
    main()
