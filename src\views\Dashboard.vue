<template>
  <div class="dashboard">
    <div class="page-header">
      <h1>小超媒体运营系统</h1>
    </div>
    <el-row gutter="24">
      <el-col :span="12" style="margin-bottom: 20px">
        <div class="dashboard-content">
          <div style="margin: 0 auto">
            <div
              style="display: flex; align-items: center; margin-bottom: 20px"
            >
              <p style="margin: 0; margin-right: 8px">一键搬运发布</p>
              <el-popover placement="top" :width="300" trigger="hover">
                <template #reference>
                  <span
                    style="cursor: pointer; color: #409eff; font-size: 13px"
                  >
                    使用必看
                  </span>
                  <el-icon size="16" style="color: #409eff; cursor: help">
                    <QuestionFilled />
                  </el-icon>
                </template>
                <div style="font-size: 12px; line-height: 1.4">
                  <p style="margin-bottom: 5px">
                    搬运视频需要登录快手否则搬运不成功。
                  </p>
                  <p style="margin-bottom: 5px">
                    注意该账号只负责搬运，不负责发布
                  </p>
                  <p style="margin-bottom: 5px">
                    需要发布请到账号管理录入发布账号。
                  </p>
                  <p style="margin-bottom: 5px">
                    自动发布，录入海螺密钥后会自动启用发布。
                  </p>
                  <p style="margin-bottom: 5px">
                    请看高级设置，否则只搬运视频。
                  </p>
                  <p style="margin-bottom: 5px">海螺初始注册用户会送 15 元</p>
                </div>
              </el-popover>
            </div>

            <!-- 登录状态显示 -->
            <div v-if="!loginState.isLoggedIn" style="margin-bottom: 20px">
              <el-button
                :loading="loginState.isLoading"
                @click="onLoginKs"
                type="primary"
                size="default"
              >
                {{ loginState.isLoading ? "登录中..." : "登录快手账号" }}
              </el-button>
            </div>

            <!-- 已登录状态显示 -->
            <div
              v-else
              style="
                margin-bottom: 20px;
                padding: 15px;
                background-color: #f0f9ff;
                border-radius: 8px;
                border: 1px solid #e1f5fe;
              "
            >
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                "
              >
                <div style="display: flex; align-items: center">
                  <el-avatar
                    :size="40"
                    :src="loginState.userInfo.avatar || ''"
                    style="margin-right: 12px"
                  >
                    <el-icon><User /></el-icon>
                  </el-avatar>
                  <div>
                    <div style="font-weight: 500; color: #2563eb">
                      {{ loginState.userInfo.userName || "快手用户" }}
                    </div>
                    <div style="font-size: 12px; color: #64748b">
                      用户ID: {{ loginState.userInfo.userId }}
                    </div>
                  </div>
                </div>
                <el-button size="small" @click="onLogoutKs" type="default">
                  重新登录
                </el-button>
              </div>
            </div>

            <el-form :model="oneClickFormUrl" class="demo-form-inline">
              <el-form-item label="分享链接">
                <el-input
                  v-model="oneClickFormUrl.shareText"
                  style="width: 100%"
                  :autosize="{ minRows: 10 }"
                  type="textarea"
                  placeholder="请输入快手视频的分享链接，多个链接用换行分隔"
                  @input="saveFormSettings"
                />
              </el-form-item>

              <!-- 高级选项切换按钮 -->
              <el-form-item>
                <el-button
                  type="text"
                  @click="advancedOptionsVisible = !advancedOptionsVisible"
                  style="padding: 0; color: #a8abb2; font-size: 14px"
                >
                  <el-icon style="margin-right: 4px">
                    <ArrowDown v-if="!advancedOptionsVisible" />
                    <ArrowUp v-else />
                  </el-icon>
                  {{ advancedOptionsVisible ? "收起高级选项" : "展开高级选项" }}
                </el-button>
              </el-form-item>

              <!-- 高级选项内容 -->
              <div v-show="advancedOptionsVisible" class="advanced-options">
                <el-form-item label="视频生成密钥">
                  <div style="width: 100%">
                    <el-input
                      v-model="oneClickFormUrl.videoGenerationKey"
                      style="width: 100%; margin-bottom: 8px"
                      type="password"
                      show-password
                      placeholder="请输入视频生成API密钥"
                      @input="saveVideoGenerationKey"
                    />
                    <div style="font-size: 12px; color: #666">
                      <span>没有密钥？</span>
                      <el-link
                        href="https://platform.minimaxi.com/user-center/basic-information/interface-key"
                        target="_blank"
                        type="primary"
                        style="font-size: 12px"
                      >
                        点击获取密钥
                      </el-link>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="自动AB去重">
                  <div style="display: flex; align-items: center">
                    <el-switch
                      v-model="oneClickFormUrl.autoAB"
                      :disabled="!oneClickFormUrl.videoGenerationKey.trim() || oneClickFormUrl.autoPublish"
                      style="margin-right: 12px"
                      @change="saveFormSettings"
                    />
                    <span style="font-size: 12px; color: #666">
                      自动对搬运的视频进行AB帧去重处理
                      <span
                        v-if="!oneClickFormUrl.videoGenerationKey.trim()"
                        style="color: #f56c6c"
                        >（需要输入视频生成密钥）</span
                      >
                      <span
                        v-if="oneClickFormUrl.autoPublish"
                        style="color: #f56c6c"
                        >（自动发布时强制开启）</span
                      >
                    </span>
                  </div>
                </el-form-item>

                <el-form-item label="自动发布">
                  <div style="display: flex; align-items: center">
                    <el-switch
                      v-model="oneClickFormUrl.autoPublish"
                      :disabled="!oneClickFormUrl.videoGenerationKey.trim()"
                      style="margin-right: 12px"
                      @change="handleAutoPublishChange"
                    />
                    <span style="font-size: 12px; color: #666">
                      处理完成后自动发布到快手平台
                      <span
                        v-if="!oneClickFormUrl.videoGenerationKey.trim()"
                        style="color: #f56c6c"
                        >（需要输入视频生成密钥）</span
                      >
                      <span
                        v-if="oneClickFormUrl.autoPublish"
                        style="color: #f56c6c"
                        >（自动发布时必须开启AB去重）</span
                      >
                    </span>
                  </div>
                </el-form-item>

                <!-- 封面标题设置 -->
                <el-form-item label="封面标题">
                  <div style="width: 100%">
                    <!-- 字体大小设置 -->
                    <div style="margin-bottom: 12px">
                      <div style="display: flex; gap: 16px; margin-bottom: 8px; align-items: center; flex-wrap: wrap">
                        <div style="display: flex; align-items: center; gap: 8px">
                          <span style="font-size: 13px; color: #606266; white-space: nowrap">字体大小:</span>
                          <el-input-number
                            v-model="oneClickFormUrl.coverFontSize"
                            :min="20"
                            :max="120"
                            :step="2"
                            size="small"
                            style="width: 100px"
                            @change="saveFormSettings"
                          />
                          <span style="font-size: 12px; color: #909399">px</span>
                        </div>
                      </div>
                    </div>

                    <!-- 每行标题配置 -->
                    <div
                      v-for="(line, index) in titleLines"
                      :key="index"
                      style="margin-bottom: 12px; padding: 12px; border: 1px solid #e4e7ed; border-radius: 4px; background-color: #fafafa"
                    >
                      <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px">
                        <span style="font-size: 13px; color: #606266; font-weight: 500">第{{ index + 1 }}行:</span>
                        <el-button
                          type="danger"
                          size="small"
                          plain
                          @click="removeTitleLine(index)"
                        >
                          删除
                        </el-button>
                      </div>

                      <div style="display: flex; gap: 8px; align-items: center; margin-bottom: 8px">
                        <el-input
                          v-model="line.text"
                          placeholder="请输入这一行的文字"
                          style="flex: 1"
                          @input="updateTitleData"
                        />
                        <div style="display: flex; align-items: center; gap: 8px">
                          <span style="font-size: 12px; color: #606266; white-space: nowrap">颜色:</span>
                          <el-color-picker
                            v-model="line.color"
                            size="small"
                            show-alpha
                            :predefine="fontColorPresets"
                            @change="updateTitleData"
                          />
                        </div>
                      </div>

                      <!-- 字体大小和间距设置 -->
                      <div style="display: flex; gap: 12px; align-items: center; margin-bottom: 8px">
                        <div style="display: flex; align-items: center; gap: 4px">
                          <span style="font-size: 12px; color: #606266; white-space: nowrap">字体大小:</span>
                          <el-input-number
                            v-model="line.fontSize"
                            :min="12"
                            :max="200"
                            :step="2"
                            size="small"
                            style="width: 80px"
                            @change="updateTitleData"
                          />
                          <span style="font-size: 11px; color: #909399">px</span>
                        </div>
                        <div
                          v-if="titleLines.length > 1"
                          style="display: flex; align-items: center; gap: 4px"
                        >
                          <span style="font-size: 12px; color: #606266; white-space: nowrap">上下间距:</span>
                          <el-input-number
                            v-model="line.spacing"
                            :min="0"
                            :max="100"
                            :step="2"
                            size="small"
                            style="width: 80px"
                            @change="updateTitleData"
                          />
                          <span style="font-size: 11px; color: #909399">px</span>
                        </div>
                      </div>

                      <!-- 预览效果 -->
                      <div style="padding: 4px 8px; background-color: #000; border-radius: 4px; text-align: center">
                        <span
                          :style="{
                            color: line.color,
                            fontSize: Math.max(10, (line.fontSize || 72) * 0.2) + 'px',
                            fontWeight: 'bold',
                            textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                            marginTop: index > 0 ? ((line.spacing || 10) * 0.1) + 'px' : '0',
                            display: 'block'
                          }"
                        >
                          {{ line.text || '预览文字' }}
                        </span>
                      </div>
                    </div>

                    <!-- 添加行按钮 -->
                    <div style="text-align: center; margin-bottom: 8px">
                      <el-button
                        type="primary"
                        size="small"
                        plain
                        @click="addTitleLine"
                      >
                        + 添加一行 
                      </el-button>
                    </div>

                    <div style="font-size: 12px; color: #666">
                      <span>将在封面图上居中显示标题文字，支持自动换行，最多三行</span>
                    </div>
                  </div>
                </el-form-item>

                <!-- 自定义封面图片 -->
                <el-form-item label="自定义封面">
                  <div style="width: 100%">
                    <div style="display: flex; align-items: center; margin-bottom: 8px">
                      <el-button
                        size="small"
                        type="primary"
                        @click="selectCoverImage"
                        :loading="coverImageUploading"
                      >
                        {{ coverImageUploading ? "上传中..." : "选择封面图片" }}
                      </el-button>
                      <el-button
                        v-if="oneClickFormUrl.customCoverPath"
                        size="small"
                        type="danger"
                        @click="clearCoverImage"
                        style="margin-left: 8px"
                      >
                        清除
                      </el-button>
                      <el-button
                        v-if="oneClickFormUrl.customCoverPath && titleLines.some(line => line.text.trim())"
                        size="small"
                        type="success"
                        @click="testCoverGeneration"
                        :loading="testCoverLoading"
                        style="margin-left: 8px"
                      >
                        {{ testCoverLoading ? "生成中..." : "生成预览" }}
                      </el-button>
                    </div>

                    <!-- 封面图片预览 -->
                    <div v-if="oneClickFormUrl.customCoverPath" style="margin-bottom: 8px">
                      <el-image
                        :src="getPreviewImageSrc(oneClickFormUrl.customCoverPath)"
                        style="width: 120px; border-radius: 4px"
                        fit="cover"
                        :preview-src-list="[getPreviewImageSrc(oneClickFormUrl.customCoverPath)]"
                      >
                        <template #error>
                          <div style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; background: #f5f7fa; color: #909399; font-size: 12px">
                            图片加载失败
                          </div>
                        </template>
                      </el-image>
                    </div>

                    <div style="font-size: 12px; color: #666">
                      <span>不选择则使用视频第一帧作为封面。支持 JPG、PNG 格式</span>
                    </div>
                  </div>
                </el-form-item>
              </div>

              <el-form-item>
                <el-button
                  type="primary"
                  @click="onSubmitOneClick"
                  :disabled="!loginState.isLoggedIn"
                  :loading="oneClickLoading"
                >
                  {{ oneClickLoading ? "搬运中..." : "开始搬运" }}
                </el-button>
                <el-button
                  type="warning"
                  @click="testShopProductsOld"
                  :disabled="!loginState.isLoggedIn"
                  :loading="testShopLoading"
                  style="margin-left: 10px"
                >
                  {{ testShopLoading ? "测试中..." : "测试店铺商品" }}
                </el-button>
                <div
                  v-if="!loginState.isLoggedIn"
                  style="margin-top: 8px; font-size: 12px; color: #f56c6c"
                >
                  请先登录快手账号才能开始搬运
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-col>
      <el-col :span="12" style="margin-bottom: 20px">
        <div class="dashboard-content">
          <div style="width: 80%; margin: 0 auto">
            <p>自定义素材</p>
            <p style="margin-bottom: 20px">
              提示：为避免利用副视频重复去重不过审核，去重后将自动删除副视频文件夹视频
            </p>
            <el-form :model="ABFormFolderInline" class="demo-form-inline">
              <div style="margin-bottom: 20px">
                <el-form-item label="">
                  <div style="display: flex; flex: 1">
                    <el-input
                      v-model="ABFormFolderInline.mainVideoPath"
                      readonly
                      autocomplete="off"
                      style="margin-right: 20px"
                    >
                      <template #append>
                        <el-button
                          @click="
                            () => {
                              selectSecondaryVideoFolderPath();
                            }
                          "
                        >
                          选择主视频文件夹
                        </el-button>
                      </template>
                    </el-input>
                    <el-button
                      @click="goToFolder(ABFormFolderInline.mainVideoPath)"
                    >
                      <el-icon><Folder /></el-icon>
                    </el-button>
                  </div>
                </el-form-item>
              </div>
              <el-form-item label="">
                <div style="display: flex; flex: 1">
                  <el-input
                    v-model="ABFormFolderInline.secondaryVideoPath"
                    readonly
                    autocomplete="off"
                    style="margin-right: 20px"
                  >
                    <template #append>
                      <el-button
                        @click="
                          () => {
                            selectSecondaryVideoFolderPath(false);
                          }
                        "
                      >
                        选择副视频文件夹
                      </el-button>
                    </template>
                  </el-input>
                  <el-button
                    @click="goToFolder(ABFormFolderInline.secondaryVideoPath)"
                  >
                    <el-icon><Folder /></el-icon>
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="onSubmitABFolder"
                  >开始AB去重</el-button
                >
                <el-button type="warning" @click="testFetchDebug" style="margin-left: 10px"
                  >测试 Fetch</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="MaterialManagement"></div>
      </el-col>
    </el-row>

    <!-- 视频详情弹窗 -->
    <el-dialog
      v-model="videoDetailDialog.visible"
      :title="videoDetailDialog.isMultiple ? `视频处理结果 (${videoDetailDialog.allResults.length}个)` : '视频详情'"
      :width="videoDetailDialog.isMultiple ? '800px' : '480px'"
      @close="closeVideoDetailDialog"
    >
      <!-- 多视频模式 -->
      <div v-if="videoDetailDialog.isMultiple" class="multi-video-content">
        <div class="result-summary">
          <el-alert
            :title="`成功处理 ${videoDetailDialog.allResults.length} 个视频`"
            type="success"
            :closable="false"
            show-icon
          />
        </div>

        <div class="video-tabs">
          <el-tabs v-model="videoDetailDialog.activeTab" type="card">
            <el-tab-pane
              v-for="(result, index) in videoDetailDialog.allResults"
              :key="index"
              :label="`视频 ${index + 1}`"
              :name="index.toString()"
            >
              <div class="video-detail-content">
                <div class="video-main-info">
                  <!-- 竖屏视频封面 -->
                  <div class="cover-container">
                    <el-image
                      :src="getVideoCoverUrl(result.data)"
                      alt="视频封面"
                      class="cover-image"
                      fit="cover"
                    >
                      <template #error>
                        <div class="image-error">
                          <el-icon><Picture /></el-icon>
                          <span>封面加载失败</span>
                        </div>
                      </template>
                    </el-image>
                  </div>

                  <!-- 视频信息 -->
                  <div class="video-info">
                    <h3 class="video-title">
                      {{
                        result.data.name ||
                        result.data.publishParams?.videoInfo?.author ||
                        "未知作者"
                      }}
                    </h3>

                    <div class="video-stats">
                      <div class="stat-item">
                        <span class="stat-label">播放量:</span>
                        <span class="stat-value">{{
                          formatViewCount(
                            result.data.viewCount ||
                              result.data.publishParams?.videoInfo?.viewCount
                          )
                        }}</span>
                      </div>
                      <div class="stat-item">
                        <span class="stat-label">时长:</span>
                        <span class="stat-value">{{
                          formatDuration(
                            result.data.duration ||
                              result.data.publishParams?.videoInfo?.duration
                          )
                        }}</span>
                      </div>
                      <div v-if="result.data.ab_processed" class="stat-item">
                        <span class="stat-label">AB版本:</span>
                        <span class="stat-value">{{ result.data.account_count || 1 }}个</span>
                      </div>
                    </div>

                    <div class="video-description">
                      <p class="description-text">
                        {{
                          result.data.caption ||
                          result.data.publishParams?.videoInfo?.caption ||
                          "暂无描述"
                        }}
                      </p>
                    </div>

                    <div class="action-buttons">
                      <el-button size="small" type="primary" @click="goToMaterial(result.data)">
                        去素材管理页面
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 单视频模式 -->
      <div v-else-if="videoDetailDialog.data" class="video-detail-content">
        <div class="video-main-info">
          <!-- 竖屏视频封面 -->
          <div class="cover-container">
            <el-image
              :src="getVideoCoverUrl(videoDetailDialog.data)"
              alt="视频封面"
              class="cover-image"
              fit="cover"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>封面加载失败</span>
                </div>
              </template>
            </el-image>
          </div>

          <!-- 视频信息 -->
          <div class="video-info">
            <h3 class="video-title">
              {{
                videoDetailDialog.data.name ||
                videoDetailDialog.data.publishParams?.videoInfo?.author ||
                "未知作者"
              }}
            </h3>

            <div class="video-stats">
              <div class="stat-item">
                <span class="stat-label">播放量:</span>
                <span class="stat-value">{{
                  formatViewCount(
                    videoDetailDialog.data.viewCount ||
                      videoDetailDialog.data.publishParams?.videoInfo?.viewCount
                  )
                }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">时长:</span>
                <span class="stat-value">{{
                  formatDuration(
                    videoDetailDialog.data.duration ||
                      videoDetailDialog.data.publishParams?.videoInfo?.duration
                  )
                }}</span>
              </div>
              <div v-if="videoDetailDialog.data.ab_processed" class="stat-item">
                <span class="stat-label">AB版本:</span>
                <span class="stat-value"
                  >{{
                    videoDetailDialog.data.ab_versions?.length || 0
                  }}
                  个</span
                >
              </div>
              <div
                v-if="videoDetailDialog.data.account_count"
                class="stat-item"
              >
                <span class="stat-label">账号数:</span>
                <span class="stat-value">{{
                  videoDetailDialog.data.account_count
                }}</span>
              </div>
            </div>

            <div class="video-caption">
              <p>
                {{
                  videoDetailDialog.data.caption ||
                  videoDetailDialog.data.publishParams?.suggestedTitle ||
                  "无描述"
                }}
              </p>
            </div>

            <div class="action-buttons">
              <el-button
                size="small"
                type="warning"
                icon="Folder"
                @click="goToVideoFolder(videoDetailDialog.data)"
              >
                查看文件
              </el-button>
              <el-button
                size="small"
                type="info"
                icon="Upload"
                @click="goToMaterial(videoDetailDialog.data)"
              >
                去素材管理页面
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <!-- <el-button @click="viewOriginalLink" size="small"
            >查看原链接</el-button
          > -->
          <el-button type="primary" @click="closeVideoDetailDialog" size="small"
            >关闭</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 测试封面预览对话框 -->
    <el-dialog
      v-model="testCoverPreviewVisible"
      title="测试封面预览"
      width="600px"
      :close-on-click-modal="false"
    >
      <div style="text-align: center">
        <div v-if="testCoverPreviewUrl" style="margin-bottom: 16px">
          <el-image
                  style="width: 80px; margin-right: 10px"
                  :src="testCoverPreviewUrl"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="[testCoverPreviewUrl]"
                  :hide-on-click-modal="true"
                  show-progress
                  :initial-index="0"
                  fit="cover"
                  :preview-teleported="true"
                />
                
        </div>
        <div v-else style="padding: 40px; color: #909399">
          暂无预览图片
        </div>
      </div>

      <template #footer>
        <div style="text-align: center">
          <el-button @click="testCoverPreviewVisible = false">关闭</el-button>
          <!-- <el-button
            v-if="testCoverPreviewUrl"
            type="primary"
            @click="downloadTestCover"
          >
            下载图片
          </el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import {
  User,
  UserFilled,
  Platform,
  List,
  Document,
  Upload,
  Timer,
  DataAnalysis,
  Picture,
  DocumentCopy,
  Download,
  Folder,
  QuestionFilled,
  ArrowDown,
  ArrowUp,
} from "@element-plus/icons-vue";
import {
  ElMessage,
  ElMessageBox,
  ElLoading,
  ElAvatar,
  ElDialog,
  ElCard,
  ElDescriptions,
  ElDescriptionsItem,
  ElImage,
  ElButton,
  ElLink,
  ElSwitch,
} from "element-plus";
import { startSidecar } from "@/utils/sidecar";
import { getState } from "@/api";
import {
  selectFile,
  selectFolder,
  goFolder,
  selectCoverFile,
} from "@/utils/fileUtils";
import { materialApi } from "@/api/material";
import { accountApi } from "@/api/account";
import { convertFileSrc, invoke } from "@tauri-apps/api/core";
import { fetch } from "@tauri-apps/plugin-http";
import { extractLinksFromText } from "@/utils/linkExtractor";
import {
  getKuaishouDid,
  requestQrCode,
  checkScanResult,
  getQrToken,
  completeLogin,
  LoginStatusChecker,
} from "@/utils/kuaishouLogin";
import { getSig4 } from "@/utils/sig4";
import { log } from "devtools-detector";
const router = useRouter();

const ABFormFolderInline = reactive({
  visible: false,
  mainVideoPath: "",
  secondaryVideoPath: "",
  outputPath: "",
});
const oneClickFormUrl = reactive({
  shareText: "",
  videoGenerationKey: "",
  autoAB: true, // 默认关闭，根据密钥状态动态调整
  autoPublish: false, // 默认关闭，根据密钥状态动态调整
  coverTitle: "", // 封面标题
  customCoverPath: "", // 自定义封面图片路径
  coverFontSize: 72, // 封面标题字体大小，默认72px
  coverFontColor: "#FFFF00", // 封面标题字体颜色，默认黄色
  coverTitleLines: [], // 高级模式下的多行标题配置
});

// 高级选项展开状态
const advancedOptionsVisible = ref(false);

// 标题行配置（支持每行不同颜色、字体大小、间距）
const titleLines = ref([
  { text: '', color: '#FFFF00', fontSize: 72, spacing: 10 }
]);

// 字体颜色预设
const fontColorPresets = [
  "#FFFF00", // 黄色
  "#FFFFFF", // 白色
  "#FF0000", // 红色
  "#00FF00", // 绿色
  "#0000FF", // 蓝色
  "#FF6600", // 橙色
  "#FF00FF", // 紫色
  "#00FFFF", // 青色
  "#000000", // 黑色
  "#808080", // 灰色
];

// 添加标题行
const addTitleLine = () => {
  if (titleLines.value.length < 5) {
    titleLines.value.push({
      text: '',
      color: '#FFFF00',
      fontSize: 72,
      spacing: 10
    });
    updateTitleData();
  }
};

// 删除标题行
const removeTitleLine = (index) => {
  titleLines.value.splice(index, 1);
    updateTitleData();
};

// 更新标题数据
const updateTitleData = () => {
  // 将多行标题数据保存到 oneClickFormUrl 中
  oneClickFormUrl.coverTitleLines = titleLines.value.map(line => ({
    text: line.text.trim(),
    color: line.color,
    fontSize: line.fontSize || 72,
    spacing: line.spacing || 10
  }));

  // 同时更新传统的标题字段（用于兼容）
  const combinedTitle = titleLines.value
    .filter(line => line.text.trim())
    .map(line => line.text.trim())
    .join('\n');
  oneClickFormUrl.coverTitle = combinedTitle;

  saveFormSettings();
};

// 一键搬运loading状态
const oneClickLoading = ref(false);

// 测试店铺商品loading状态
const testShopLoading = ref(false);

// 封面图片上传loading状态
const coverImageUploading = ref(false);

// 测试封面生成状态
const testCoverLoading = ref(false);
const testCoverPreviewUrl = ref('');

const fileList = ref([]); // 用于存储已上传的文件列表

// 登录相关状态
const loginState = reactive({
  isLoading: false,
  qrData: null,
  cookieData: null,
  showQrDialog: false,
  loginChecker: null,
  qrExpireTimeout: null,
  isLoggedIn: false,
  userInfo: {
    userId: null,
    userName: "",
    avatar: "",
  },
});

const goToFolder = async (path) => {
  // 请求获取当前工作目录
  const filePath = path; // 替换反斜杠为正斜杠
  await materialApi.openFolder({ filePath });

  ElMessage.success("打开路径" + filePath);
};

onMounted(async () => {
  // 初始化主视频和副视频路径
  const mainVideoPath = localStorage.getItem("mainVideoFolderPath") || "";
  const secondaryVideoPath =
    localStorage.getItem("secondaryVideoFolderPath") || "";
  ABFormFolderInline.mainVideoPath = mainVideoPath;
  ABFormFolderInline.secondaryVideoPath = secondaryVideoPath;

  // 检查是否已有登录信息
  checkLoginStatus();

  // 初始化视频生成密钥
  initVideoGenerationKey();

  // 恢复表单设置
  loadFormSettings();
});

// 初始化视频生成密钥
const initVideoGenerationKey = () => {
  const savedKey = localStorage.getItem("video_generation_key");
  if (savedKey) {
    oneClickFormUrl.videoGenerationKey = savedKey;
  } else {
    // 没有密钥时，默认关闭自动AB去重和自动发布
    oneClickFormUrl.autoAB = false;
    oneClickFormUrl.autoPublish = false;
  }
};

// 保存视频生成密钥
const saveVideoGenerationKey = () => {
  localStorage.setItem(
    "video_generation_key",
    oneClickFormUrl.videoGenerationKey
  );

  // 根据密钥是否存在来设置默认值
  if (oneClickFormUrl.videoGenerationKey.trim()) {
    // 有密钥时，自动开启功能
    oneClickFormUrl.autoAB = true;
    oneClickFormUrl.autoPublish = true;
  } else {
    // 没有密钥时，自动关闭并禁用
    oneClickFormUrl.autoAB = false;
    oneClickFormUrl.autoPublish = false;
  }

  // 保存所有设置
  saveFormSettings();
};

// 保存表单设置到localStorage
const saveFormSettings = () => {
  const settings = {
    shareText: oneClickFormUrl.shareText,
    autoAB: oneClickFormUrl.autoAB,
    autoPublish: oneClickFormUrl.autoPublish,
    advancedOptionsVisible: advancedOptionsVisible.value,
    coverTitle: oneClickFormUrl.coverTitle,
    customCoverPath: oneClickFormUrl.customCoverPath,
    coverFontSize: oneClickFormUrl.coverFontSize,
    coverFontColor: oneClickFormUrl.coverFontColor,
    coverTitleLines: oneClickFormUrl.coverTitleLines,
    titleLines: titleLines.value,
  };
  localStorage.setItem("oneclick_form_settings", JSON.stringify(settings));
};

// 处理自动发布变更
const handleAutoPublishChange = (value) => {
  // 如果开启自动发布，强制开启AB去重
  if (value) {
    oneClickFormUrl.autoAB = true;
    ElMessage.info("自动发布已开启，AB去重已自动开启");
  }
  saveFormSettings();
};

// 从localStorage恢复表单设置
const loadFormSettings = () => {
  try {
    const savedSettings = localStorage.getItem("oneclick_form_settings");
    if (savedSettings) {
      const settings = JSON.parse(savedSettings);

      // 恢复表单数据
      if (settings.shareText !== undefined) {
        oneClickFormUrl.shareText = settings.shareText;
      }
      if (settings.autoAB !== undefined) {
        oneClickFormUrl.autoAB = settings.autoAB;
      }
      if (settings.autoPublish !== undefined) {
        oneClickFormUrl.autoPublish = settings.autoPublish;
      }

      if (settings.coverTitle !== undefined) {
        oneClickFormUrl.coverTitle = settings.coverTitle;
      }
      if (settings.customCoverPath !== undefined) {
        oneClickFormUrl.customCoverPath = settings.customCoverPath;
      }
      if (settings.coverFontSize !== undefined) {
        oneClickFormUrl.coverFontSize = settings.coverFontSize;
      }
      if (settings.coverFontColor !== undefined) {
        oneClickFormUrl.coverFontColor = settings.coverFontColor;
      }

      // 恢复多行标题相关设置
      if (settings.coverTitleLines !== undefined) {
        oneClickFormUrl.coverTitleLines = settings.coverTitleLines;
      }
      if (settings.titleLines !== undefined && Array.isArray(settings.titleLines)) {
        // 确保每行都有完整的字段
        titleLines.value = settings.titleLines.map(line => ({
          text: line.text || '',
          color: line.color || '#FFFF00',
          fontSize: line.fontSize || 72,
          spacing: line.spacing || 10
        }));
      }
    }
  } catch (error) {
    console.warn("恢复表单设置失败:", error);
  }
};

// 切换高级选项显示状态
const toggleAdvancedOptions = () => {
  advancedOptionsVisible.value = !advancedOptionsVisible.value;
  saveFormSettings();
};

// 选择封面图片
const selectCoverImage = async () => {
  try {
    coverImageUploading.value = true;

    // 使用文件选择工具
    const selectedFile = await selectFile(["jpg", "jpeg", "png"], "选择封面图片");

    if (selectedFile) {
      // 验证文件格式
      const allowedExtensions = ['.jpg', '.jpeg', '.png'];
      const fileExtension = selectedFile.toLowerCase().substring(selectedFile.lastIndexOf('.'));

      if (!allowedExtensions.includes(fileExtension)) {
        ElMessage.error("请选择 JPG 或 PNG 格式的图片文件");
        return;
      }

      oneClickFormUrl.customCoverPath = selectedFile;
      saveFormSettings();
      ElMessage.success("封面图片选择成功");
    }
  } catch (error) {
    console.error("选择封面图片失败:", error);
    ElMessage.error("选择封面图片失败: " + error.message);
  } finally {
    coverImageUploading.value = false;
  }
};

// 清除封面图片
const clearCoverImage = () => {
  oneClickFormUrl.customCoverPath = "";
  testCoverPreviewUrl.value = ''; // 清除测试预览
  saveFormSettings();
  ElMessage.success("已清除自定义封面图片");
};

// 测试封面生成
const testCoverGeneration = async () => {
  try {
    testCoverLoading.value = true;

    // 验证参数
    if (!oneClickFormUrl.customCoverPath) {
      ElMessage.error("请先选择自定义封面图片");
      return;
    }

    const validLines = titleLines.value.filter(line => line.text.trim());
    if (validLines.length === 0) {
      ElMessage.error("请配置封面标题");
      return;
    }

    // 准备请求参数
    const coverTitleLinesParam = validLines.map(line => ({
      text: line.text.trim(),
      color: line.color,
      fontSize: line.fontSize || 72,
      spacing: line.spacing || 10
    }));

    console.log("测试封面生成参数:", {
      customCoverPath: oneClickFormUrl.customCoverPath,
      coverTitleLines: coverTitleLinesParam
    });

    // 调用测试接口
    const response = await materialApi.testCoverGeneration({
      customCoverPath: oneClickFormUrl.customCoverPath,
      coverTitleLines: coverTitleLinesParam
    });

    console.log("测试封面生成结果:", response);

    if (response && response.code === 200) {
      const { data } = response;
      testCoverPreviewUrl.value = convertFileSrc(data.full_path) + '?t=' + Date.now();

      ElMessage.success("测试封面生成成功！");

      // 显示预览对话框
      showTestCoverPreview();
    } else {
      ElMessage.error(response?.msg || "测试封面生成失败");
    }

  } catch (error) {
    console.error("测试封面生成失败:", error);
    ElMessage.error("测试封面生成失败: " + error.message);
  } finally {
    testCoverLoading.value = false;
  }
};

// 显示测试封面预览对话框
const testCoverPreviewVisible = ref(false);
const showTestCoverPreview = () => {
  testCoverPreviewVisible.value = true;
};

// 下载测试封面

// 下载测试封面
const downloadTestCover = async(filePath) => {
  await materialApi.openFolder({ filePath: testCoverPreviewUrl });
};

// 获取预览图片的 src
const getPreviewImageSrc = (filePath) => {
  if (!filePath) return "";
  try {
    return convertFileSrc(filePath);
  } catch (error) {
    console.error("转换文件路径失败:", error);
    return filePath; // 降级处理
  }
};

onUnmounted(() => {
  // 组件卸载时清理超时和检查器
  if (loginState.qrExpireTimeout) {
    clearTimeout(loginState.qrExpireTimeout);
    loginState.qrExpireTimeout = null;
  }
  if (loginState.loginChecker) {
    loginState.loginChecker.stopChecking();
    loginState.loginChecker = null;
  }
});

const handelSelectCoverFile = async (material) => {
  try {
    const response = await selectCoverFile(material);
    if (response.success) {
      ElMessage.success(response.msg || "封面图更新成功");
      //
      material.cover_image = response.data.cover_image; // 更新封面图路径
      // await fetchMaterials();
    } else {
      ElMessage.error(response.msg || "封面图更新失败");
    }
  } catch (error) {
    console.error("更新封面图出错:", error);
    ElMessage.error("封面图更新失败");
  }
};

// 编辑时的文件选择函数
const selectSecondaryVideoFolderPath = async (isMain = true) => {
  const lastPathKey = isMain
    ? "mainVideoFolderPath"
    : "secondaryVideoFolderPath";
  const lastPath = localStorage.getItem(lastPathKey) || "";

  const fileUrl = await selectFolder("请选择文件夹", lastPath);

  if (fileUrl) {
    if (isMain) {
      ABFormFolderInline.mainVideoPath = fileUrl;
    } else {
      ABFormFolderInline.secondaryVideoPath = fileUrl;
    }
    // 记录本次选择的父级文件夹（兼容 Windows 路径）
    try {
      //   const normalizedPath = fileUrl.replace(/\//g, "\\");
      //   const parentPath = normalizedPath.substring(0, normalizedPath.lastIndexOf("\\"));
      localStorage.setItem(lastPathKey, fileUrl);
    } catch (e) {
      // 路径处理异常忽略
    }
    console.log(isMain ? "主视频路径:" : "副视频路径:", fileUrl);
  }
};

// 快手登录相关方法
// 检查登录状态
const checkLoginStatus = () => {
  try {
    const savedLoginData = localStorage.getItem("kuaishou_login_data");
    if (savedLoginData) {
      const loginData = JSON.parse(savedLoginData);
      if (loginData.isLoggedIn && loginData.userId) {
        loginState.isLoggedIn = true;
        loginState.userInfo.userId = loginData.userId;
        loginState.userInfo.userName =
          loginData.userName || `用户${loginData.userId}`;
        loginState.userInfo.avatar = loginData.avatar || "";
        console.log("检测到已登录状态:", {
          userId: loginData.userId,
          userName: loginData.userName,
          avatar: loginData.avatar,
        });
      }
    }
  } catch (error) {
    console.error("检查登录状态失败:", error);
  }
};

// 退出登录
const onLogoutKs = () => {
  ElMessageBox.confirm("确定要退出当前账号重新登录吗？", "确认退出", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 清除登录状态
      loginState.isLoggedIn = false;
      loginState.userInfo = {
        userId: null,
        userName: "",
        avatar: "",
      };
      // 清除本地存储
      localStorage.removeItem("kuaishou_login_data");
      ElMessage.success("已退出登录");
    })
    .catch(() => {
      // 用户取消
    });
};

const onLoginKs = async () => {
  try {
    loginState.isLoading = true;

    // 1. 获取 DID
    loginState.cookieData = await getKuaishouDid();
    console.log("获取到的 Cookie 信息:", loginState.cookieData);

    if (Object.keys(loginState.cookieData).length === 0) {
      ElMessage.warning("未获取到 Cookie 信息");
      return;
    }

    ElMessage.success(
      `成功获取到 ${Object.keys(loginState.cookieData).length} 个 Cookie`
    );

    // 2. 请求二维码
    loginState.qrData = await requestQrCode(loginState.cookieData);
    if (!loginState.qrData) {
      ElMessage.error("获取二维码失败");
      return;
    }

    // 3. 显示二维码弹窗
    showQrCodeDialog();
  } catch (error) {
    console.error("快手登录出错:", error);
    ElMessage.error("登录失败: " + error.message);
  } finally {
    loginState.isLoading = false;
  }
};

// 显示二维码弹窗
const showQrCodeDialog = () => {
  const qrImageSrc = `data:image/png;base64,${loginState.qrData.imageData}`;

  const createMessageContent = (imageData, expireTime) => {
    return `
      <div style="text-align: center;">
        <p style="margin-bottom: 15px;">请使用快手App扫描二维码登录</p>
        <img id="qr-code-image" src="data:image/png;base64,${imageData}" style="width: 200px; height: 200px;" alt="登录二维码" />
        <p style="margin-top: 15px; color: #666; font-size: 12px;">二维码有效期: ${new Date(
          expireTime
        ).toLocaleString()}</p>
      </div>
    `;
  };

  // 标记弹窗已显示
  loginState.showQrDialog = true;

  // 创建消息框，但不保存实例
  ElMessageBox({
    title: "快手登录",
    message: createMessageContent(
      loginState.qrData.imageData,
      loginState.qrData.expireTime
    ),
    dangerouslyUseHTMLString: true,
    showCancelButton: true,
    confirmButtonText: "刷新二维码",
    cancelButtonText: "取消",
    closeOnClickModal: false,
    beforeClose: (action, instance, done) => {
      if (action === "confirm") {
        // 刷新二维码
        refreshQrCode();
        return false; // 不关闭弹窗
      } else {
        // 停止检查和超时
        stopLoginProcess();
        loginState.showQrDialog = false;
        done();
      }
    },
  })
    .then(() => {
      // 处理确认按钮点击（已在 beforeClose 中处理）
    })
    .catch(() => {
      // 用户取消或关闭弹窗
      stopLoginProcess();
      loginState.showQrDialog = false;
    });

  // 设置二维码过期超时
  setQrExpireTimeout();

  // 开始检测扫码结果
  startScanResultCheck();
};

// 刷新二维码
const refreshQrCode = async () => {
  try {
    console.log("正在刷新二维码...");

    // 停止当前检查
    if (loginState.loginChecker) {
      loginState.loginChecker.stopChecking();
    }

    // 获取新的二维码
    const newQrData = await requestQrCode(loginState.cookieData);
    if (!newQrData) {
      ElMessage.error("刷新二维码失败");
      return;
    }

    loginState.qrData = newQrData;
    console.log("获取到新的二维码数据:", newQrData);

    // 等待DOM渲染完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 更新弹窗中的二维码图片
    const qrImage = document.getElementById("qr-code-image");
    if (qrImage) {
      qrImage.src = `data:image/png;base64,${newQrData.imageData}`;
      console.log("已更新二维码图片");
    } else {
      // 备用查找方法
      const allImages = document.querySelectorAll(".el-message-box img");
      if (allImages.length > 0) {
        allImages[0].src = `data:image/png;base64,${newQrData.imageData}`;
        allImages[0].id = "qr-code-image";
        console.log("通过备用方法更新了二维码图片");
      }
    }

    // 更新过期时间显示
    const messageBoxContent = document.querySelector(
      ".el-message-box__content"
    );
    if (messageBoxContent) {
      const expireTimeElements = messageBoxContent.querySelectorAll("p");
      if (expireTimeElements.length >= 2) {
        expireTimeElements[1].textContent = `二维码有效期: ${new Date(
          newQrData.expireTime
        ).toLocaleString()}`;
        console.log("已更新过期时间显示");
      }
    }

    // 重新设置过期超时
    setQrExpireTimeout();

    // 重新开始检测扫码结果
    startScanResultCheck();

    ElMessage.success("二维码已刷新");
  } catch (error) {
    console.error("刷新二维码失败:", error);
    ElMessage.error("刷新二维码失败: " + error.message);
  }
};

// 设置二维码过期超时
const setQrExpireTimeout = () => {
  if (loginState.qrExpireTimeout) {
    clearTimeout(loginState.qrExpireTimeout);
  }

  const expireTime = new Date(loginState.qrData.expireTime).getTime();
  const currentTime = Date.now();
  const timeUntilExpire = expireTime - currentTime;

  if (timeUntilExpire > 0) {
    loginState.qrExpireTimeout = setTimeout(() => {
      console.log("定时器触发: 二维码已过期，正在自动刷新...");
      ElMessage.info("二维码已过期，正在刷新...");
      refreshQrCode();
    }, timeUntilExpire);
    console.log(`设置过期超时: ${Math.round(timeUntilExpire / 1000)}秒`);
  }
};

// 开始检测扫码结果
const startScanResultCheck = () => {
  if (loginState.loginChecker) {
    loginState.loginChecker.stopChecking();
  }

  loginState.loginChecker = new LoginStatusChecker();
  loginState.loginChecker.startChecking(
    loginState.qrData,
    loginState.qrData.cookieData,
    onScanSuccess,
    onQrExpired,
    onLoginError
  );
};

// 扫码成功回调
const onScanSuccess = async (scanResult) => {
  try {
    console.log("扫码确认成功，scanResult数据:", scanResult);

    // 从scanResult中获取用户信息
    const userInfo = scanResult.user || {};
    const userId = userInfo.user_id;
    const userName = userInfo.user_name || `用户${userId}`;
    const avatar = userInfo.headurl || "";

    console.log("提取的用户信息:", { userId, userName, avatar });

    // 获取 qrToken
    const qrTokenResult = await getQrToken(
      loginState.qrData,
      loginState.qrData.cookieData
    );
    if (!qrTokenResult) {
      throw new Error("获取 qrToken 失败");
    }

    // 完成最终登录
    const loginData = await completeLogin(
      qrTokenResult,
      loginState.qrData.cookieData
    );
    if (!loginData) {
      throw new Error("完成登录失败");
    }

    // 解析callback响应体获取登录信息
    let callbackData = {};
    try {
      if (loginData.responseBody) {
        callbackData = JSON.parse(loginData.responseBody);
        console.log("解析到的callback数据:", callbackData);
      }
    } catch (parseError) {
      console.warn("无法解析callback响应体为JSON:", parseError);
      // 如果无法解析，使用原始响应体
      callbackData = { rawResponse: loginData.responseBody };
    }

    // 构建要保存的登录数据，优先使用scanResult和callback返回的字段
    const savedLoginData = {
      loginTime: Date.now(),
      // callback接口返回的核心字段
      result: callbackData.result || scanResult.result,

      stsUrl: callbackData.stsUrl,
      followUrl: callbackData.followUrl,
      bUserId: callbackData.bUserId,
      userId: callbackData.userId || userId,
      sid: callbackData.sid,
      // 用户信息（从scanResult获取）
      userName: userName,
      avatar: avatar,
      userSex: userInfo.user_sex,
      eid: userInfo.eid,
      // 保留原始完整数据作为备份
      originalLoginData: loginData,
      scanResultData: scanResult,
      // 保留cookie信息
      cookies: {
        ...loginData.cookies,
        "kuaishou.server.webday7.at":
          callbackData["kuaishou.server.webday7.at"],
        ssecurity: callbackData.ssecurity,
        "kuaishou.server.webday7_st":
          callbackData["kuaishou.server.webday7_st"],
        passToken: callbackData.passToken,
        userId: callbackData.userId || userId,
      },
      // 添加登录状态标记
      isLoggedIn: (callbackData.result || scanResult.result) === 1,
    };

    // 保存登录信息到本地存储
    localStorage.setItem("kuaishou_login_data", JSON.stringify(savedLoginData));

    // 更新UI状态
    if (savedLoginData.isLoggedIn) {
      loginState.isLoggedIn = true;
      loginState.userInfo.userId = savedLoginData.userId;
      loginState.userInfo.userName = userName;
      loginState.userInfo.avatar = avatar;
    }

    console.log("保存的登录数据:", savedLoginData);
    ElMessage.success(`登录成功！欢迎 ${userName}`);

    // 强制关闭弹窗 - 直接操作DOM元素
    const forceCloseDialog = () => {
      console.log("开始强制关闭二维码弹窗");

      // 先停止检查器
      stopLoginProcess();

      // 查找并关闭弹窗的多种方式
      setTimeout(() => {
        // 方式1: 点击取消按钮
        const cancelBtn = document.querySelector(
          ".el-message-box__btns .el-button--default"
        );
        if (cancelBtn) {
          console.log("找到取消按钮，点击关闭");
          cancelBtn.click();
          return;
        }

        // 方式2: 点击关闭图标
        const closeIcon = document.querySelector(".el-message-box__close");
        if (closeIcon) {
          console.log("找到关闭图标，点击关闭");
          closeIcon.click();
          return;
        }

        // 方式3: 触发ESC键事件
        const escEvent = new KeyboardEvent("keydown", {
          key: "Escape",
          keyCode: 27,
          which: 27,
          bubbles: true,
          cancelable: true,
        });
        document.dispatchEvent(escEvent);
        console.log("触发ESC键事件");

        // 方式4: 最后手段 - 直接移除DOM元素
        setTimeout(() => {
          const overlay = document.querySelector(".el-overlay");
          const messageBox = document.querySelector(".el-message-box");
          if (overlay) {
            console.log("直接移除弹窗DOM元素");
            overlay.remove();
          }
          if (messageBox && !overlay) {
            messageBox.remove();
          }
        }, 200);
      }, 100);
    };

    // 执行强制关闭
    forceCloseDialog();

    console.log("登录成功，完整数据:", { loginData, scanResult });
  } catch (error) {
    console.error("登录过程出错:", error);
    ElMessage.error("登录失败: " + error.message);
  }
};

// 二维码过期回调
const onQrExpired = () => {
  console.log("二维码已过期，自动刷新");
  refreshQrCode();
};

// 登录错误回调
const onLoginError = (error) => {
  console.error("登录失败:", error);
  ElMessage.error("登录失败: " + error);
};

// 停止登录过程
const stopLoginProcess = () => {
  if (loginState.qrExpireTimeout) {
    clearTimeout(loginState.qrExpireTimeout);
    loginState.qrExpireTimeout = null;
  }
  if (loginState.loginChecker) {
    loginState.loginChecker.stopChecking();
    loginState.loginChecker = null;
  }
  loginState.showQrDialog = false;
};

// 从登录数据中生成cookie字符串
const getCookieStringFromLoginData = () => {
  try {
    const savedLoginData = localStorage.getItem("kuaishou_login_data");
    if (!savedLoginData) {
      throw new Error("未找到登录数据");
    }

    const loginData = JSON.parse(savedLoginData);
    if (!loginData.isLoggedIn) {
      throw new Error("用户未登录");
    }

    // 合并所有cookie数据
    const allCookies = { ...loginData.cookies };
    allCookies["kuaishou.kwaishop.token	"] = allCookies["passToken"];
    allCookies["ssecurity"] = loginData.cookies["ssecurity"];
    allCookies["kuaishou.server.webday7.at"] =
      loginData["kuaishou.server.webday7.at"];
    allCookies["kuaishou.server.webday7_st"] =
      loginData["kuaishou.server.webday7_st"];
    allCookies["kuaishou.kwaishop.token"] = loginData.passToken;
    allCookies["ud"] = loginData.userId;
    allCookies["kpf"] = "IPHONE";
    allCookies["appver"] = "6.6.20.1231";
    allCookies["sid"] = "kuaishou.server.webday7";
    allCookies["ssecurity"] = "gDzgCVYe5U9xUaHLNAnp6Q==";

    // 将cookie对象转换为字符串格式
    const cookieString = Object.entries(allCookies)
      .map(([key, value]) => `${key}=${value}`)
      .join("; ");

    console.log("生成的cookie字符串长度:", cookieString);
    console.log("cookie内容预览:", cookieString.substring(0, 200) + "...");

    return cookieString;
  } catch (error) {
    console.error("获取cookie失败:", error);
    throw error;
  }
};

// 获取店铺商品数据
const fetchShopProductID = async ({
  sellerId,
  authorId,
  carrierType,
  carrierId,
  entrance,
  cookieContent,
}) => {
  try {
    console.log("开始获取店铺商品数据...");

    const requestData = {
      asyncToken: "false",
      globalParam: {
        sellerId: sellerId,
        selectionId: 0,
        viewType: 0,
        entrance: entrance || "p",
        carrierId: carrierId || "",
        carrierType: carrierType || "29",
        pageInstanceId: "0",
        plcParam: {},
        sceneCode: "DEFAULT",
        pageCode: "SHOP_ALL_PRODUCT",
        traceTag: "IRjwJtDeltXRB_kYoKo4zw",
        h5Token: true,
        productParam: {
          canShowPlcModule: true,
          productCardNew: true,
        },
        keyword: "冰箱",
        keyWord: "冰箱",
        search: "冰箱",
      },
      asyncParam: {
        keyword: "冰箱",
        keyWord: "冰箱",
        search: "冰箱",
      },
    };

    // 使用getSig4生成__NS_hxfalcon参数
    const sig4Params = {
      url: "/gateway/app/shop/page/product/render/v2",
      query: {
        caver: "1",
        keyWord: "冰箱"
      },
      form: {},
      requestBody: requestData,
      projectInfo: {
        did: "web_ee6adc237b8644098c1aa0b3126d25d0",
        appKey: "nuojwbmH5T",
      },
    };

    let nsHxfalcon;
    nsHxfalcon = getSig4(sig4Params);
    console.log("生成的__NS_hxfalcon参数:", nsHxfalcon);

    // 使用Tauri的fetch避免CORS问题
    const response = await fetch(
      `https://app.kwaixiaodian.com/gateway/app/shop/page/product/render/v2?caver=1?keyWord=冰箱&__NS_hxfalcon=${nsHxfalcon}`,
      {
        method: "POST",
        headers: {
          Host: "app.kwaixiaodian.com",
          accept: "application/json, text/plain, */*",
          "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6",
          "cache-control": "no-cache",
          "content-type": "application/json",
          cookie:
            "odid=B5A8C40A-1E2C-42C9-9A82-0009A6E72682; lon=; kpf=IPHONE; power_mode=0; foreign=0; darkMode=true; isp=CMCC; token=26979443261d440a8b29a5c444a6b0ec-668845089; browseType=4; appver=13.2.40.9197; thermal=10000; ll_client_time=1753735082717.537; cdid_tag=2; cdidTag=2; mcc=46000; session_id=5C00ECB1-39C1-4AB0-AF16-556D66C55063; weblogger_switch=; os=16.1.1; kuaishou.h5_st=Cg5rdWFpc2hvdS5oNS5zdBKgAS8jNn3okw6Xf3rli8M3XsdkYynx0PJ1AE8d4Q5GGGoPis328KjztFwPTTjMcnhx_Z1pRNJXGlneKKwhkGqAOHNtBK842x65uIBt4Z_CmqjTCabZN0D-W9rhsQ6t06dNfAp3oSX_VfLDKoR6l3C6CdgmDb0CY9BHuAobwjvyABZKku_Zox3B5AtXshN8_W2C_xM9pTTAB_d398H6ta65lCgaEhjJyx43dk5d3a_MhbFPKNS9fCIgNy399C7uJqhHYXr24LlaPsXYyRAAzWGSvuU2ykJ3M1MoBTAB; userId=668845089; sh=2796; grant_browse_type=AUTHORIZED; c=a; yoda_wktest_httponly=test; oDid=B5A8C40A-1E2C-42C9-9A82-0009A6E72682; lat=; egid=DFPA687B37CAC540044C0DE15371DE5CC2264BF5B9223CCA667BCF03BB3852EA; ud=668845089; sid=5C00ECB1-39C1-4AB0-AF16-556D66C55063; yoda_wktest_rewrite=js; videoModelCrowdTag=1_100; pUid=rwEK260F9dz5FybmJDxYJcIiR77b5A07003074289d5036; ver=13.2; global_id=DFPA687B37CAC540044C0DE15371DE5CC2264BF5B9223CCA667BCF03BB3852EA; kpn=KUAISHOU; net=WIFI; cl=0; __NSWJ=; country_code=cn; ftt=; lkvr=OTZENjEwODItMEJBKjbpDGlPMTZ1L9Ivo94Y0V8sXBcUxCXGSqHBztuv5GajThRRRhY1iA; deviceBit=0; randomDid=B5A8C40A-1E2C-42C9-9A82-0009A6E72682; kuaishou.sixin.login_st=ChdrdWFpc2hvdS5zaXhpbi5sb2dpbi5zdBKgAecxBPbWsd93SSiJq6g0VJL8nrvw7_QdKO0Bqwp947LYFYiNJLpBeb5hfdeHadWAHhjIQqklWvBjxIDaf12UxbTK8zDIeqrduCEqMlfgTH6EYusqM4v1Jq-0ALnszASFuAk4n9fADIzIOt9rPDZUd2-UBfym3Kpn5qlZ1FF6K2Y2QQGvIzP12voJKlAY8SL1dPAb4K8PmApgx2O6BLayQxoaEqX_RFYqBE1kredwAzTIg-2CUSIgRwGw4EQ0lsMyl2Y1mEK9hjdC7HrTkbfpFKAt-4KbhaQoBTAB; kuaishou.api_st=Cg9rdWFpc2hvdS5hcGkuc3QSoAHJR9W_nKLHOyxUGliD8NBAW6O_uOkW6q4oftBD5yVkX-4ag7dRmjprMEbgcZ0c2HPf6oG4ohU4Vf01jx2Rn6xxacKmKdOrkl2hxGYqu2cCH3MaunJUEvQEK2CZEgnmfW-kIgBhUxPj_9UD25RVONHl_P3elXOkNTIDh1FEyrr1a9c3kML13b89vLPg9XkEA1k2MmiPzxzRv3KPr9XpEqJtGhJvFHrrKslACKKghHE7NlCD-YgiIA07Gc1hsePq-CyhuAI8ZPEumQYVhZyMBb4286o6_PsNKAUwAQ; ll=; uQaTag=3%2333333333339999999999%23swLdgl%3A4-%23ecPp%3A79%23cmNt%3A11%23cmHs%3A5-%23cmMnsl%3A0-; gid=; rdid=B5A8C40A-1E2C-42C9-9A82-0009A6E72682; didTag=0; urlencode_mod=iPhone15%252C3; did_tag=0; client_key=56c3713c; did=616F0F81-8F07-998B-68DE-3695E82A5B43; cs=false; mod=iPhone15%2C3; language=zh-Hans-CN%3Bq%3D1; sys=iOS_16.1.1; userRecoBit=0; sw=1290; countryCode=cn",
          // cookie: cookieContent,
          dnt: "1",
          kpf: "OUTSIDE_IOS_H5",
          kpn: "unknown",
          "ktrace-str":
            "3|My40NTgzNjk4Mjg2NzM2NzY5LjE0MTI3NjcyLjE3NTM0NjE4MzYyMjcuMTAwMQ==|My40NTgzNjk4Mjg2NzM2NzY5LjQ1Mjg5NDE3LjE3NTM0NjE4MzYyMjcuMTAwMA==|0|plateco-kfx-service|plateco|true|src:Js,seqn:5555,rsi:e81933c3-88f9-4fe4-9f7e-d80d3c244f7b,path:/page/kwaishop-store-redesign-goodslist-app,rpi:3c3411bdf1",
          origin: "https://app.kwaixiaodian.com",
          pragma: "no-cache",
          priority: "u=1, i",
          referer:
            "https://app.kwaixiaodian.com/page/kwaishop-store-redesign-goodslist-app",
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-origin",
          "User-Agent":
            "Mozilla/5.0 (iPhone; CPU iPhone OS 16_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Kwai/13.2.40.9197 ISLP/0 StatusHT/59 KDT/PHONE iosSCH/0 TitleHT/44 NetType/WIFI ISDM/1 ICFO/0 locale/zh-Hans CT/0 Yoda/3.1.1 ISLB/0 CoIS/2 ISLM/0 WebViewType/WK BHT/102 AZPREFIX/az1",
        },
        body: JSON.stringify(requestData),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log("商品列表:", data.data.data.itemAllList_0.fields);

    return data.data.data.itemAllList_0.fields.data.itemList[0].itemId;
    // 检查数据结构是否正确
  } catch (error) {
    console.log("获取店铺商品失败", error);
    ElMessage.error("获取店铺商品失败: " + error.message);
    return null;
  }
};

// 测试 Fetch 调试功能
const testFetchDebug = async () => {
  try {
    console.log("🔍 开始 Fetch 调试测试...");
    ElMessage.info("开始 Fetch 调试测试，请查看控制台");

    // 测试 1: 简单的 GET 请求
    console.log("=== 测试 1: 简单 GET 请求 ===");
    const response1 = await fetch("https://httpbin.org/get", {
      method: "GET",
      headers: {
        "User-Agent": "Tauri-Debug/1.0"
      }
    });

    console.log("GET 请求状态:", response1.status);
    console.log("GET 请求 OK:", response1.ok);

    console.log("开始读取 GET 响应体...");
    const text1 = await response1.text();
    console.log("GET 响应体读取成功，长度:", text1.length);

    // 测试 2: POST 请求 (模拟 authRequest)
    console.log("=== 测试 2: POST 请求 (模拟 authRequest) ===");
    const response2 = await fetch("https://httpbin.org/post", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      body: JSON.stringify({
        test: "data",
        timestamp: Date.now()
      })
    });

    console.log("POST 请求状态:", response2.status);
    console.log("POST 请求 OK:", response2.ok);

    console.log("开始读取 POST 响应体 (关键测试点)...");
    const text2 = await response2.text();
    console.log("POST 响应体读取成功，长度:", text2.length);

    // 测试 3: JSON 解析
    console.log("=== 测试 3: JSON 解析 ===");
    try {
      const jsonData = JSON.parse(text2);
      console.log("JSON 解析成功:", jsonData);
    } catch (parseError) {
      console.error("JSON 解析失败:", parseError);
    }

    ElMessage.success("Fetch 调试测试完成，所有测试通过！");
    console.log("✅ 所有 Fetch 测试完成");

  } catch (error) {
    console.error("❌ Fetch 调试测试失败:", error);
    console.error("错误类型:", error.constructor.name);
    console.error("错误消息:", error.message);
    console.error("错误堆栈:", error.stack);

    ElMessage.error(`Fetch 调试测试失败: ${error.message}`);
  }
};

// 测试商品推荐接口 - 使用 test-7-31.py 的逻辑和 Rust utils 方法
const testRecommendationAPI = async (successResultsWithGoods) => {
  try {
    console.log("开始测试商品推荐接口...");

    if (!successResultsWithGoods || successResultsWithGoods.length === 0) {
      console.warn("没有可用的视频数据");
      return;
    }

    // 从第一个视频数据中提取参数
    const firstVideo = successResultsWithGoods[0];
    const videoData = firstVideo.data;

    // 提取关键参数
    const detailID = videoData.detailID; // 3xbn4qs5fkfq6qc
    const authorID = videoData.authorID; // 3xfyrsi7wia35ek
    const photoId = videoData.shopping_data?.find(item => item.type === "user_shop")?.photoId || "5216857383469532746";
    const userId = videoData.shopping_data?.find(item => item.type === "user_shop")?.userId || "4808775036";

    console.log("提取的参数:", {
      detailID,
      authorID,
      photoId,
      userId
    });

    // 根据 test-7-31.py 设置请求头
    const headers = {
      "User-Agent": "kwai-android aegon/3.18.0",
      "Content-Type": "application/x-www-form-urlencoded",
      "Accept-Language": "zh-cn",
      "X-REQUESTID": "175137438817733451",
      "X-Client-Info": "model=SM-S9260;os=Android;nqe-score=34;network=WIFI;signal-strength=4;"
    };

    // 根据 test-7-31.py 设置 cookies
    const cookies = {
      "kuaishou.api_st": "Cg9rdWFpc2hvdS5hcGkuc3QSoAGyQr5_Pxnjcm0SZq8ikMsn3acDSjCQZqq-p9vfzoLnXW5OGHXH4G9LEhXfIKI_LJk2KNPmZYT1tt1l4Q_zET5jJK7sXwoDbjdCA-_YVZ38bHJiMNWYuJyYnkKqrlsRV5IpFHK0MjvPvh1N5Te-DQCl8aFWUf-j7ClLSBwHDnUaCdtfmQ4DQOHfvAt6FemPsnhlecbJEIrxMr0weFsMKR_JGhJvFHrrKslACKKghHE7NlCD-YgiIHn9vQ8gP9PW2smphT9q6vKdBy6RfR4ti1k4lGgSX5NeKAUwAQ",
      "token": "6314caebe22b42dbba9b4185beebffd3-4879352811"
    };

    // 根据 test-7-31.py 设置查询参数
    const params = {
      "earphoneMode": "1",
      "mod": "Samsung(SM-S9260)",
      "appver": "10.10.10.28300",
      "isp": "CMCC",
      "language": "zh-cn",
      "ud": "3166429095",
      "did_tag": "0",
      "egid": "DFPE6BFB11F8AF9B59EF311E914B3763C6243FCEF5A4D5F36E23934860814118",
      "net": "WIFI",
      "kcv": "1599",
      "app": "0",
      "kpf": "ANDROID_PHONE",
      "bottom_navigation": "false",
      "ver": "10.10",
      "oDid": "ANDROID_752b3dcfda26f942",
      "android_os": "0",
      "boardPlatform": "aosp-user",
      "kpn": "KUAISHOU",
      "androidApiLevel": "28",
      "newOc": "ALI_CPD,666",
      "slh": "0",
      "country_code": "cn",
      "nbh": "0",
      "hotfix_ver": "",
      "did_gt": "1751320183207",
      "keyconfig_state": "2",
      "cdid_tag": "5",
      "sys": "ANDROID_9",
      "max_memory": "192",
      "cold_launch_time_ms": "1751373895175",
      "oc": "ALI_CPD,666",
      "sh": "1920",
      "ddpi": "480",
      "deviceBit": "0",
      "browseType": "4",
      "socName": "Qualcomm MSM8998",
      "is_background": "0",
      "c": "ALI_CPD,666",
      "sw": "1080",
      "ftt": "",
      "abi": "arm32",
      "userRecoBit": "0",
      "device_abi": "",
      "totalMemory": "3945",
      "grant_browse_type": "AUTHORIZED",
      "iuid": "",
      "rdid": "ANDROID_5f011e02f6f2798b",
      "sbh": "72",
      "darkMode": "false",
      "did": "7221BE95-A362-460A-A2C3-AA0E8498A79D"
    };

    // 根据 test-7-31.py 设置数据参数
    const data = {
      'photoId': photoId,
      'userId': userId,
      'pageSource': '0',
      'displayType': '3',
      'client_key': '3c2cd3f3'
    };

    // 使用 Rust utils 方法生成签名
    console.log("开始生成签名...");

    // 1. 生成 MD5 签名
    const sig = await invoke('sig_py', { queryParams: params, dataParams: data });
    console.log('sig-->', sig);

    // 2. 构建 encurl 用于生成 sig3
    const encurl = '/rest/n/feed/selection/profile/position' + sig;
    console.log('encurl-->', encurl);

    // 3. 使用 JAR 文件生成 sig3
    const sig3Result = await invoke('get_sig3', { param: encurl });
    console.log('sig3 result-->', sig3Result);

    if (!sig3Result.success) {
      throw new Error(`生成 sig3 失败: ${sig3Result.error}`);
    }
    const sig3 = sig3Result.signature;

    // 4. 生成 token
    const client_salt = '808e3d5a9fe2cf0e7342c0151d2fab85';
    const token = await invoke('token_py', { sig: sig, clientSalt: client_salt });
    console.log('__NStokensig-->', token);

    // 5. 将生成的签名添加到参数中
    params['sig'] = sig;
    params['__NS_sig3'] = sig3;
    params['__NStokensig'] = token;

    // 根据 test-7-31.py 构建 URL 和请求
    const url = "https://api3.ksapisrv.com/rest/n/feed/selection/profile/position";

    // 构建查询参数字符串
    const queryString = new URLSearchParams(params).toString();
    const fullUrl = `${url}?${queryString}`;

    // 构建请求体 (form data)
    const requestBody = new URLSearchParams(data);

    console.log("请求 URL:", fullUrl);
    console.log("请求体:", requestBody.toString());
    console.log("请求头:", headers);
    console.log("Cookies:", cookies);

    // 构建 Cookie 字符串
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    // 发送请求 - 根据 test-7-31.py 的逻辑
    const response = await fetch(fullUrl, {
      method: "POST",
      headers: {
        ...headers,
        "Cookie": cookieString
      },
      body: requestBody.toString()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseData = await response.json();
    console.log("商品推荐接口响应:", responseData);

    // 检查响应数据结构 - 根据实际 API 响应调整
    if (responseData) {
      console.log("API 响应成功");
      ElMessage.success("商品推荐接口调用成功");

      // 显示响应的基本信息
      console.log("响应状态:", response.status);
      console.log("响应数据:", responseData);

      // 如果有特定的数据结构，可以在这里处理
      if (responseData.result) {
        console.log("API 返回结果:", responseData.result);
      }

      if (responseData.data) {
        console.log("API 返回数据:", responseData.data);
      }
    } else {
      console.warn("推荐接口返回数据为空:", responseData);
      ElMessage.warning("商品推荐接口返回数据为空");
    }

  } catch (error) {
    console.error("商品推荐接口调用失败:", error);
    ElMessage.error(`商品推荐接口调用失败: ${error.message}`);
  }
};

// 视频详情弹窗相关状态
const videoDetailDialog = reactive({
  visible: false,
  data: null,
  originalUrl: "",
  isMultiple: false,
  allResults: [],
  activeTab: "0"
});

// 显示单个视频详情弹窗
const showVideoDetailDialog = (videoData, originalUrl) => {
  videoDetailDialog.data = videoData;
  videoDetailDialog.originalUrl = originalUrl;
  videoDetailDialog.isMultiple = false;
  videoDetailDialog.allResults = [];
  videoDetailDialog.activeTab = "0";
  videoDetailDialog.visible = true;
};

// 显示多个视频结果弹窗
const showMultiVideoResults = (results) => {
  videoDetailDialog.data = null;
  videoDetailDialog.originalUrl = "";
  videoDetailDialog.isMultiple = true;
  videoDetailDialog.allResults = results;
  videoDetailDialog.activeTab = "0";
  videoDetailDialog.visible = true;
};

// 关闭视频详情弹窗
const closeVideoDetailDialog = () => {
  videoDetailDialog.visible = false;
  videoDetailDialog.data = null;
  videoDetailDialog.originalUrl = "";
  videoDetailDialog.isMultiple = false;
  videoDetailDialog.allResults = [];
  videoDetailDialog.activeTab = "0";
};

const getVideoCoverUrl = (videoData) => {
  return videoData?.coverUrl || '';
};
// // 获取视频封面URL（优先使用入库后的封面图）
// const getVideoCoverUrl = (videoData) => {
//   // 优先使用入库后生成的封面图
//   if (videoData?.database_record?.cover_image) {
//     // 如果是相对路径，转换为可访问的URL
//     if (videoData?.database_record?.cover_image.startsWith('backends/videoFile/')) {
//       // 提取文件名
//       const filename = videoData?.database_record?.cover_image.replace('backends/videoFile/', '');
//       return `http://localhost:5409/getFile?filename=${encodeURIComponent(filename)}`;
//     } else if (videoData?.database_record?.cover_image.startsWith('backends/')) {
//       // 其他backends路径的处理
//       const filename = videoData?.database_record?.cover_image.split('/').pop();
//       return `http://localhost:5409/getFile?filename=${encodeURIComponent(filename)}`;
//     }
//     return videoData.cover_path;
//   }

//   // 备选：使用原始封面图
//   return videoData.cover_image || videoData.coverUrl || '';
// };



// 复制下载链接
const copyDownloadLink = async (url) => {
  try {
    await navigator.clipboard.writeText(url);
    ElMessage.success("下载链接已复制到剪贴板");
  } catch (error) {
    ElMessage.error("复制失败，请手动复制");
  }
};

// 打开下载链接
const openDownloadLink = (url) => {
  window.open(url, "_blank");
};

// 查看原链接
const viewOriginalLink = () => {
  window.open(videoDetailDialog.originalUrl, "_blank");
};

// 查看视频文件夹 - 参考MaterialManagement.vue的goToFolder功能
const goToVideoFolder = async (videoData) => {
  console.log(videoData);
  try {
    // 使用videoData中的file_path或通过detailID构建路径
    let filePath = "";

    // 适配新的数据结构，优先使用filepath，然后尝试existing_video_path
    if (videoData.filepath) {
      filePath = videoData.filepath;
    } else if (videoData.existing_video_path) {
      filePath = videoData.existing_video_path;
    } else {
      ElMessage.warning("未找到视频文件路径");
      return;
    }

    await materialApi.openFolder({ filePath: filePath });
    ElMessage.success("已打开视频文件夹: " + filePath);
  } catch (error) {
    console.error("打开文件夹失败:", error);
    ElMessage.error("打开文件夹失败: " + error.message);
  }
};

// 去发布功能 - 参考MaterialManagement.vue的handleSelectionABPush功能
const goToMaterial = async (videoData) => {
  try {
    console.log("准备跳转到发布中心，视频数据:", videoData);

    // 使用接口返回的素材ID跳转到发布中心
    await router.push({
      path: "/material-management",
    });

    // 关闭详情弹窗
    closeVideoDetailDialog();

    ElMessage.success("已跳转到发布中心");
  } catch (error) {
    console.error("跳转到发布中心失败:", error);
    ElMessage.error("跳转失败: " + error.message);
  }
};

// 格式化播放量
const formatViewCount = (count) => {
  if (count === -1) return "未知";
  if (typeof count === "string") return count;
  if (count >= 10000) return `${(count / 10000).toFixed(1)}万`;
  return count.toString();
};

// 格式化时长
const formatDuration = (duration) => {
  return duration || "未知";
};

// 添加商品到橱窗的独立方法
const addGoodsToShowcase = async (goodsId) => {
  try {
    console.log("开始将商品添加到快手橱窗...");
    ElMessage.info(`正在将商品 ${goodsId} 添加到橱窗...`);

    const addGoodsResult = await fetch(
      "http://localhost:5409/addGoodsToShowcase",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          goodsId: goodsId,
        }),
      }
    );

    const addGoodsResponse = await addGoodsResult.json();
    console.log("添加商品到橱窗结果:", addGoodsResponse);

    if (addGoodsResponse.code === 200) {
      const { success_count, total_accounts } = addGoodsResponse.data;
      ElMessage.success(
        `成功将商品添加到 ${success_count}/${total_accounts} 个快手账号的橱窗`
      );

      return {
        success: true,
        data: addGoodsResponse.data,
        msg: `成功将商品添加到 ${success_count}/${total_accounts} 个快手账号的橱窗`,
      };
    } else {
      ElMessage.error(`添加商品到橱窗失败: ${addGoodsResponse.msg}`);
      return {
        success: false,
        msg: `添加商品到橱窗失败: ${addGoodsResponse.msg}`,
      };
    }
  } catch (addGoodsError) {
    console.error("添加商品到橱窗出错:", addGoodsError);
    const errorMsg =
      addGoodsError?.message ||
      (typeof addGoodsError === "string"
        ? addGoodsError
        : JSON.stringify(addGoodsError));
    return {
      success: false,
      msg: "添加商品到橱窗失败: " + errorMsg,
    };
  }
};

// 测试店铺商品获取功能（旧版本，用于按钮调用）
const testShopProductsOld = async () => {
  let goodsId = null;

  const successResultsWithGoods = [
    {
        "data": {
            "account_count": 2,
            "authorID": "3xfyrsi7wia35ek",
            "caption": "#冰箱控温器",
            "collection_time": "2025-07-31_19:34:37",
            "commentCount": 195,
            "coverUrl": "https://p2.a.yximgs.com/upic/2025/07/12/22/BMjAyNTA3MTIyMjA2MjZfODczNzMwNl8xNjkyODMyMzc1MzJfMl8z_ev2_Bed1680ff57f65f29fb30622488d637ca.jpg?clientCacheKey=3xbn4qs5fkfq6qc_ev2.jpg&di=e913f01&bp=10002",
            "database_record": {
                "cover_image": "D:\\peoject\\test\\backends\\videoFile\\56cdbb52-6e02-11f0-93ec-10ffe081bf89_cover.jpg",
                "filename": "3xbn4qs5fkfq6qc_时光小清新_#冰箱控温器.mp4",
                "filepath": "56cdbb52-6e02-11f0-93ec-10ffe081bf89_3xbn4qs5fkfq6qc_时光小清新_#冰箱控温器.mp4",
                "filesize": 19.67,
                "id": 166
            },
            "detailID": "3xbn4qs5fkfq6qc",
            "download": "https://v2.kwaicdn.com/upic/2025/07/12/22/BMjAyNTA3MTIyMjA2MjZfODczNzMwNl8xNjkyODMyMzc1MzJfMl8z_b_B1198d03e0cda67eb4262b55782acc356.mp4?pkey=AAVIRt44XDBGkbhJvqjcQ72sET1r6cWS8uyAwlnsd1tDdGC3cCqoeSNLNSk_MLKwel8xGB6AThsyeuJB1jds6GIrsB7aJIre8g6p0RG6hiHj1X_R68e9jyC99OIp-L8x2IA&tag=1-1753961675-unknown-0-coye1g7ow4-8931bd92af6e0493&clientCacheKey=3xbn4qs5fkfq6qc_b.mp4&di=e913f01&bp=10000&kwai-not-alloc=40&tt=b&ss=vpm",
            "duration": "00:01:37",
            "existing_video_path": "D:\\peoject\\test\\backends\\videoFile\\56cdbb52-6e02-11f0-93ec-10ffe081bf89_3xbn4qs5fkfq6qc_时光小清新_#冰箱控温器.mp4",
            "final_filepath": "D:\\peoject\\test\\backends\\videoFile\\56cdbb52-6e02-11f0-93ec-10ffe081bf89_3xbn4qs5fkfq6qc_时光小清新_#冰箱控温器.mp4",
            "link_index": 0,
            "link_url": "https://v.kuaishou.com/JIzaeLWZ",
            "name": "时光小清新",
            "photoType": "视频",
            "realLikeCount": 3907,
            "shareCount": 2708,
            "shopping_data": [
                {
                    "bizType": 3,
                    "category": "购物",
                    "categoryType": 27,
                    "iconUrl": "https://ali2.a.yximgs.com/udata/pkg/plc/xhc_new_icon.png",
                    "source": "init_state_plc",
                    "title": "冰箱智能温控器",
                    "type": "product_info"
                },
                {
                    "authorId": "8737306",
                    "carrierId": "3xfyrsi7wia35ek",
                    "carrierType": 27,
                    "kwaiId": "jinzi0605",
                    "link": "https://app.kwaixiaodian.com/page/kwaishop-store-c-frame-h5/frame?layoutType=4&hyId=kwaishop-store-c-frame-h5&sellerId=8737306&authorId=8737306&carrierType=27&carrierId=3xfyrsi7wia35ek&entrance=p&_refer=KWAISHOP_C_SHOPLIST",
                    "photoId": "5193776437779847730",
                    "sellerId": "8737306",
                    "source": "init_state_photo",
                    "title": "时光小清新的小店",
                    "type": "user_shop",
                    "userEid": "3xfyrsi7wia35ek",
                    "userName": "时光小清新"
                }
            ],
            "timestamp": "2025-07-12_22:07:03",
            "userSex": "女",
            "video_file_path": "D:\\peoject\\test\\backends\\videoFile\\56cdbb52-6e02-11f0-93ec-10ffe081bf89_3xbn4qs5fkfq6qc_时光小清新_#冰箱控温器.mp4",
            "viewCount": 692995
        },
        "success": true
    }
];

  console.log("获取到的商品ID:", goodsId);

  // 测试商品推荐接口
  await testRecommendationAPI(successResultsWithGoods);

  // const publishResult = await materialApi.publishVideos({
  //   videoResults: successResultsWithGoods, // 传递包含商品ID的视频结果
  // });

  // console.log("发布结果:", publishResult);

  // if (publishResult && publishResult.code === 200) {
  //   ElMessage.success(
  //     `发布完成: ${publishResult.data.successful_videos} 个视频成功发布`
  //   );
  // } else {
  //   ElMessage.warning(`发布失败: ${publishResult?.msg || "未知错误"}`);
  // }
};

// 解析链接并提取视频详情的方法
const onSubmitOneClick = async () => {
  console.log("开始一键搬运:", oneClickFormUrl);

  if (!oneClickFormUrl.shareText.trim()) {
    ElMessage.warning("请输入分享链接");
    return;
  }

  // 检查是否已登录
  if (!loginState.isLoggedIn) {
    ElMessage.warning("请先登录快手账号");
    return;
  }

  // 检查自动发布时AB去重必须开启
  if (oneClickFormUrl.autoPublish && !oneClickFormUrl.autoAB) {
    ElMessage.warning("选择自动发布时，AB去重必须开启");
    return;
  }

  // 设置表单loading状态
  oneClickLoading.value = true;

  try {
    // 1. 从登录数据中获取cookie
    console.log("正在从登录数据获取cookie...");

    const cookieContent = getCookieStringFromLoginData();
    if (!cookieContent) {
      throw new Error("未获取到有效的 cookie 信息");
    }

    console.log("获取到登录cookie内容长度:", cookieContent.length);

    // 2. 解析输入文本中的链接
    const links = extractLinksFromText(oneClickFormUrl.shareText);
    console.log("解析出的链接:", links);

    if (links.length === 0) {
      ElMessage.warning("未找到有效链接");
      return;
    }

    // 3. 第一步：处理视频链接（批量处理）
    console.log("步骤1: 处理视频链接...");
    ElMessage.info("正在处理视频链接，请稍候...");

    // 准备封面标题参数
    let coverTitleParam = oneClickFormUrl.coverTitle.trim();
    let coverFontColorParam = oneClickFormUrl.coverFontColor;
    let coverTitleLinesParam = null;

    // 使用多行颜色配置
    if (titleLines.value && titleLines.value.length > 0) {
      const validLines = titleLines.value.filter(line => line.text && line.text.trim());
      if (validLines.length > 0) {
        coverTitleLinesParam = validLines.map(line => ({
          text: line.text.trim(),
          color: line.color,
          fontSize: line.fontSize || 72,
          spacing: line.spacing || 10
        }));
        // 同时更新传统的标题字段（用于兼容）
        coverTitleParam = validLines.map(line => line.text.trim()).join('\n');
      }
    }

    const processResult = await materialApi.processVideoLinks({
      urls: links,
      cookie: cookieContent,
      download: true,
      videoGenerationKey: oneClickFormUrl.videoGenerationKey,
      autoAB: oneClickFormUrl.autoAB,
      coverTitle: coverTitleParam, // 封面标题
      customCoverPath: oneClickFormUrl.customCoverPath.trim(), // 自定义封面图片路径
      coverFontSize: oneClickFormUrl.coverFontSize, // 封面标题字体大小
      coverFontColor: coverFontColorParam, // 封面标题字体颜色（兼容字段）
      coverTitleLines: coverTitleLinesParam, // 多行颜色配置
    });

    console.log("视频处理结果:", processResult);

    if (!processResult || processResult.code !== 200) {
      throw new Error(processResult?.msg || "视频处理失败");
    }

    const { data: processData } = processResult;
    ElMessage.success(
      `成功处理 ${processData.success_count}/${processData.total_links} 个视频`
    );
    // 每个视频的处理结果 - 使用 for...of 支持 async/await
    // 用于存储视频结果和对应的商品ID
    const videoResultsWithGoods = [];

    for (const processResult of processData.results) {
      console.log("处理结果:", processResult);
      if (processResult.success) {
        // 取出店铺 - 筛选目标店铺
        const currentUserId = loginState.userInfo.userId; // 当前登录用户ID
        const shoppingData = processResult.data.shopping_data || [];

        // 筛选目标店铺：type=user_shop 且 sellerId != 当前用户ID
        const targetShops = shoppingData.find(
          (item) => item.type === "user_shop" && item.sellerId != currentUserId
        );

        let goodsId = null;

        if (targetShops) {
          console.log("找到目标店铺:", targetShops);
          const shopLink = targetShops.link;
          // 参考格式: https://app.kwaixiaodian.com/page/kwaishop-store-c-frame-h5/frame?layoutType=4&hyId=kwaishop-store-c-frame-h5&sellerId=8737306&authorId=8737306&carrierType=29&carrierId=v1mLnht1qSI&entrance=p&_refer=KWAISHOP_C_SHOPLIST

          // 从shopLink中提取参数
          try {
            const url = new URL(shopLink);
            const params = new URLSearchParams(url.search);

            const sellerId = params.get("sellerId");
            const authorId = params.get("authorId");
            const carrierType = params.get("carrierType");
            const carrierId = params.get("carrierId");
            const entrance = params.get("entrance");

            console.log("店铺参数:", {
              sellerId,
              authorId,
              carrierType,
              carrierId,
              entrance,
            });

            if (sellerId) {
              // 请求店铺商品id
              goodsId = await fetchShopProductID({
                sellerId,
                authorId,
                carrierType,
                carrierId,
                entrance,
                cookieContent,
              });
              console.log("获取到的商品ID:", goodsId);

              // 获取到商品ID后，调用添加到橱窗的接口
              if (goodsId) {
                const addResult = await addGoodsToShowcase(goodsId);
                // 只有成功添加到橱窗的商品才保留商品ID，否则设置为null
                if (!addResult.success) {
                  console.warn("商品添加到橱窗失败，该视频将跳过自动发布");
                  goodsId = null; // 添加失败，清空商品ID
                }
              } else {
                ElMessage.warning("未获取到有效的商品ID，无法添加到橱窗");
                goodsId = null;
              }
            }
          } catch (error) {
            console.error("解析店铺链接失败:", error);
            ElMessage.error("解析店铺链接失败");
          }
        } else {
          console.log("未找到目标店铺，视频无对应商品");
        }

        // 将视频结果和商品ID关联起来，用于后续发布
        videoResultsWithGoods.push({
          ...processResult,
          goodsId: goodsId, // 添加商品ID到视频结果中
        });
      } else {
        ElMessage.error(`视频处理失败: ${processResult.msg || "未知错误"}`);
      }
    }

    // 4. 检查是否需要自动发布
    if (
      oneClickFormUrl.autoPublish &&
      oneClickFormUrl.videoGenerationKey.trim()
    ) {
      console.log("步骤2: 自动发布视频...");
      ElMessage.info("正在发布视频到快手平台...");

      // 只发布成功处理且商品成功添加到橱窗的视频
      const successResultsWithGoods = videoResultsWithGoods.filter(
        (result) => result.success && result.goodsId // 必须有有效的商品ID
      );

      const skippedVideos = videoResultsWithGoods.filter(
        (result) => result.success && !result.goodsId // 成功处理但商品添加失败的视频
      );

      if (Array.isArray(skippedVideos) && skippedVideos.length > 0) {
        ElMessage.warning(
          `${skippedVideos.length} 个视频因商品添加失败将跳过自动发布`,
          { duration: 5000 }
        );
      }

      if (successResultsWithGoods.length > 0) {
        console.log("准备发布的视频（包含商品ID）:", successResultsWithGoods);

        const publishResult = await materialApi.publishVideos({
          videoResults: successResultsWithGoods, // 传递包含商品ID的视频结果
        });

        console.log("发布结果:", publishResult);

        if (publishResult && publishResult.code === 200) {
          ElMessage.success(
            `发布完成: ${publishResult.data.successful_videos} 个视频成功发布`
          );
        } else {
          ElMessage.warning(`发布失败: ${publishResult?.msg || "未知错误"}`);
        }
      } else {
        ElMessage.warning("没有可发布的视频（所有视频的商品都添加失败）");
      }
    }

    // 5. 显示处理结果（显示所有成功处理的视频详情）
    const successResults = videoResultsWithGoods.filter(
      (result) => result.success
    );

    if (successResults.length > 0) {
      // 如果只有一个视频，直接显示详情弹窗
      if (successResults.length === 1) {
        showVideoDetailDialog(
          successResults[0].data,
          successResults[0].data.link_url
        );
      } else {
        // 多个视频时，显示多视频结果弹窗
        showMultiVideoResults(successResults);
      }
    }

    ElMessage.success("视频搬运处理完成！");
  } catch (error) {
    console.error("一键搬运出错:", error);
    ElMessage.error("一键搬运失败: " + error.message);
  } finally {
    oneClickLoading.value = false;
  }
};

const onSubmitABFolder = async () => {
  console.log("submit!", ABFormFolderInline);
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  try {
    const response = await materialApi.changeABByFolder(ABFormFolderInline);

    if (response.code === 200) {
      fileList.value = response.data; // 更新文件列表
      console.log("去重后文件列表:", fileList.value);
      // 跳转并传递id参数
      const ids = Array.isArray(response.data)
        ? response.data.map((item) => item.id).join(",")
        : response.data.id;
      // 弹窗询问是否跳转
      ElMessageBox.confirm(
        `AB去重成功，成功去重${response.data.length} 条主视频，是否前往渠道发布页面？`,
        "操作提示",
        {
          confirmButtonText: "去发布",
          cancelButtonText: "去查看",
          type: "info",
          closeOnClickModal: false,
        }
      )
        .then(() => {
          router.push({
            path: "/publish-center",
            query: { ids },
          });
        })
        .catch(() => {
          // 用户取消，不跳转
          router.push({
            path: "/material-management",
            query: { ids },
          });
        });
    }
  } catch (error) {
    console.error("快手AB帧去重出错:", error);
    ElMessage.error("快手AB帧去重失败");
  } finally {
    loading.close();
  }
};
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.dashboard {
  .page-header {
    margin-bottom: 20px;

    h1 {
      font-size: 24px;
      color: $text-primary;
      margin: 0;
    }
  }

  .dashboard-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 380px;
    flex: 1;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .uploaded-files {
    margin-top: 20px;

    h4 {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 12px;
      color: #303133;
    }

    .file-list {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .file-item {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background-color: #f5f7fa;
        border-radius: 4px;

        .el-link {
          margin-right: 10px;
          max-width: 300px;
          overflow: hidden;
        }

        .file-size {
          color: #909399;
          font-size: 13px;
          margin-right: 10px;
        }
      }
    }
  }
}

// 多视频内容样式
.multi-video-content {
  .result-summary {
    margin-bottom: 16px;
  }

  .video-tabs {
    .el-tabs__header {
      margin-bottom: 16px;
    }

    .el-tab-pane {
      padding: 0;
    }
  }
}

// 视频详情弹窗样式
.video-detail-content {
  .video-main-info {
    display: flex;
    gap: 16px;
    align-items: flex-start;

    .cover-container {
      flex-shrink: 0;
      width: 120px;

      .cover-image {
        width: 120px;
        border-radius: 8px;
        object-fit: cover;
      }

      .image-error {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 160px;
        background-color: #f5f7fa;
        border-radius: 8px;
        color: #909399;
        font-size: 12px;

        .el-icon {
          font-size: 24px;
          margin-bottom: 6px;
        }
      }
    }

    .video-info {
      flex: 1;
      min-width: 0;

      .video-title {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        line-height: 1.4;
        word-break: break-all;
      }

      .video-stats {
        margin-bottom: 12px;

        .stat-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 6px;
          font-size: 14px;

          .stat-label {
            color: #909399;
            font-weight: 500;
          }

          .stat-value {
            color: #606266;
            font-weight: 600;
          }
        }
      }

      .video-caption {
        margin-bottom: 16px;

        p {
          margin: 0;
          font-size: 13px;
          line-height: 1.5;
          color: #666;
          word-break: break-all;
          max-height: 60px;
          overflow-y: auto;
          background: #f8f9fa;
          padding: 8px;
          border-radius: 4px;
        }
      }

      .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}

// 高级选项样式
.advanced-options {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e9ecef;

  .el-form-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
