#!/usr/bin/env python3
"""
专门为包含 KS-Downloader 和 services 的项目优化的 Nuitka 打包脚本
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def get_playwright_info():
    """获取 Playwright 安装信息"""
    try:
        import playwright
        playwright_dir = Path(playwright.__file__).parent
        print(f"Playwright 模块路径: {playwright_dir}")
        
        # 查找浏览器安装目录
        ms_playwright_dir = Path.home() / "AppData" / "Local" / "ms-playwright"
        if ms_playwright_dir.exists():
            print(f"Playwright 浏览器目录: {ms_playwright_dir}")
            
            # 查找 chromium 目录
            chromium_dirs = []
            for item in ms_playwright_dir.iterdir():
                if item.is_dir() and "chromium" in item.name:
                    chromium_dirs.append(item)
            
            if chromium_dirs:
                # 选择最新的 chromium 版本
                latest_chromium = max(chromium_dirs, key=lambda x: x.name)
                print(f"最新 Chromium 目录: {latest_chromium}")
                return playwright_dir, ms_playwright_dir, latest_chromium
        
        return playwright_dir, None, None
        
    except ImportError:
        print("错误: 未安装 Playwright")
        return None, None, None

def prepare_build_environment():
    """准备构建环境"""
    print("准备构建环境...")
    
    # 清理旧的构建文件
    build_dirs = ["build", "out", "dist"]
    for build_dir in build_dirs:
        if os.path.exists(build_dir):
            print(f"清理旧的构建目录: {build_dir}")
            try:
                if os.name == "nt":
                    result = subprocess.run(
                        ["powershell", "-Command", f"Remove-Item -Recurse -Force '{build_dir}' -ErrorAction SilentlyContinue"],
                        capture_output=True,
                        text=True
                    )
                    if result.returncode == 0:
                        print(f"  成功清理 {build_dir}")
                    else:
                        print(f"  清理 {build_dir} 时有警告: {result.stderr}")
                else:
                    shutil.rmtree(build_dir)
            except Exception as e:
                print(f"警告: 清理目录 {build_dir} 时出错: {e}")
    
    # 确保必要的目录存在
    os.makedirs("out", exist_ok=True)
    print("构建环境准备完成")
    return True

def build_with_nuitka():
    """使用 Nuitka 进行打包"""
    print("开始 Nuitka 打包...")
    
    # 获取 Playwright 信息
    playwright_dir, ms_playwright_dir, chromium_dir = get_playwright_info()
    if not playwright_dir:
        print("错误: 无法获取 Playwright 信息")
        return False
    
    # 基础 Nuitka 命令 - 简化版本避免 DLL 检测问题
    nuitka_cmd = [
        "nuitka.cmd" if os.name == "nt" else "nuitka",
        "--standalone",
        "--show-progress",
        "--output-dir=out",
        "--windows-icon-from-ico=logo.ico",

        # 禁用 DLL 依赖缓存以避免路径问题
        "--disable-dll-dependency-cache",
        "--assume-yes-for-downloads",

        # 使用更简单的编译器
        "--mingw64",

        # 不跟踪某些包以避免问题
        "--nofollow-imports",

        # 明确跟踪项目相关的包
        "--follow-import-to=services",
        "--follow-import-to=utils",
        "--follow-import-to=src",

        # 跟踪必要的系统包
        "--follow-import-to=sqlite3",
        "--follow-import-to=asyncio",
        "--follow-import-to=pathlib",
        "--follow-import-to=logging",
        "--follow-import-to=socket",
        "--follow-import-to=psutil",
        "--follow-import-to=optparse",

        # 包含其他必要的包
        "--include-package=sqlite3",
        "--include-package=asyncio",
        "--include-package=pathlib",
        "--include-package=logging",
        "--include-package=json",
        "--include-package=urllib",
        "--include-package=ssl",
        "--include-package=certifi",
        "--include-package=socket",
        "--include-package=psutil",
        "--include-package=optparse",

        # 禁用一些可能有问题的优化
        "--plugin-disable=anti-bloat",

        # 主文件
        "main.py"
    ]
    
    # 如果找到了浏览器目录，包含它们
    if ms_playwright_dir and chromium_dir:
        # 包含整个 Playwright 驱动目录
        driver_dir = playwright_dir / "driver"
        if driver_dir.exists():
            nuitka_cmd.append(f"--include-data-dir={driver_dir}=playwright/driver")
        
        # 包含浏览器目录
        nuitka_cmd.append(f"--include-data-dir={chromium_dir}=playwright/driver/package/.local-browsers/{chromium_dir.name}")
    
    print("执行命令:", " ".join(nuitka_cmd))
    
    try:
        result = subprocess.run(nuitka_cmd, check=True, capture_output=True, text=True)
        print("Nuitka 打包成功!")
        if result.stdout:
            print("标准输出:", result.stdout[-2000:])  # 只显示最后2000字符
        return True
    except subprocess.CalledProcessError as e:
        print("Nuitka 打包失败!")
        print("错误输出:", e.stderr)
        if e.stdout:
            print("标准输出:", e.stdout[-2000:])
        return False

def post_build_setup():
    """构建后的设置"""
    print("执行构建后设置...")
    
    dist_dir = Path("out/main.dist")
    if not dist_dir.exists():
        print("构建目录不存在!")
        return False
    
    # 复制项目相关文件
    project_files = [
        "logo.ico",
        "cookiesFile",
        "videoFile",
        "database.db"
    ]
    
    # 复制 KS-Downloader 相关文件
    ks_files = [
        "KS-Downloader/config.yaml",
        "KS-Downloader/KS-Downloader.db",
        "KS-Downloader/locale",
        "KS-Downloader/Data"
    ]
    
    # 合并文件列表
    all_files = project_files + ks_files
    
    for file_path in all_files:
        if os.path.exists(file_path):
            try:
                if os.path.isdir(file_path):
                    dest = dist_dir / file_path
                    if dest.exists():
                        shutil.rmtree(dest)
                    shutil.copytree(file_path, dest)
                    print(f"复制目录: {file_path} -> {dest}")
                else:
                    dest_dir = dist_dir / Path(file_path).parent
                    dest_dir.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(file_path, dest_dir)
                    print(f"复制文件: {file_path} -> {dest_dir}")
            except Exception as e:
                print(f"复制失败 {file_path}: {e}")
    
    # 确保 Playwright 浏览器路径正确
    setup_playwright_in_dist(dist_dir)
    
    # 创建启动脚本
    create_launch_script(dist_dir)
    
    return True

def setup_playwright_in_dist(dist_dir):
    """在分发目录中设置 Playwright"""
    print("设置 Playwright 环境...")
    
    # 检查 Playwright 目录是否存在
    playwright_driver_dir = dist_dir / "playwright" / "driver"
    if not playwright_driver_dir.exists():
        print("警告: Playwright 驱动目录不存在，尝试手动复制...")
        
        # 尝试从系统中复制 Playwright 驱动
        try:
            import playwright
            source_driver = Path(playwright.__file__).parent / "driver"
            if source_driver.exists():
                dest_playwright = dist_dir / "playwright"
                dest_playwright.mkdir(exist_ok=True)
                shutil.copytree(source_driver, dest_playwright / "driver")
                print("成功复制 Playwright 驱动")
        except Exception as e:
            print(f"复制 Playwright 驱动失败: {e}")

def create_launch_script(dist_dir):
    """创建启动脚本"""
    script_content = """@echo off
echo ========================================
echo 小超媒体管理系统启动中...
echo ========================================
echo.

REM 设置环境变量
set PLAYWRIGHT_BROWSERS_PATH=%~dp0playwright\\driver\\package\\.local-browsers
set PLAYWRIGHT_DRIVER_PATH=%~dp0playwright\\driver

echo 启动主程序...
echo ========================================

REM 启动主程序
main.exe

REM 程序结束处理
echo.
echo ========================================
if errorlevel 1 (
    echo 程序异常退出，错误代码: %errorlevel%
    echo 请检查上方的错误信息
) else (
    echo 程序正常退出
)
echo ========================================
pause
"""
    
    script_path = dist_dir / "启动.bat"
    with open(script_path, 'w', encoding='gbk') as f:
        f.write(script_content)
    
    print(f"创建启动脚本: {script_path}")

def main():
    """主函数"""
    print("=" * 70)
    print("小超媒体管理系统 - KS-Downloader 专用打包工具")
    print("支持 services 模块和 KS-Downloader 集成")
    print("=" * 70)
    
    # 检查是否在正确的目录
    if not os.path.exists("main.py"):
        print("错误: 未找到 main.py 文件，请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 检查 KS-Downloader 目录
    if not os.path.exists("KS-Downloader"):
        print("错误: 未找到 KS-Downloader 目录")
        sys.exit(1)
    
    # 检查 services 目录
    if not os.path.exists("services"):
        print("错误: 未找到 services 目录")
        sys.exit(1)
    
    # 检查 Nuitka 是否安装
    nuitka_cmd = "nuitka.cmd" if os.name == "nt" else "nuitka"
    try:
        result = subprocess.run([nuitka_cmd, "--version"], check=True, capture_output=True, text=True)
        print("✓ Nuitka 已安装")
        print(f"  版本: {result.stdout.strip().split()[0]}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到 Nuitka，请先安装: pip install nuitka")
        sys.exit(1)
    
    # 检查 Playwright 是否安装
    try:
        import playwright
        print("✓ Playwright 已安装")
    except ImportError:
        print("错误: 未安装 Playwright，请先安装: pip install playwright")
        sys.exit(1)
    
    # 执行构建步骤
    steps = [
        ("准备构建环境", prepare_build_environment),
        ("Nuitka 打包", build_with_nuitka),
        ("构建后设置", post_build_setup),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*25} {step_name} {'='*25}")
        if not step_func():
            print(f"❌ 步骤失败: {step_name}")
            sys.exit(1)
        print(f"✓ {step_name} 完成")
    
    print("\n" + "="*70)
    print("🎉 打包完成!")
    print("📁 可执行文件位置: out/main.dist/main.exe")
    print("🚀 启动脚本位置: out/main.dist/启动.bat")
    print("💡 建议使用启动脚本运行程序以获得最佳兼容性")
    print("=" * 70)

if __name__ == "__main__":
    main()
