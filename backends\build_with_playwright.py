#!/usr/bin/env python3
"""
Nuitka打包脚本 - 包含Playwright支持
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def find_playwright_browsers():
    """查找Playwright浏览器安装位置"""
    possible_locations = [
        # 用户目录
        Path.home() / "AppData" / "Local" / "ms-playwright",
        # 虚拟环境中的Playwright
        Path(sys.executable).parent.parent / "Lib" / "site-packages" / "playwright" / "driver",
        # 全局安装的Playwright
        Path(sys.prefix) / "Lib" / "site-packages" / "playwright" / "driver",
    ]

    for location in possible_locations:
        if location.exists():
            print(f"找到Playwright安装: {location}")
            # 查找具体的浏览器目录
            browsers_dir = location / "package" / ".local-browsers"
            if browsers_dir.exists():
                # 查找 chromium_headless_shell-1169 目录
                for item in browsers_dir.iterdir():
                    if item.is_dir() and "chromium_headless_shell" in item.name:
                        print(f"找到浏览器目录: {item}")
                        return item
                    elif item.is_dir() and "chromium" in item.name:
                        print(f"找到备用浏览器目录: {item}")
                        return item
            return location

    print("未找到Playwright安装")
    return None

def prepare_build_environment():
    """准备构建环境"""
    print("准备构建环境...")
    
    # 清理旧的构建文件
    build_dirs = ["build", "out", "dist"]
    for build_dir in build_dirs:
        if os.path.exists(build_dir):
            print(f"清理旧的构建目录: {build_dir}")
            shutil.rmtree(build_dir)
    
    # 确保必要的目录存在
    os.makedirs("out", exist_ok=True)

def build_with_nuitka():
    """使用Nuitka进行打包"""
    print("开始Nuitka打包...")
    
    # 基础Nuitka命令
    nuitka_cmd = [
        "nuitka",
        "--standalone",
        "--show-progress",
        "--follow-import-to=utils,src,myUtils",
        "--output-dir=out",
        "--windows-icon-from-ico=logo.ico",
        "--disable-dll-dependency-cache",
        "--assume-yes-for-downloads",
        "--mingw64",
        
        # Playwright相关配置
        "--include-package=playwright",
        "--include-package-data=playwright",
        "--include-data-dir=playwright=playwright",

        # 包含 KS-Downloader 目录及其所有内容
        "--include-data-dir=backends/KS-Downloader=KS-Downloader",

        # 其他必要的包
        "--include-package=sqlite3",
        "--include-package=asyncio",
        "--include-package=pathlib",
        "--include-package=logging",
        
        # 禁用一些可能有问题的插件
        "--plugin-disable=dll-files",
        "--plugin-disable=anti-bloat",
        
        # 主文件
        "main.py"
    ]
    
    # 查找Playwright浏览器
    playwright_location = find_playwright_browsers()
    if playwright_location:
        # 添加Playwright数据目录
        if "chromium_headless_shell" in str(playwright_location):
            # 如果找到的是具体的浏览器目录，包含到指定位置
            nuitka_cmd.extend([
                f"--include-data-dir={playwright_location}=playwright/driver/package/.local-browsers/chromium_headless_shell-1169"
            ])
            print(f"包含浏览器目录: {playwright_location} -> playwright/driver/package/.local-browsers/chromium_headless_shell-1169")
        else:
            # 包含整个driver目录
            nuitka_cmd.extend([
                f"--include-data-dir={playwright_location}=playwright/driver"
            ])
            print(f"包含Playwright目录: {playwright_location} -> playwright/driver")
    
    print("执行命令:", " ".join(nuitka_cmd))
    
    try:
        result = subprocess.run(nuitka_cmd, check=True, capture_output=True, text=True)
        print("Nuitka打包成功!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("Nuitka打包失败!")
        print("错误输出:", e.stderr)
        print("标准输出:", e.stdout)
        return False

def post_build_setup():
    """构建后的设置"""
    print("执行构建后设置...")
    
    dist_dir = Path("out/main.dist")
    if not dist_dir.exists():
        print("构建目录不存在!")
        return False
    
    # 复制额外的文件
    extra_files = [
        "logo.ico",
        "cookiesFile",
        "videoFile",
        "logFile"
    ]
    
    for file_path in extra_files:
        if os.path.exists(file_path):
            if os.path.isdir(file_path):
                dest = dist_dir / file_path
                if dest.exists():
                    shutil.rmtree(dest)
                shutil.copytree(file_path, dest)
                print(f"复制目录: {file_path} -> {dest}")
            else:
                shutil.copy2(file_path, dist_dir)
                print(f"复制文件: {file_path} -> {dist_dir}")
    
    # 创建启动脚本
    create_launch_script(dist_dir)
    
    return True

def create_launch_script(dist_dir):
    """创建启动脚本"""
    script_content = """@echo off
echo 启动小超媒体管理系统...
echo.

REM 检查是否有Chrome浏览器
if exist "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" (
    echo 找到Chrome浏览器
) else if exist "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe" (
    echo 找到Chrome浏览器
) else (
    echo 警告: 未找到Chrome浏览器，可能影响某些功能
    echo 建议安装Chrome浏览器以获得最佳体验
    echo.
)

REM 启动主程序
main.exe

REM 如果程序异常退出，暂停以查看错误信息
if errorlevel 1 (
    echo.
    echo 程序异常退出，错误代码: %errorlevel%
    pause
)
"""
    
    script_path = dist_dir / "启动.bat"
    with open(script_path, 'w', encoding='gbk') as f:
        f.write(script_content)
    
    print(f"创建启动脚本: {script_path}")

def main():
    """主函数"""
    print("=" * 60)
    print("小超媒体管理系统 - Nuitka打包工具")
    print("=" * 60)
    
    # 检查是否在正确的目录
    if not os.path.exists("main.py"):
        print("错误: 未找到main.py文件，请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 检查Nuitka是否安装
    try:
        subprocess.run([sys.executable, "-m", "nuitka", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        try:
            subprocess.run(["nuitka", "--version"], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("错误: 未找到Nuitka，请先安装: pip install nuitka")
        sys.exit(1)
    
    # 执行构建步骤
    steps = [
        ("准备构建环境", prepare_build_environment),
        ("Nuitka打包", build_with_nuitka),
        ("构建后设置", post_build_setup),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if not step_func():
            print(f"步骤失败: {step_name}")
            sys.exit(1)
    
    print("\n" + "="*60)
    print("打包完成!")
    print("可执行文件位置: out/main.dist/main.exe")
    print("启动脚本位置: out/main.dist/启动.bat")
    print("=" * 60)

if __name__ == "__main__":
    main()
