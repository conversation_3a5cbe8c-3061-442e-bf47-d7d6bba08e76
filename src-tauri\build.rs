use std::env;
use std::fs;
use std::path::Path;

fn main() {
    // 获取构建脚本的输出目录
    let out_dir = env::var("OUT_DIR").unwrap();
    let target_dir = Path::new(&out_dir).ancestors().nth(3).unwrap();

    // 源JAR文件路径 (相对于 src-tauri 目录)
    let src_jar = "src/test/ks_sig3.jar";
    let src_jar_path = Path::new(src_jar);

    // 目标JAR文件路径（在可执行文件同级目录）
    let dest_jar = target_dir.join("ks_sig3.jar");

    // 检查源文件是否存在
    if src_jar_path.exists() {
        // 复制JAR文件到输出目录
        if let Err(e) = fs::copy(src_jar, &dest_jar) {
            println!("cargo:warning=Failed to copy JAR file: {}", e);
        }
    } else {
        println!("cargo:warning=JAR file not found at: {}", src_jar);
    }

    // 告诉Cargo在JAR文件改变时重新运行构建脚本
    println!("cargo:rerun-if-changed=src/test/ks_sig3.jar");

    // 运行默认的tauri构建
    tauri_build::build()
}
