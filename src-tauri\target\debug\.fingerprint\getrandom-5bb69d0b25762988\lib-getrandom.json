{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 15657897354478470176, "path": 3966551323784293027, "deps": [[10411997081178400487, "cfg_if", false, 17273532339252434183]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-5bb69d0b25762988\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}