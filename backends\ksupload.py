#快手上传相关
def get_updata(kuaishou_web_cp_api_ph,cookie1):
    try:
        data = {
                'kuaishou.web.cp.api_ph': kuaishou_web_cp_api_ph,
                'uploadType': '1',
            }
        header3 = {
                'Connection': 'keep-alive',
                'Content-Length': str(len(data)),
                'Content-Type': 'application/json;charset=utf-8',
                'Pragma': 'no-cache',
                'Referer': 'https://cp.kuaishou.com/article/publish/video',
                'Host': 'cp.kuaishou.com',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Cookie': cookie1
            }
        data = json.dumps(data).encode()
        get__Ns_sig3 = getNs_sig3(data)
        params ={
                    'uploadType': '1',
                    '__NS_sig3':get__Ns_sig3
                }
        req = requests.post('https://cp.kuaishou.com/rest/cp/works/v2/video/pc/upload/pre', headers = header3,params=params,data=data)
        #print(req.text)
        res_dc = req.json()
        #print('*'*100)
        #print(res_dc)
        上传token = res_dc['data']['token']

        # # 上传/恢复
        # # 2.https://upload.kuaishouzt.com/api/upload/resume   #/api/上传/恢复
        #print(上传token)

        req = urllib.request.Request('https://upload.kuaishouzt.com/api/upload/resume?upload_token='+上传token)
        req.headers = header  # 模拟浏览器
        print('恢复上传功能成功' + time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time())))
        return 上传token
    except:
        print('恢复上传功能失败:' + time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time())))
        return 'error'    
def upfile(kuaishou_web_cp_api_ph,cookie1,上传token,userId,Copywriting,partner,username):
    #try:
        # 3.申请块上传文件
        with open(os.getcwd() + '\\mp4\\' + userId + '.mp4', 'rb') as f:
            视频数据二进制 = f.read()
        文件大小 = len(视频数据二进制)
        文件大小2 = 文件大小
        迭代次数 = 0
        最后一次文件大小 = 0
        while (迭代次数 < 100):
            判断是否为负 = 文件大小2-4194304
            if 判断是否为负 < 0:
                最后一次文件大小 = 文件大小2
                break
            迭代次数 = 迭代次数 + 1
            文件大小2 = 判断是否为负
        if 迭代次数 == 0:
            Content_Range = 'bytes 0-'+str(文件大小)+'/'+str(文件大小)
            data = {
                'kuaishou.web.cp.api_ph': kuaishou_web_cp_api_ph,
                'uploadType': '1'
            }
            header_视频上传 = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                'Connection': 'keep-alive',
                'Content-Length': str(len(视频数据二进制)),
                'Content-Range': Content_Range,
                'Content-Type': 'application/octet-stream',
                'Host': 'upload.kuaishouzt.com',
                'Origin': 'https://cp.kuaishou.com',
                'Referer': 'https://cp.kuaishou.com/article/publish/video?origin=www.kuaishou.com',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Cookie': cookie1
            }
            req = urllib.request.Request('https://upload.kuaishouzt.com/api/upload/fragment?upload_token='+上传token+'&fragment_id=0', data=data,headers=header_视频上传, method='POST')
            html = opener.open(req, 视频数据二进制).read()  # html会返回验证码成功或不成功
            #print(html.decode('utf8'))
        else:
            data = {
                'kuaishou.web.cp.api_ph': kuaishou_web_cp_api_ph,
                'uploadType': '1'
            }
            #print('当前文件迭代次数是:' +str(迭代次数))
            for i in range(0, 迭代次数+1):
                #print('i'+str(i))
                if i == 迭代次数:
                    #print('进入最后上传块')
                    Content_Range = 'bytes ' + str(迭代次数*4194304)+'-'+str(文件大小-1)+'/'+str(文件大小)
                    #print(Content_Range)
                    #print(最后一次文件大小)
                    header_视频上传 = {
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                        'Connection': 'keep-alive',
                        'Content-Length': str(最后一次文件大小),
                        'Content-Range': Content_Range,
                        'Content-Type': 'application/octet-stream',
                        'Host': 'upload.kuaishouzt.com',
                        'Origin': 'https://cp.kuaishou.com',
                        'Referer': 'https://cp.kuaishou.com/article/publish/video?origin=www.kuaishou.com',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Cookie': cookie1
                    }
                    #print('最后上传的迭代次数是:' + str(迭代次数))
                    req = urllib.request.Request('https://upload.kuaishouzt.com/api/upload/fragment?upload_token=' + 上传token + '&fragment_id=' + str(迭代次数) , data=data,headers=header_视频上传, method='POST')
                    #print('1111111')
                    文件切割 = 视频数据二进制[迭代次数*4194304:文件大小]
                    #print('文件大小'+str(文件大小-迭代次数*4194304))
                    html = opener.open(req, 文件切割).read()  # html会返回验证码成功或不成功
                    #print('这里以经运行过了')
                else:
                    #print(str(i))
                    Content_Range = 'bytes ' + str(i*4194304) + '-' + str((i+1)*4194304-1)+'/'+str(文件大小)
                    #print(Content_Range)
                    header_视频上传 = {
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                        'Connection': 'keep-alive',
                        'Content-Length': str(4194304),
                        'Content-Range': Content_Range,
                        'Content-Type': 'application/octet-stream',
                        'Host': 'upload.kuaishouzt.com',
                        'Origin': 'https://cp.kuaishou.com',
                        'Referer': 'https://cp.kuaishou.com/article/publish/video?origin=www.kuaishou.com',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        'Cookie': cookie1
                    }
                    #print(str(i))
                    req = urllib.request.Request('https://upload.kuaishouzt.com/api/upload/fragment?upload_token='+上传token+'&fragment_id='+str(i), data=data, method='POST')
                    req.headers = header_视频上传  # 模拟浏览器
                    文件切割 = 视频数据二进制[i*4194304:(i+1)*4194304]
                    html = opener.open(req, 文件切割).read()  # html会返回验证码成功或不成功
                    #print('11111111111'+html.decode('utf8'))

        #print(len(data))
        header_视频上传 = {
            'Host': 'upload.kuaishouzt.com',
            'Connection': 'keep-alive',
            'Content-Length': '0',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Origin': 'https://cp.kuaishou.com',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://cp.kuaishou.com/article/publish/video',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9'
        }
        FragmentCount = 迭代次数 + 1
        req = urllib.request.Request('https://upload.kuaishouzt.com/api/upload/complete?fragment_count=' + str(FragmentCount) + '&upload_token='+上传token, method='POST')
        req.headers = header_视频上传
        html = opener.open(req).read()  # html会返回验证码成功或不成功
        #print(html.decode('utf8'))
        #print('申请上传模块以完成:' + time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time())))


        # 3.文件上传完毕
        data = {
            'fileLength': str(文件大小),  # 文件大小
            'fileName': userId + '.mp4',  # 文件名称
            'fileType': 'video/mp4',
            'kuaishou.web.cp.api_ph': kuaishou_web_cp_api_ph,
            'token': 上传token
        }
        data = json.dumps(data).encode()
        header3 = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            #'Content-Length': int(len(data)),
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://cp.kuaishou.com',
            'Referer': 'https://cp.kuaishou.com/article/publish/video',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Cookie':cookie1
        }

        get__Ns_sig3 = getNs_sig3(data)
        params ={
                    '__NS_sig3':get__Ns_sig3,
                }
        req = requests.post('https://cp.kuaishou.com/rest/cp/works/v2/video/pc/upload/finish',headers = header3,params=params,data=data)
        print(html.decode('utf8'))
        print('>>'*40)
        print(req.text)
        print('>>'*40)
        print('视频以经上传到cp服务器:' + time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time())))
        data_json = json.loads(req.text)

        提取字符串 = data_json['data']['fileId']
        fileId = 提取字符串
        coverKey = data_json['data']['coverKey']
        mediaId = data_json['data']['mediaId']
        photoIdStr = data_json['data']['photoIdStr']
        videoDuration  = data_json['data']['videoDuration']
        print('取到fileId的值是:' + str(fileId) + '    ' + time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time())))
        data = {
            "activityIds":[],
            "allowSameFrame":"true",
            "caption":Copywriting,
            "chapters":[],
            "collectionId":"",
            "coverCropped":"false",
            "coverKey":coverKey,
            "coverTimeStamp":0,
            "coverTitle":"",
            "coverType":1,
            "declareInfo":{},
            "disableNearbyShow":"false",
            "domain":"",    #VarField
            "downloadType":1,
            "fileId":fileId,
            "kuaishou.web.cp.api_ph":kuaishou_web_cp_api_ph,
            "latitude":"",
            "longitude":"",
            "mediaId":mediaId,
            "movieId":"",
            "notifyResult":0,
            "openPrePreview":"false",
            "photoStatus":1,
            "photoType":0,
            "pkCoverKey":"",
            "poiId":0,
            "previewUrlErrorMessage":"",
            "profileCoverKey":"",
            "projectId":"",
            "publishTime":0,
            "recTagIdList":[],
            "riseQuality":"false",
            "secondDomain":"",    #VarLabel
            "triggerH265":"false",
            "videoDuration":videoDuration,
            "videoInfoMeta":""
            }
        data = json.dumps(data).encode()
        header_submit = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Content-Length': len(data),
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://cp.kuaishou.com',
            'Referer': 'https://cp.kuaishou.com/article/publish/video',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Cookie':cookie1
        }
        req = urllib.request.Request(
            'https://cp.kuaishou.com/rest/cp/works/v2/video/pc/submit', data=data,method='POST')
        req.headers = header_submit  # 模拟浏览器
        #print(req.headers)
        html = opener.open(req, data).read()  # html会返回验证码成功或不成功
        print(html.decode('utf8'))
        print('发布成功' + time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time())))
        #os.remove('001.mp4')
        #列表.remove(列表[0])
        return 'uploadOK'
    # except:
    #     print('upfile中出现未知错误')
    #     #sendMsg(str(partner + '  账号以经掉线...'))
    #     return 'uploadErr'
 