"""
Services 包初始化文件
包含业务逻辑服务模块
"""

# 详情查询服务
from .detail_enquire_service import process_detail_enquire_request

# AB处理服务
from .ab_processing_service import process_ab_video_fusion

# 日志服务
from .log_service import (
    create_log_service,
    process_test_logs_request,
    process_get_logs_request, 
    process_clear_logs_request,
    process_logs_stream_request
)

# 账号管理服务
from .account_service import (
    get_valid_accounts_handler,
    get_all_accounts_handler, 
    get_account_with_cookie_handler,
    get_kuaishou_accounts_with_cookie_handler,
    delete_account_handler,
    update_userinfo_handler,
    update_auto_publish_handler
)

# 文件管理服务
from .file_service import (
    upload_file_handler,
    upload_save_handler,
    get_all_files_handler,
    delete_file_handler,
    delete_files_handler,
    update_material_cover_image_handler
)

# 统计服务
from .stats_service import (
    get_stats_handler,
    get_file_stats_handler,
    get_account_stats_handler
)

# 系统工具服务
from .system_service import (
    get_current_directory_handler,
    open_file_in_explorer_handler,
    check_system_resources_handler,
    check_directories_handler,
    ensure_directories_handler
)

__all__ = [
    # 详情查询服务
    'process_detail_enquire_request',
    
    # AB处理服务
    'process_ab_video_fusion',
    
    # 日志服务
    'create_log_service',
    'process_test_logs_request',
    'process_get_logs_request',
    'process_clear_logs_request', 
    'process_logs_stream_request',
    
    # 账号管理服务
    'get_valid_accounts_handler',
    'get_all_accounts_handler', 
    'get_account_with_cookie_handler',
    'get_kuaishou_accounts_with_cookie_handler',
    'delete_account_handler',
    'update_userinfo_handler',
    'update_auto_publish_handler',
    
    # 文件管理服务
    'upload_file_handler',
    'upload_save_handler',
    'get_all_files_handler',
    'delete_file_handler',
    'delete_files_handler',
    'update_material_cover_image_handler',
    
    # 统计服务
    'get_stats_handler',
    'get_file_stats_handler',
    'get_account_stats_handler',
    
    # 系统工具服务
    'get_current_directory_handler',
    'open_file_in_explorer_handler',
    'check_system_resources_handler',
    'check_directories_handler',
    'ensure_directories_handler'
]
