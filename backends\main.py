import asyncio
import os
import sys
import logging
import requests
from collections import deque
import io
import threading
import time
import re
import sqlite3
import traceback
import uuid
import shutil
import subprocess
import urllib3
from pathlib import Path
print("Python 服务工作目录:", os.getcwd())


# 创建内存日志缓存
log_cache = deque(maxlen=1000)  # 最多保存1000条日志
log_lock = threading.Lock()

class MemoryLogHandler(logging.Handler):
    """自定义日志处理器，只保存业务日志到内存中"""

    def __init__(self):
        super().__init__()
        # ANSI 转义序列正则表达式
        self.ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')

    def clean_ansi_codes(self, text):
        """清理 ANSI 转义序列"""
        if not text:
            return text
        # 移除 ANSI 转义序列
        cleaned = self.ansi_escape.sub('', text)
        # 清理多余的空格和换行符
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned

    def emit(self, record):
        try:
            # 保留来自 loguru_bridge 的日志（即 kuaishou_logger 等业务日志）
            # 以及来自 root logger 的日志（即 detailEnquire 等接口的日志）
            if record.name not in ['loguru_bridge', 'root']:
                return

            # 清理消息中的 ANSI 转义序列
            clean_message = self.clean_ansi_codes(record.getMessage())

            log_entry = {
                'timestamp': self.format(record),
                'level': record.levelname,
                'message': clean_message,
                'time': record.created
            }
            with log_lock:
                log_cache.append(log_entry)
        except Exception:
            self.handleError(record)

# 日志基础配置
logging.basicConfig(
    level=logging.INFO,  # 去除 DEBUG 日志
    format="%(asctime)s %(levelname)s %(message)s",
    handlers=[
        logging.FileHandler("app.log", encoding="utf-8"),
        logging.StreamHandler(sys.stdout),
        MemoryLogHandler()  # 添加内存日志处理器
    ]
)

# 禁用特定库的 DEBUG 日志
logging.getLogger('aiosqlite').setLevel(logging.WARNING)
logging.getLogger('asyncio').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)

# 处理 PyInstaller 打包后的路径
if getattr(sys, 'frozen', False):
    # PyInstaller 运行时解包目录
    application_path = sys._MEIPASS
    # 获取可执行文件所在目录
    executable_path = os.path.dirname(sys.executable)
    # 添加应用路径到系统路径
    sys.path.insert(0, application_path)
    # 添加可执行文件所在目录到系统路径
    sys.path.insert(0, executable_path)
else:
    # 开发环境下的路径
    application_path = os.path.dirname(os.path.abspath(__file__))
    executable_path = application_path
    # 添加应用路径到系统路径
    sys.path.insert(0, application_path)

print("sys.path:", sys.path)
print("main.py __file__:", __file__)
print("main.py 当前工作目录:", os.getcwd())
print("application_path:", application_path)
print("executable_path:", executable_path)

import sqlite3
import threading
import time
import uuid
import traceback
from pathlib import Path
from queue import Queue
from flask_cors import CORS
import subprocess
import uuid
import re
import shutil
import io

sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 修改 BASE_DIR 的定义
if getattr(sys, 'frozen', False):
    # 在打包环境下，BASE_DIR 应该是可执行文件所在目录
    BASE_DIR = Path(executable_path)
else:
    # 在开发环境下，保持原有的 BASE_DIR
    from conf import BASE_DIR

# 导入统一的 ffmpeg 配置
from ffmpeg_config import FFMPEG_BIN, FFPROBE_BIN, FFMPEG_DIR
def fetch_url(url):
    try:
        logging.info(f"请求: {url}")
        response = requests.get(url)
        logging.info(f"响应状态: {response.status_code}")
        response.raise_for_status()
        return response.text
    except Exception as e:
        logging.error(f"请求异常: {e}", exc_info=True)
        return None
# 确保必要的目录存在
def ensure_directories():
    # 确保数据库目录存在
    db_dir = BASE_DIR / "db"
    db_dir.mkdir(exist_ok=True)
    
    # 确保其他必要的目录存在
    from conf import VIDEO_DIR, COOKIES_DIR
    VIDEO_DIR.mkdir(exist_ok=True)
    COOKIES_DIR.mkdir(exist_ok=True)
    (BASE_DIR / "cookies").mkdir(exist_ok=True)

# 检查是否是首次运行
def is_first_run():
    db_path = Path(BASE_DIR / "database.db")
    return not db_path.exists()

# 执行首次运行初始化
def initialize_first_run():
    try:
        print("执行数据库初始化...")
        ensure_directories()  # 确保目录存在
        import createTable
        # 如果 createTable 有 main/init 函数，调用它
        if hasattr(createTable, "main"):
            createTable.main()
        print("数据库初始化完成")
    except Exception as e:
        print(f"初始化失败: {e}")
        raise

# 检查并执行首次运行初始化
if is_first_run():
    print("首次运行，执行初始化...")
    initialize_first_run()

try:
    # 确保 myUtils 目录在 Python 路径中
    myutils_path = os.path.join(application_path, 'myUtils')
    if myutils_path not in sys.path:
        sys.path.insert(0, myutils_path)
    
    from myUtils.auth import check_cookie
    print("myUtils.auth.check_cookie 导入成功")
except Exception as e:
    print("导入 myUtils.auth.check_cookie 失败:", e)
    raise

from flask import Flask, request, jsonify, Response, render_template, send_from_directory
from myUtils.login import get_tencent_cookie, douyin_cookie_gen, get_ks_cookie, xiaohongshu_cookie_gen, get_ks_shop_cookie
from myUtils.postVideo import post_video_tencent, post_video_DouYin, post_video_ks, post_video_xhs

active_queues = {}
app = Flask(__name__)

@app.errorhandler(Exception)
def handle_exception(e):
    logging.error(f"全局异常: {e}", exc_info=True)
    return "服务器内部错误", 500


#允许所有来源跨域访问
CORS(app)

# 限制上传文件大小为160MB
app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 512MB

# 获取当前目录（假设 index.html 和 assets 在这里）
current_dir = os.path.dirname(os.path.abspath(__file__))

# 处理所有静态资源请求（未来打包用）
@app.route('/assets/<filename>')
def custom_static(filename):
    return send_from_directory(os.path.join(current_dir, 'assets'), filename)

# 处理 favicon.ico 静态资源（未来打包用）
@app.route('/favicon.ico')
def favicon(filename):
    return send_from_directory(os.path.join(current_dir, 'assets'), 'favicon.ico')

# （未来打包用）
@app.route('/')
def hello_world():  # put application's code here
    # return render_template('index.html')
    return jsonify({
            "code": 200,
            "data": None,
            "msg": "应用激活成功"
        }), 400
@app.route('/upload', methods=['POST'])
def upload_file():
    """单文件上传接口"""
    if 'file' not in request.files:
        return jsonify({
            "code": 400,
            "data": None,
            "msg": "No file part in the request"
        }), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({
            "code": 400,
            "data": None,
            "msg": "No selected file"
        }), 400
    
    from services import upload_file_handler
    result = upload_file_handler(BASE_DIR, file)
    
    if result['success']:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']
    else:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']

@app.route('/getFile', methods=['GET'])
def get_file():
    # 获取 filename 参数
    filename = request.args.get('filename')

    if not filename:
        return {"error": "filename is required"}, 400

    # 防止路径穿越攻击
    if '..' in filename or filename.startswith('/'):
        return {"error": "Invalid filename"}, 400

    # 拼接完整路径
    file_path = str(Path(BASE_DIR / "videoFile"))

    # 返回文件
    return send_from_directory(file_path,filename)


import subprocess
from pathlib import Path
import uuid
import os

@app.route('/uploadSave', methods=['POST'])
def upload_save():
    """多文件上传并保存到数据库接口"""
    files = request.files.getlist('file')
    custom_filename = request.form.get('filename', None)
    
    from services import upload_save_handler
    result = upload_save_handler(BASE_DIR, files, custom_filename)
    
    if result['success']:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']
    else:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']
    
@app.route('/updateMaterialCoverImage', methods=['POST'])
def update_material_cover_image():
    """更新素材的封面图，删除原封面，将新封面拷贝到视频目录下并更新数据库"""
    from services import update_material_cover_image_handler
    
    data = request.get_json()
    file_url = data.get('fileUrl')
    material_id = data.get('materialId')
    
    result = update_material_cover_image_handler(BASE_DIR, file_url, material_id)
    
    return jsonify({
        "code": result['code'],
        "msg": result['msg'],
        "data": result['data']
    }), result['code']
        
@app.route('/getFiles', methods=['GET'])
def get_all_files():
    """获取所有文件记录"""
    from services import get_all_files_handler

    # 添加调试信息
    print(f"getFiles - BASE_DIR: {BASE_DIR}")
    print(f"getFiles - 数据库路径: {BASE_DIR / 'database.db'}")
    print(f"getFiles - 数据库存在: {(BASE_DIR / 'database.db').exists()}")

    result = get_all_files_handler(BASE_DIR)

    return jsonify({
        "code": result['code'],
        "msg": result['msg'],
        "data": result['data']
    }), result['code']


@app.route("/getValidAccounts", methods=['GET'])
async def getValidAccounts():
    """获取有效账号列表，可选择是否校验Cookie"""
    from services import get_valid_accounts_handler
    
    # 获取 check_cookie 参数，默认为 0（不校验）
    check_cookie_param = request.args.get('check_cookie', default='0')
    
    result = get_valid_accounts_handler(BASE_DIR, check_cookie_param)
    
    if result['success']:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']
    else:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']
        
@app.route("/getAllAccounts", methods=['GET'])
def get_all_accounts():
    """获取所有账号信息，不校验 cookie 是否有效"""
    from services import get_all_accounts_handler
    
    result = get_all_accounts_handler(BASE_DIR)
    
    if result['success']:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']
    else:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']

@app.route("/getAccountsWithCookie", methods=['GET'])
def get_accounts_with_cookie():
    """获取一条账号信息，不校验 cookie 是否有效，但返回 cookie 文件内容"""
    from services import get_account_with_cookie_handler
    
    result = get_account_with_cookie_handler(BASE_DIR)
    
    if result['success']:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']
    else:
        return jsonify({
            "code": result['code'],
            "msg": result['msg'],
            "data": result['data']
        }), result['code']
def process_downloaded_video_with_upload_logic(video_file_path, video_data):
    """
    使用 uploadSave 逻辑处理下载的视频文件
    """
    try:
        import uuid
        import subprocess
        import shutil

        # 获取视频信息
        author_name = video_data.get('name', 'unknown')
        detail_id = video_data.get('detailID', 'unknown')
        caption = video_data.get('caption', '')

        # 清理文件名中的非法字符
        import re
        safe_author_name = re.sub(r'[<>:"/\\|?*]', '_', author_name)
        safe_caption = re.sub(r'[<>:"/\\|?*]', '_', caption)

        # 生成原始文件名
        original_filename = f"{detail_id}_{safe_author_name}_{safe_caption[:30]}.mp4"

        # 生成UUID文件名（复用uploadSave逻辑）
        uuid_v1 = uuid.uuid1()
        final_filename = f"{uuid_v1}_{original_filename}"
        final_filepath = Path(BASE_DIR / "videoFile" / final_filename)

        # 如果文件不存在，尝试移动或复制
        if not video_file_path.exists():
            print(f"警告: 源视频文件不存在: {video_file_path}")
            return None

        # 移动文件到最终位置
        if video_file_path != final_filepath:
            import shutil
            shutil.move(str(video_file_path), str(final_filepath))
            print(f"文件已移动: {video_file_path} -> {final_filepath}")

        # 生成封面图（复用uploadSave逻辑）
        cover_name = f"{uuid_v1}_cover.jpg"
        cover_path = Path(BASE_DIR / "videoFile" / cover_name)
        ffmpeg_bin = FFMPEG_BIN

        print(f"生成封面图: {cover_path}")
        try:
            subprocess.run([
                ffmpeg_bin,
                "-y",
                "-i", str(final_filepath),
                "-vf", "select=eq(n\\,0)",
                "-q:v", "2",
                str(cover_path)
            ], check=True, encoding='utf-8', errors='ignore')
            print(f"✅ 封面图生成成功: {cover_path}")
        except subprocess.CalledProcessError as e:
            print(f"❌ 封面图生成失败: {e}")
            cover_path = None

        # 保存到数据库（复用uploadSave逻辑）
        with sqlite3.connect(Path(BASE_DIR / "database.db")) as conn:
            cursor = conn.cursor()

            # 检查是否已存在相同的记录
            cursor.execute("""
                SELECT id FROM file_records
                WHERE filename = ? OR file_path = ?
            """, (original_filename, final_filename))

            existing = cursor.fetchone()
            if existing:
                print(f"视频记录已存在，跳过保存: {final_filename}")
                return {
                    "filename": original_filename,
                    "filepath": final_filename,
                    "cover_image": str(cover_path) if cover_path else None
                }

            # 计算文件大小（MB）
            file_size_mb = round(float(os.path.getsize(final_filepath)) / (1024 * 1024), 2)

            # 插入新记录（复用uploadSave逻辑）
            cursor.execute('''
                INSERT INTO file_records (filename, filesize, file_path, cover_image)
                VALUES (?, ?, ?, ?)
            ''', (
                original_filename,
                file_size_mb,
                final_filename,
                str(cover_path) if cover_path else None
            ))

            conn.commit()
            print(f"✅ 视频记录保存成功:")
            print(f"   - 原始文件名: {original_filename}")
            print(f"   - 最终文件名: {final_filename}")
            print(f"   - 文件大小: {file_size_mb} MB")
            print(f"   - 封面图: {cover_path}")

            return {
                "filename": original_filename,
                "filepath": final_filename,
                "cover_image": str(cover_path) if cover_path else None,
                "filesize": file_size_mb
            }

    except Exception as e:
        print(f"❌ 处理下载视频失败: {e}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
        return None

def generate_images_from_api(api_key, prompt, aspect_ratio="16:9", image_count=3):
    """
    调用外部API生成图片
    """
    import requests
    import json
    import urllib3
    
    # 禁用SSL警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # logging.info(f"开始调用图片生成API - prompt: {prompt[:50]}...")

    try:
        url = "https://api.minimaxi.com/v1/image_generation"

        payload = json.dumps({
            "model": "image-01",
            "prompt": prompt,
            "aspect_ratio": aspect_ratio,
            "response_format": "url",
            "n": image_count,
            "prompt_optimizer": True
        })

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        logging.info(f"等待 minimaxi 生成副视频: {url}")
        # 添加verify=False来禁用SSL证书验证
        response = requests.post(url, headers=headers, data=payload, timeout=60, verify=False)

        if response.status_code == 200:
            result = response.json()
            logging.info(f"API调用成功，状态: {result.get('base_resp', {}).get('status_msg', 'unknown')}")

            if result.get('base_resp', {}).get('status_code') == 0:
                image_urls = result.get('data', {}).get('image_urls', [])
                logging.info(f"成功获取 {len(image_urls)} 张图片URL")
                return image_urls
            else:
                logging.error(f"API返回错误: {result.get('base_resp', {}).get('status_msg', 'unknown')}")
                return None
        else:
            logging.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
            return None

    except Exception as e:
        logging.error(f"图片生成API调用失败: {e}")
        return None

def download_images(image_urls, download_dir):
    """
    下载图片到本地
    """
    import requests
    import urllib3
    from urllib.parse import urlparse
    
    # 禁用SSL警告
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    downloaded_files = []

    for i, url in enumerate(image_urls):
        try:
            logging.info(f"下载图片 {i+1}/{len(image_urls)}: {url}")

            # 添加verify=False来禁用SSL证书验证
            response = requests.get(url, timeout=30, verify=False)
            if response.status_code == 200:
                # 生成文件名
                filename = f"generated_image_{i+1}.jpg"
                filepath = Path(download_dir) / filename

                with open(filepath, 'wb') as f:
                    f.write(response.content)

                downloaded_files.append(str(filepath))
                logging.info(f"图片下载成功: {filepath}")
            else:
                logging.error(f"图片下载失败，状态码: {response.status_code}")

        except Exception as e:
            logging.error(f"下载图片失败: {e}")

    logging.info(f"图片下载完成，成功下载 {len(downloaded_files)} 张图片")
    return downloaded_files

def create_video_from_images(image_files, output_path, duration_per_image=1, fps=30):
    """
    使用ffmpeg将图片生成视频，每张图片持续指定秒数
    直接使用原始图片文件，不创建临时目录
    """
    import subprocess

    if not image_files:
        logging.error("没有图片文件可用于生成视频")
        return None

    try:
        ffmpeg_bin = FFMPEG_BIN

        logging.info(f"准备生成视频，图片数量: {len(image_files)}")

        # 计算总时长和帧数
        total_duration = len(image_files) * duration_per_image  # 总时长（秒）
        total_frames = total_duration * fps  # 总帧数

        logging.info(f"开始生成视频: {output_path}")
        logging.info(f"参数: {len(image_files)}张图片, 每张图片{duration_per_image}秒, {fps}fps")
        logging.info(f"总时长: {total_duration}秒, 总帧数: {total_frames}")

        # 创建输入文件列表
        input_list = []
        for img_file in image_files:
            # 每张图片重复指定的时长
            input_list.extend(["-loop", "1", "-t", str(duration_per_image), "-i", str(img_file)])

        # 创建filter_complex来连接所有图片
        if len(image_files) == 1:
            filter_complex = f"[0:v]fps={fps}[v]"
        else:
            # 多张图片需要连接
            inputs = "".join([f"[{i}:v]fps={fps}[v{i}];" for i in range(len(image_files))])
            concat = "".join([f"[v{i}]" for i in range(len(image_files))])
            filter_complex = f"{inputs}{concat}concat=n={len(image_files)}:v=1:a=0[v]"

        # 使用ffmpeg生成视频
        cmd = [
            ffmpeg_bin,
            "-y",  # 覆盖输出文件
        ] + input_list + [
            "-filter_complex", filter_complex,
            "-map", "[v]",
            "-pix_fmt", "yuv420p",  # 像素格式
            "-c:v", "libx264",  # 强制使用CPU编码器
            "-crf", "28",  # 质量参数
            "-preset", "medium",  # 编码预设
            str(output_path)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')

        if result.returncode == 0:
            logging.info(f"视频生成成功: {output_path}")
            return str(output_path)
        else:
            logging.error(f"视频生成失败: {result.stderr}")
            return None

    except Exception as e:
        logging.error(f"生成视频时发生错误: {e}")
        return None

def clean_ab_files_after_publish(video_data):
    """
    发布成功后清理AB文件和数据库记录，避免重复发布
    
    Args:
        video_data: 包含AB版本信息的字典
    """
    try:
        if not video_data.get('ab_processed') or not video_data.get('ab_versions'):
            logging.info("没有AB处理文件需要清理")
            return
            
        ab_versions = video_data.get('ab_versions', [])
        logging.info(f"开始清理 {len(ab_versions)} 个AB版本文件")
        
        deleted_files = []
        deleted_records = []
        
        import sqlite3
        
        with sqlite3.connect(Path(BASE_DIR / "database.db")) as conn:
            cursor = conn.cursor()
            
            for version in ab_versions:
                try:
                    filename = version.get('filename')
                    cover_image = version.get('cover_image')
                    record_id = version.get('record_id')
                    
                    if not filename:
                        continue
                    
                    # 删除视频文件
                    video_file_path = Path(BASE_DIR / "videoFile" / filename)
                    if video_file_path.exists():
                        video_file_path.unlink()
                        deleted_files.append(str(video_file_path))
                        logging.info(f"已删除AB视频文件: {video_file_path}")
                    
                    # 删除封面图文件
                    if cover_image:
                        cover_file_path = Path(cover_image)
                        if cover_file_path.exists():
                            cover_file_path.unlink()
                            deleted_files.append(str(cover_file_path))
                            logging.info(f"已删除AB封面文件: {cover_file_path}")
                    
                    # 删除数据库记录
                    if record_id:
                        cursor.execute("DELETE FROM file_records WHERE id = ?", (record_id,))
                        if cursor.rowcount > 0:
                            deleted_records.append(record_id)
                            logging.info(f"已删除AB数据库记录: {record_id}")
                    
                except Exception as e:
                    logging.error(f"清理AB版本 {version.get('version', 'unknown')} 时出错: {e}")
                    continue
            
            # 提交数据库更改
            conn.commit()
        
        # 发布成功后也清理原始主视频文件
        temp_main_video_path = video_data.get('temp_main_video_path')
        if temp_main_video_path:
            try:
                temp_file = Path(temp_main_video_path)
                if temp_file.exists():
                    temp_file.unlink()
                    deleted_files.append(str(temp_file))
                    logging.info(f"已清理原始主视频文件: {temp_main_video_path}")
            except Exception as e:
                logging.warning(f"清理原始主视频文件失败: {e}")
        
        logging.info(f"AB文件清理完成: 删除了 {len(deleted_files)} 个文件, {len(deleted_records)} 条数据库记录")
        
        return {
            'deleted_files': deleted_files,
            'deleted_records': deleted_records,
            'success': True
        }
        
    except Exception as e:
        logging.error(f"清理AB文件时发生异常: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'success': False,
            'error': str(e)
        }

def auto_publish_to_kuaishou(video_data, auto_publish_config):
    """
    AB处理完成后自动发布到快手
    
    Args:
        video_data: 包含视频信息的字典
        auto_publish_config: 自动发布配置（当前用作标识，未来可扩展具体配置）
    
    Returns:
        dict: 发布结果
    """
    try:
        logging.info("开始自动发布到快手")
        
        # 1. 获取快手账号（type=4代表快手）
        from services import get_all_accounts_handler
        accounts_result = get_all_accounts_handler(BASE_DIR)
        
        if not accounts_result.get('success'):
            logging.error("获取账号列表失败")
            return {
                'success': False,
                'msg': '获取账号列表失败',
                'data': None
            }
        
        # 筛选快手账号（type=4）并且启用了自动发布（auto_publish=1）
        kuaishou_accounts = [
            account for account in accounts_result.get('data', [])
            if account.get('type') == 4 and account.get('auto_publish') == 1
        ]
        
        if not kuaishou_accounts:
            logging.error("未找到启用自动发布的快手账号，请先添加快手账号并启用自动发布")
            return {
                'success': False,
                'msg': '未找到启用自动发布的快手账号，请先添加快手账号并启用自动发布',
                'data': None
            }
        
        # 使用所有启用自动发布的快手账号进行发布
        logging.info(f"找到 {len(kuaishou_accounts)} 个启用自动发布的快手账号")
        for account in kuaishou_accounts:
            logging.info(f"- 账号: {account.get('userName', 'Unknown')} (ID: {account.get('id')}, 自动发布状态: {account.get('auto_publish')})")
        
        # 2. 准备发布参数 - 使用多版本AB处理后的文件信息
        if video_data.get('ab_processed') and video_data.get('ab_versions'):
            # 使用多个AB处理后的文件信息
            ab_versions = video_data.get('ab_versions', [])
            logging.info(f"检测到 {len(ab_versions)} 个AB去重版本，将分配给 {len(kuaishou_accounts)} 个账号")
            
            # 为每个账号分配不同的AB版本
            account_file_mappings = []
            for i, account in enumerate(kuaishou_accounts):
                # 循环分配AB版本，如果账号数多于AB版本数，则重复使用
                version_index = i % len(ab_versions)
                assigned_version = ab_versions[version_index]
                
                account_file_mappings.append({
                    'account': account,
                    'version': assigned_version,
                    'files': [assigned_version.get('filename')],
                    'cover_list': [assigned_version.get('cover_image', '')]
                })
                
                logging.info(f"账号 '{account.get('userName', 'Unknown')}' 分配AB版本 {assigned_version.get('version')} (文件: {assigned_version.get('filename')})")
        else:
            # 没有AB处理或处理失败，使用原始下载的文件
            files = [video_data.get('filepath')]
            cover_list = [video_data.get('cover_image', '')]
            
            # 所有账号使用同一个原始文件
            account_file_mappings = []
            for account in kuaishou_accounts:
                account_file_mappings.append({
                    'account': account,
                    'version': {'version': 0, 'filename': files[0]},
                    'files': files,
                    'cover_list': cover_list
                })
            
            logging.info(f"AB处理未启用或失败，使用原始下载的视频文件进行发布: {files[0]}")
        
        # 使用原始视频的标题，如果为空则使用默认标题
        title = video_data.get('caption', '').strip()
        if not title:
            title = f"自动发布-{video_data.get('name', 'Unknown')}"
        
        # 从caption中提取标签
        tags = []
        caption = video_data.get('caption', '')
        if caption:
            import re
            hashtags = re.findall(r'#([^#\s]+)', caption)
            tags = hashtags[:5] if hashtags else []  # 最多5个标签
        
        # 3. 逐个账号进行发布，每个账号使用分配的AB版本
        from myUtils.postVideo import post_video_ks
        
        success_accounts = []
        failed_accounts = []
        
        logging.info(f"开始逐个向 {len(kuaishou_accounts)} 个账号发布视频（使用不同AB版本）")
        
        for i, mapping in enumerate(account_file_mappings):
            try:
                account = mapping['account']
                account_name = account.get('userName', 'Unknown')
                account_file_path = account.get('filePath')
                version_info = mapping['version']
                files = mapping['files']
                cover_list = mapping['cover_list']
                
                logging.info(f"[{i+1}/{len(kuaishou_accounts)}] 正在向账号 '{account_name}' 发布 AB版本{version_info.get('version')}...")
                
                # 为单个账号调用发布功能
                post_video_ks(
                    title=title,
                    files=files,
                    tags=tags,
                    account_file=[account_file_path],  # 单个账号的文件路径
                    category=None,
                    enableTimer=False,
                    videos_per_day=1,
                    daily_times=['09:00'],
                    start_days=0,
                    goods_id=None,
                    cover_list=cover_list
                )
                
                success_accounts.append({
                    'account': account_name,
                    'version': version_info.get('version'),
                    'file': files[0] if files else 'N/A'
                })
                logging.info(f"✅ 账号 '{account_name}' AB版本{version_info.get('version')} 发布任务提交成功")
                
                # 添加短暂延迟，避免并发压力过大
                import time
                time.sleep(1)  # 延迟1秒
                
            except Exception as e:
                failed_accounts.append({
                    'account': account_name,
                    'error': str(e)
                })
                logging.error(f"❌ 账号 '{account_name}' 发布失败: {e}")
                continue
        
        # 统计发布结果
        total_accounts = len(kuaishou_accounts)
        success_count = len(success_accounts)
        failed_count = len(failed_accounts)
        
        logging.info(f"发布完成: 成功 {success_count}/{total_accounts} 个账号")
        
        if failed_accounts:
            for failed in failed_accounts:
                logging.warning(f"失败账号: {failed['account']} - {failed['error']}")
        
        # 构建返回消息
        if success_count == total_accounts:
            msg = f'所有 {total_accounts} 个账号发布任务均已提交成功'
        elif success_count > 0:
            msg = f'{success_count}/{total_accounts} 个账号发布成功，{failed_count} 个失败'
        else:
            msg = f'所有账号发布失败'
        
        # 如果发布成功，清理AB文件避免重复使用
        cleanup_result = None
        if success_count > 0 and video_data.get('ab_processed'):
            logging.info("=== 开始清理AB文件，避免重复发布 ===")
            cleanup_result = clean_ab_files_after_publish(video_data)
            if cleanup_result and cleanup_result.get('success'):
                logging.info(f"AB文件清理成功: 删除了 {len(cleanup_result.get('deleted_files', []))} 个文件")
            else:
                logging.warning(f"AB文件清理失败: {cleanup_result.get('error', 'Unknown error') if cleanup_result else 'No result'}")
        
        return {
            'success': success_count > 0,
            'msg': msg,
            'data': {
                'total_accounts': total_accounts,
                'success_accounts': success_accounts,
                'success_count': success_count,
                'failed_accounts': failed_accounts,
                'failed_count': failed_count,
                'title': title,
                'tags': tags,
                'ab_versions_used': video_data.get('ab_versions', []),
                'cleanup_result': cleanup_result  # 添加清理结果
            }
        }
        
    except Exception as e:
        logging.error(f"自动发布到快手失败: {e}")
        import traceback
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'success': False,
            'msg': f'自动发布失败: {str(e)}',
            'data': None
        }

@app.route("/processVideoLinks", methods=['POST'])
def process_video_links():
    """
    接口1：处理视频链接接口
    支持多链接下载，根据已启用账号数生成对应的AB融合视频
    返回AB视频和爬取的视频全部信息
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                "code": 400,
                "msg": "请求参数不能为空",
                "data": None
            }), 400

        urls = data.get('urls', [])  # 支持多个链接
        if isinstance(urls, str):
            urls = [urls]  # 兼容单个链接的情况

        cookie = data.get('cookie', '').strip()
        download = data.get('download', True)  # 默认下载视频
        proxy = data.get('proxy', '')
        video_generation_key = data.get('videoGenerationKey', '').strip()  # 视频生成密钥
        auto_ab = data.get('autoAB', True)  # 默认启用AB去重
        cover_title = data.get('coverTitle', '').strip()  # 封面标题
        custom_cover_path = data.get('customCoverPath', '').strip()  # 自定义封面图片路径
        cover_font_size = data.get('coverFontSize', 72)  # 封面标题字体大小，默认72px
        cover_font_color = data.get('coverFontColor', '#FFFF00')  # 封面标题字体颜色，默认黄色
        cover_title_lines = data.get('coverTitleLines', None)  # 多行颜色配置

        # 调试日志：检查前端传递的参数
        logging.info(f"🔍 前端传递的颜色参数: {cover_font_color} (类型: {type(cover_font_color)})")
        logging.info(f"🔍 前端传递的字体大小: {cover_font_size} (类型: {type(cover_font_size)})")
        logging.info(f"🔍 前端传递的标题: {repr(cover_title)}")
        logging.info(f"🔍 前端传递的多行配置: {cover_title_lines}")

        if not urls:
            return jsonify({
                "code": 400,
                "msg": "视频链接不能为空",
                "data": None
            }), 400

        # 获取启用自动发布的快手账号数量，确定需要生成的AB视频数量
        from services import get_all_accounts_handler
        accounts_result = get_all_accounts_handler(BASE_DIR)
        
        kuaishou_accounts = []
        if accounts_result.get('success'):
            # 筛选快手账号（type=4）并且启用了自动发布（auto_publish=1）
            kuaishou_accounts = [
                account for account in accounts_result.get('data', [])
                if account.get('type') == 4 and account.get('auto_publish') == 1
            ]

        account_count = len(kuaishou_accounts) if kuaishou_accounts else 1
        logging.info(f"检测到 {account_count} 个启用自动发布的快手账号，将为每个链接生成 {account_count} 个AB版本")

        results = []
        
        # 处理每个链接
        for idx, url in enumerate(urls):
            try:
                logging.info(f"=== 处理第 {idx + 1}/{len(urls)} 个链接 ===")
                logging.info(f"链接: {url}")
                
                # 导入并调用服务模块进行下载和AB处理
                from services import process_detail_enquire_request
                
                # 执行异步任务，强制启用AB处理
                if hasattr(asyncio, 'run'):
                    result = asyncio.run(process_detail_enquire_request(
                        url=url,
                        cookie=cookie,
                        download=download,
                        proxy=proxy,
                        video_generation_key=video_generation_key,
                        auto_ab=auto_ab,
                        auto_publish=False,  # 这个接口不自动发布
                        cover_title=cover_title,  # 封面标题
                        custom_cover_path=custom_cover_path,  # 自定义封面图片路径
                        cover_font_size=cover_font_size,  # 封面标题字体大小
                        cover_font_color=cover_font_color,  # 封面标题字体颜色
                        cover_title_lines=cover_title_lines  # 多行颜色配置
                    ))
                else:
                    # Python 3.6 兼容
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        result = loop.run_until_complete(process_detail_enquire_request(
                            url=url,
                            cookie=cookie,
                            download=download,
                            proxy=proxy,
                            video_generation_key=video_generation_key,
                            auto_ab=auto_ab,
                            auto_publish=False,
                            cover_title=cover_title,  # 封面标题
                            custom_cover_path=custom_cover_path,  # 自定义封面图片路径
                            cover_font_size=cover_font_size,  # 封面标题字体大小
                            cover_font_color=cover_font_color,  # 封面标题字体颜色
                            cover_title_lines=cover_title_lines  # 多行颜色配置
                        ))
                    finally:
                        loop.close()

                if result.get('success'):
                    response_data = result.get('data', {})
                    
                    # 添加链接索引和处理状态
                    response_data['link_index'] = idx
                    response_data['link_url'] = url
                    response_data['account_count'] = account_count
                    
                    # 为发布页面准备参数
                    if response_data.get('filename') and response_data.get('filepath'):
                        # 如果有AB版本，使用AB版本信息
                        if response_data.get('ab_processed') and response_data.get('ab_versions'):
                            ab_versions = response_data.get('ab_versions', [])
                            file_list = [version.get('filename') for version in ab_versions]
                            cover_list = [version.get('cover_image', '') for version in ab_versions]
                        else:
                            # 没有AB处理，使用原始文件
                            file_list = [response_data.get('filepath')]
                            cover_list = [response_data.get('cover_image', '')]
                        
                        response_data['publishParams'] = {
                            'fileList': file_list,  # 发布页面需要的文件路径数组
                            'coverList': cover_list,  # 封面图路径数组
                            'materialId': response_data.get('record_id'),  # 素材ID
                            'suggestedTitle': response_data.get('caption', '')[:100] if response_data.get('caption') else '',
                            'suggestedTags': [],
                            'videoInfo': {
                                'duration': response_data.get('duration', ''),
                                'filesize': response_data.get('filesize', 0),
                                'author': response_data.get('name', ''),
                                'viewCount': response_data.get('viewCount', 0)
                            }
                        }
                        
                        # 从caption中提取可能的标签
                        caption = response_data.get('caption', '')
                        if caption:
                            import re
                            hashtags = re.findall(r'#([^#\s]+)', caption)
                            if hashtags:
                                response_data['publishParams']['suggestedTags'] = hashtags[:5]
                    
                    results.append({
                        'success': True,
                        'data': response_data
                    })
                    logging.info(f"✅ 第 {idx + 1} 个链接处理成功")
                else:
                    results.append({
                        'success': False,
                        'error': result.get('msg', '处理失败'),
                        'link_index': idx,
                        'link_url': url
                    })
                    logging.error(f"❌ 第 {idx + 1} 个链接处理失败: {result.get('msg')}")
                    
            except Exception as e:
                results.append({
                    'success': False,
                    'error': str(e),
                    'link_index': idx,
                    'link_url': url
                })
                logging.error(f"❌ 第 {idx + 1} 个链接处理异常: {e}")

        # 统计处理结果
        success_count = len([r for r in results if r.get('success')])
        total_count = len(results)
        
        logging.info(f"=== 处理完成: {success_count}/{total_count} 个链接成功 ===")
        
        return jsonify({
            "code": 200 if success_count > 0 else 500,
            "msg": f"成功处理 {success_count}/{total_count} 个视频链接",
            "data": {
                "total_links": total_count,
                "success_count": success_count,
                "failed_count": total_count - success_count,
                "account_count": account_count,
                "results": results
            }
        })

    except Exception as e:
        logging.error(f"processVideoLinks 接口出错: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        return jsonify({
            "code": 500,
            "msg": f"处理视频链接出错: {str(e)}",
            "data": None
        }), 500


@app.route("/publishVideos", methods=['POST'])
def publish_videos():
    """
    接口2：发布视频接口
    根据已启用一键发布的账号数，对应发布视频，然后删除已发布的视频
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                "code": 400,
                "msg": "请求参数不能为空",
                "data": None
            }), 400

        # 接收来自 processVideoLinks 接口的结果数据
        video_results = data.get('videoResults', [])  # 上个接口返回的处理结果
        custom_title = data.get('title', '').strip()  # 自定义标题
        custom_tags = data.get('tags', [])  # 自定义标签
        
        if not video_results:
            return jsonify({
                "code": 400,
                "msg": "没有可发布的视频数据",
                "data": None
            }), 400

        # 获取启用自动发布的快手账号
        from services import get_all_accounts_handler
        accounts_result = get_all_accounts_handler(BASE_DIR)
        
        kuaishou_accounts = []
        if accounts_result.get('success'):
            # 筛选快手账号（type=4）并且启用了自动发布（auto_publish=1）
            kuaishou_accounts = [
                account for account in accounts_result.get('data', [])
                if account.get('type') == 4 and account.get('auto_publish') == 1
            ]
        
        if not kuaishou_accounts:
            return jsonify({
                "code": 400,
                "msg": "未找到启用自动发布的快手账号，请先添加快手账号并启用自动发布",
                "data": None
            }), 400

        logging.info(f"开始发布视频到 {len(kuaishou_accounts)} 个启用自动发布的快手账号")

        publish_results = []
        cleanup_files = []  # 需要清理的文件列表
        
        # 处理每个视频的发布
        for video_idx, video_result in enumerate(video_results):
            if not video_result.get('success'):
                publish_results.append({
                    'video_index': video_idx,
                    'success': False,
                    'msg': f'视频 {video_idx + 1} 处理失败，跳过发布'
                })
                continue
            try:
                video_data = video_result.get('data', {})
                publish_params = video_data

                # 从视频结果中获取商品ID
                goods_id = video_result.get('goodsId')  # 前端传递的商品ID
                
                # 如果没有有效的商品ID，跳过该视频的发布
                if not goods_id:
                    logging.warning(f"视频 {video_idx + 1} 没有有效的商品ID，跳过发布")
                    publish_results.append({
                        'video_index': video_idx,
                        'success': False,
                        'msg': f'视频 {video_idx + 1} 没有有效的商品ID，跳过发布'
                    })
                    continue
                
                # 准备发布参数
                title = custom_title if custom_title else video_data.get('caption', '')[:100]
                if not title:
                    title = f"自动发布-{video_data.get('name', 'Unknown')}"
                
                # 合并自定义标签和从视频中提取的标签
                # tags = custom_tags.copy() if custom_tags else []
                # if not tags and publish_params.get('suggestedTags'):
                #     tags = publish_params.get('suggestedTags', [])[:5]
                # 抓取视频 tag 在视频名称中
                tags = []
                
                # 获取文件列表和封面列表
                file_list = publish_params.get('ab_versions', [])
                # cover_list = publish_params.get('coverList', [])

                if not file_list:
                    publish_results.append({
                        'video_index': video_idx,
                        'success': False,
                        'msg': f'视频 {video_idx + 1} 没有可发布的文件'
                    })
                    continue

                logging.info(f"=== 发布视频 {video_idx + 1}: {title} ===")
                logging.info(f"文件数: {len(file_list)}, 账号数: {len(kuaishou_accounts)}, 商品ID: {goods_id}")
                
                # 为每个账号分配不同的AB版本进行发布
                account_results = []
                for account_idx, account in enumerate(kuaishou_accounts):
                    try:
                        # 循环分配文件，如果账号数多于文件数，则重复使用
                        file_index = account_idx % len(file_list)
                        assigned_fileObj = file_list[file_index]
                        assigned_file = assigned_fileObj.get('output_path', '')
                        assigned_cover = assigned_fileObj.get('cover_image', '')  # 封面图路径
                        
                        account_name = account.get('userName', 'Unknown')
                        account_file_path = account.get('filePath')
                        
                        logging.info(f"账号 '{account_name}' 分配文件: {assigned_file}, 商品ID: {goods_id}")
                        
                        # 调用发布函数，传递商品ID
                        from myUtils.postVideo import post_video_ks
                        
                        post_video_ks(
                            title=title,
                            files=[assigned_file],
                            tags=tags,
                            account_file=[account_file_path],
                            category=None,
                            enableTimer=False,
                            videos_per_day=1,
                            daily_times=['09:00'],
                            start_days=0,
                            goods_id=goods_id,  # 传递从前端获取的商品ID
                            cover_list=[assigned_cover] if assigned_cover else []
                        )
                        
                        account_results.append({
                            'account': account_name,
                            'file': assigned_file,
                            'success': True
                        })
                        
                        # 记录需要清理的文件
                        cleanup_files.append(assigned_file)
                        if assigned_cover:
                            cleanup_files.append(assigned_cover)
                        
                        logging.info(f"✅ 账号 '{account_name}' 发布任务提交成功")
                        
                        # 添加短暂延迟
                        time.sleep(1)
                        
                    except Exception as e:
                        account_results.append({
                            'account': account.get('userName', 'Unknown'),
                            'file': assigned_file if 'assigned_file' in locals() else 'N/A',
                            'success': False,
                            'error': str(e)
                        })
                        logging.error(f"❌ 账号 '{account.get('userName', 'Unknown')}' 发布失败: {e}")

                # 统计该视频的发布结果
                success_accounts = [r for r in account_results if r.get('success')]
                failed_accounts = [r for r in account_results if not r.get('success')]
                
                publish_results.append({
                    'video_index': video_idx,
                    'video_title': title,
                    'success': len(success_accounts) > 0,
                    'success_count': len(success_accounts),
                    'failed_count': len(failed_accounts),
                    'total_accounts': len(kuaishou_accounts),
                    'account_results': account_results,
                    'msg': f'视频 {video_idx + 1} 发布: {len(success_accounts)}/{len(kuaishou_accounts)} 个账号成功'
                })
                
                logging.info(f"视频 {video_idx + 1} 发布完成: {len(success_accounts)}/{len(kuaishou_accounts)} 个账号成功")
                
            except Exception as e:
                publish_results.append({
                    'video_index': video_idx,
                    'success': False,
                    'msg': f'视频 {video_idx + 1} 发布异常: {str(e)}'
                })
                logging.error(f"❌ 视频 {video_idx + 1} 发布异常: {e}")

        # 发布完成后清理文件
        cleanup_result = cleanup_published_files(cleanup_files)
        
        # 统计总体结果
        total_videos = len(video_results)
        successful_videos = len([r for r in publish_results if r.get('success')])
        total_accounts = len(kuaishou_accounts)
        
        overall_msg = f"发布完成: {successful_videos}/{total_videos} 个视频成功发布到 {total_accounts} 个账号"
        
        logging.info(f"=== {overall_msg} ===")
        
        return jsonify({
            "code": 200,
            "msg": overall_msg,
            "data": {
                "total_videos": total_videos,
                "successful_videos": successful_videos,
                "failed_videos": total_videos - successful_videos,
                "total_accounts": total_accounts,
                "publish_results": publish_results,
                "cleanup_result": cleanup_result
            }
        })

    except Exception as e:
        logging.error(f"publishVideos 接口出错: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        return jsonify({
            "code": 500,
            "msg": f"发布视频出错: {str(e)}",
            "data": None
        }), 500


def cleanup_published_files(file_list):
    """
    清理已发布的文件
    """
    try:
        if not file_list:
            return {'success': True, 'deleted_files': [], 'msg': '没有需要清理的文件'}
        
        deleted_files = []
        deleted_records = []
        
        with sqlite3.connect(Path(BASE_DIR / "database.db")) as conn:
            cursor = conn.cursor()
            
            for filename in set(file_list):  # 去重
                try:
                    # 提取纯文件名（去除路径）
                    clean_filename = Path(filename).name

                    # 删除文件
                    file_path = Path(BASE_DIR / "videoFile" / clean_filename)
                    if file_path.exists():
                        file_path.unlink()
                        deleted_files.append(str(file_path))
                        logging.info(f"已删除发布文件: {file_path}")

                    # 删除数据库记录 - 使用文件名匹配
                    cursor.execute("DELETE FROM file_records WHERE file_path = ? OR filename = ?", (clean_filename, clean_filename))
                    if cursor.rowcount > 0:
                        deleted_records.append(clean_filename)
                        logging.info(f"已删除数据库记录: {clean_filename}")
                    else:
                        # 如果没有匹配到，尝试模糊匹配（处理可能的文件名变化）
                        cursor.execute("DELETE FROM file_records WHERE file_path LIKE ? OR filename LIKE ?", (f"%{clean_filename}%", f"%{clean_filename}%"))
                        if cursor.rowcount > 0:
                            deleted_records.append(clean_filename)
                            logging.info(f"已删除数据库记录(模糊匹配): {clean_filename}")
                        else:
                            logging.warning(f"未找到匹配的数据库记录: {clean_filename}")

                except Exception as e:
                    logging.error(f"清理文件 {filename} 时出错: {e}")
                    continue
            
            conn.commit()
        
        logging.info(f"文件清理完成: 删除了 {len(deleted_files)} 个文件, {len(deleted_records)} 条数据库记录")
        
        return {
            'success': True,
            'deleted_files': deleted_files,
            'deleted_records': deleted_records,
            'msg': f'清理完成: 删除了 {len(deleted_files)} 个文件'
        }
        
    except Exception as e:
        logging.error(f"清理发布文件时发生异常: {e}")
        return {
            'success': False,
            'error': str(e),
            'msg': '文件清理失败'
        }


@app.route("/detailEnquire", methods=['POST'])
def detail_enquire():
    """
    快手作品详情查询接口 (原始接口保持不变)
    调用 KS-Downloader 的 detail_one 方法，使用接口传入的 cookie 参数
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                "code": 400,
                "msg": "请求参数不能为空",
                "data": None
            }), 400

        url = data.get('url', '').strip()
        cookie = data.get('cookie', '').strip()
        download = data.get('download', True)  # 默认下载视频
        proxy = data.get('proxy', '')

        # 新增参数：根据 Dashboard.vue 传入的参数
        video_generation_key = data.get('videoGenerationKey', '').strip()  # 视频生成密钥
        auto_ab = data.get('autoAB', False)  # 是否启用AB去重
        auto_publish = data.get('autoPublish', False)  # 是否自动发布

        if not url:
            return jsonify({
                "code": 400,
                "msg": "作品链接不能为空",
                "data": None
            }), 400

        # 导入并调用服务模块
        from services import process_detail_enquire_request
        
        # 执行异步任务
        if hasattr(asyncio, 'run'):
            result = asyncio.run(process_detail_enquire_request(
                url=url,
                cookie=cookie,
                download=download,
                proxy=proxy,
                video_generation_key=video_generation_key,
                auto_ab=auto_ab,
                auto_publish=auto_publish
            ))
        else:
            # Python 3.6 兼容
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(process_detail_enquire_request(
                    url=url,
                    cookie=cookie,
                    download=download,
                    proxy=proxy,
                    video_generation_key=video_generation_key,
                    auto_ab=auto_ab,
                    auto_publish=auto_publish
                ))
            finally:
                loop.close()

        logging.info("=== 处理完成 ===")
        
        # 检查结果并返回响应
        if result.get('success'):
            # 获取返回的数据
            response_data = result.get('data', {})
            
            # 如果启用了自动发布且AB处理完成，则先检查是否有可用账号
            if auto_publish and response_data and response_data.get('ab_processed'):
                logging.info("=== 检查是否有可用的自动发布账号 ===")
                
                # 预检查账号，避免在没有账号时执行发布流程
                from services import get_all_accounts_handler
                accounts_result = get_all_accounts_handler(BASE_DIR)
                
                kuaishou_accounts = []
                if accounts_result.get('success'):
                    # 筛选快手账号（type=4）并且启用了自动发布（auto_publish=1）
                    kuaishou_accounts = [
                        account for account in accounts_result.get('data', [])
                        if account.get('type') == 4 and account.get('auto_publish') == 1
                    ]
                
                if kuaishou_accounts:
                    logging.info("=== 开始执行自动发布到快手 ===")
                    try:
                        publish_result = auto_publish_to_kuaishou(response_data, auto_publish)
                        if publish_result.get('success'):
                            logging.info("自动发布到快手成功")
                            # 包含清理结果信息
                            cleanup_info = ""
                            if publish_result.get('data', {}).get('cleanup_result'):
                                cleanup_result = publish_result.get('data', {}).get('cleanup_result')
                                if cleanup_result.get('success'):
                                    deleted_files_count = len(cleanup_result.get('deleted_files', []))
                                    deleted_records_count = len(cleanup_result.get('deleted_records', []))
                                    cleanup_info = f"，已清理 {deleted_files_count} 个AB文件和 {deleted_records_count} 条数据库记录"
                            
                            response_data['auto_publish_result'] = {
                                'success': True,
                                'msg': f'自动发布到快手成功{cleanup_info}',
                                'publish_details': publish_result.get('data')
                            }
                            
                            # 自动发布成功后清理原始主视频文件
                            temp_main_video_path = response_data.get('temp_main_video_path')
                            if temp_main_video_path:
                                try:
                                    temp_file = Path(temp_main_video_path)
                                    if temp_file.exists():
                                        temp_file.unlink()
                                        logging.info(f"自动发布成功后已清理原始主视频文件: {temp_main_video_path}")
                                except Exception as e:
                                    logging.warning(f"清理原始主视频文件失败: {e}")
                        else:
                            logging.error(f"自动发布到快手失败: {publish_result.get('msg')}")
                            response_data['auto_publish_result'] = {
                                'success': False,
                                'msg': publish_result.get('msg', '自动发布失败'),
                                'publish_details': publish_result.get('data')
                            }
                    except Exception as e:
                        logging.error(f"自动发布到快手异常: {e}")
                        response_data['auto_publish_result'] = {
                            'success': False,
                            'msg': f'自动发布异常: {str(e)}'
                        }
                else:
                    logging.warning("未找到启用自动发布的快手账号，跳过自动发布，保留AB文件供手动发布")
                    response_data['auto_publish_result'] = {
                        'success': False,
                        'msg': '未找到启用自动发布的快手账号，AB文件已保留供手动发布',
                        'skipped': True  # 标识为跳过，而非失败
                    }
                    
                    # 没有可用账号时，保留原始主视频文件供手动发布
                    # 不清理temp_main_video_path，让用户可以手动选择发布
            
            # 添加发布页面需要的参数
            if response_data:
                # 添加文件路径信息，用于发布页面
                if response_data.get('filename') and response_data.get('filepath'):
                    response_data['publishParams'] = {
                        'fileList': [response_data.get('filepath')],  # 发布页面需要的文件路径数组
                        'coverList': [response_data.get('cover_image', '')],  # 封面图路径数组
                        'materialId': response_data.get('record_id'),  # 素材ID
                        'suggestedTitle': response_data.get('caption', '')[:100] if response_data.get('caption') else '',  # 建议的标题（截取前100字符）
                        'suggestedTags': [],  # 建议的标签，可以从caption中提取
                        'videoInfo': {
                            'duration': response_data.get('duration', ''),
                            'filesize': response_data.get('filesize', 0),
                            'author': response_data.get('name', ''),
                            'viewCount': response_data.get('viewCount', 0)
                        }
                    }
                    
                    # 从caption中提取可能的标签
                    caption = response_data.get('caption', '')
                    if caption:
                        import re
                        # 提取#标签
                        hashtags = re.findall(r'#([^#\s]+)', caption)
                        if hashtags:
                            response_data['publishParams']['suggestedTags'] = hashtags[:5]  # 最多5个标签
            
            return jsonify({
                "code": result.get('code', 200),
                "msg": result.get('msg', '详情查询成功'),
                "data": response_data
            })
        else:
            return jsonify({
                "code": result.get('code', 500),
                "msg": result.get('msg', '详情查询失败'),
                "data": result.get('data')
            }), result.get('code', 500)

    except Exception as e:
        logging.error(f"detailEnquire 接口出错: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        return jsonify({
            "code": 500,
            "msg": f"详情查询出错: {str(e)}",
            "data": None
        }), 500
    

@app.route('/deleteFile', methods=['GET'])
def delete_file():
    """删除单个文件"""
    from services import delete_file_handler
    
    file_id = request.args.get('id')
    
    result = delete_file_handler(BASE_DIR, file_id)
    
    return jsonify({
        "code": result['code'],
        "msg": result['msg'],
        "data": result['data']
    }), result['code']
        
@app.route('/deleteFiles', methods=['POST'])
def delete_files():
    """批量删除素材接口，接收前端传来的 id 列表"""
    from services import delete_files_handler
    
    data = request.get_json()
    ids = data.get('ids', [])
    
    result = delete_files_handler(BASE_DIR, ids)
    
    return jsonify({
        "code": result['code'],
        "msg": result['msg'],
        "data": result['data']
    }), result['code']
        
@app.route('/stats', methods=['GET'])
def get_stats():
    """获取账号总数、任务总数、视频总数等统计信息"""
    from services import get_stats_handler
    
    result = get_stats_handler(BASE_DIR)
    
    # 恢复原格式，直接返回数据
    if result['code'] == 200:
        return jsonify({"data": result['data']})
    else:
        return jsonify({"error": result['msg']}), result['code']
        
@app.route('/deleteAccount', methods=['GET'])
def delete_account():
    """删除账号"""
    from services import delete_account_handler
    
    account_id = request.args.get('id')
    
    result = delete_account_handler(BASE_DIR, account_id)
    
    return jsonify({
        "code": result['code'],
        "msg": result['msg'],
        "data": result['data']
    }), result['code']


# SSE 登录接口
@app.route('/login')
def login():
    # 1 小红书 2 视频号 3 抖音 4 快手
    type = request.args.get('type')
    # 账号名
    id = request.args.get('id')

    # 模拟一个用于异步通信的队列
    status_queue = Queue()
    active_queues[id] = status_queue

    def on_close():
        print(f"清理队列: {id}")
        del active_queues[id]
    # 启动异步任务线程
    thread = threading.Thread(target=run_async_function, args=(type,id,status_queue), daemon=True)
    thread.start()
    response = Response(sse_stream(status_queue,), mimetype='text/event-stream')
    response.headers['Cache-Control'] = 'no-cache'
    response.headers['X-Accel-Buffering'] = 'no'  # 关键：禁用 Nginx 缓冲
    response.headers['Content-Type'] = 'text/event-stream'
    response.headers['Connection'] = 'keep-alive'
    return response

@app.route('/postVideo', methods=['POST'])
def postVideo():
    # 获取JSON数据
    data = request.get_json()

    # 从JSON数据中提取fileList和accountList
    file_list = data.get('fileList', [])
    cover_list = data.get('coverList', [])
    account_list = data.get('accountList', [])
    type = data.get('type')
    title = data.get('title')
    tags = data.get('tags')
    category = data.get('category')
    enableTimer = data.get('enableTimer')
    if category == 0:
        category = None

    videos_per_day = data.get('videosPerDay')
    daily_times = data.get('dailyTimes')
    start_days = data.get('startDays')
    goods_id = data.get('goodsId')
    # 打印获取到的数据（仅作为示例）
    print("File List:", file_list)
    print("Account List:", account_list)
    match type:
        case 1:
            post_video_xhs(title, file_list, tags, account_list, category, enableTimer, videos_per_day, daily_times,
                               start_days)
        case 2:
            post_video_tencent(title, file_list, tags, account_list, category, enableTimer, videos_per_day, daily_times,
                               start_days)
        case 3:
            post_video_DouYin(title, file_list, tags, account_list, category, enableTimer, videos_per_day, daily_times,
                      start_days)
        case 4:
            post_video_ks(title, file_list, tags, account_list, category, enableTimer, videos_per_day, daily_times,
                      start_days, goods_id, cover_list)
    # 返回响应给客户端
    return jsonify(
        {
            "code": 200,
            "msg": None,
            "data": None
        }), 200

def is_cuda_available():
    """
    检查本机是否支持 CUDA（NVIDIA 显卡且 ffmpeg 支持 h264_nvenc）。
    """
    ffmpeg_bin = FFMPEG_BIN
    try:
        result = subprocess.run(
            [ffmpeg_bin, "-hide_banner", "-encoders"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            encoding="utf-8",
            errors="replace"
        )
        return "h264_nvenc" in result.stdout
    except Exception as e:
        print("检测 CUDA 失败:", e)
        return False

def test_cuda_encoding(ffmpeg_path, test_duration=2):
    """
    测试 CUDA 编码是否真正可用
    通过创建一个短的测试视频来验证 CUDA 编码器是否工作正常
    """
    import tempfile

    try:
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            temp_output = temp_file.name

        # 创建一个简单的测试视频命令
        test_cmd = [
            ffmpeg_path,
            "-f", "lavfi",
            "-i", f"testsrc=duration={test_duration}:size=320x240:rate=30",
            "-c:v", "h264_nvenc",
            "-preset", "p1",
            "-qp", "28",
            "-t", str(test_duration),
            "-y",
            temp_output
        ]

        print("测试 CUDA 编码器...")
        result = subprocess.run(
            test_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            timeout=30,  # 30秒超时
            encoding="utf-8",
            errors="replace"
        )

        # 清理测试文件
        try:
            os.unlink(temp_output)
        except:
            pass

        if result.returncode == 0:
            print("✅ CUDA 编码器测试成功")
            return True
        else:
            print(f"❌ CUDA 编码器测试失败: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ CUDA 编码器测试超时")
        return False
    except Exception as e:
        print(f"❌ CUDA 编码器测试异常: {e}")
        return False
@app.route('/changeAB', methods=['POST'])
def changeAB():
    """
    AB帧去重处理，调用同级目录 ffmpeg/ffmpeg.exe 和 ffmpeg/ffprobe.exe
    参数:
        secondaryVideoPath: 副视频绝对路径
        mainVideoPath: 主视频相对路径
        cuda: 是否启用 cuda 加速 (bool)
    """
    

    data = request.get_json()
    print("收到参数：", data)
    secondary_video = data.get('secondaryVideoPath')
    main_video = data.get('mainVideoPath')

    if not secondary_video or not main_video:
        return jsonify({
            "code": 400,
            "msg": "secondaryVideoPath 和 mainVideoPath 必须提供",
            "data": None
        }), 400

    # 绝对路径处理
    main_video_abs = str(Path(BASE_DIR / "videoFile" / main_video).resolve())
    secondary_video_abs = str(Path(secondary_video).resolve())

    # 生成新文件名和路径
    uuid_v1 = uuid.uuid1()
    filename = Path(main_video_abs).name
    final_filename = f"AB去重_{uuid_v1}_{filename}"
    output_path = str(Path(BASE_DIR / "videoFile" / final_filename))

    # ffmpeg 路径
    ffmpeg_path = FFMPEG_BIN
    ffprobe_path = FFPROBE_BIN

    if not os.path.exists(ffmpeg_path):
        return jsonify({
            "code": 500,
            "msg": "ffmpeg.exe 不存在于 ffmpeg 目录下",
            "data": None
        }), 500

    # 获取主视频时长
    def get_duration(ffprobe_path, video_path):
        cmd = [
            ffprobe_path, "-v", "error", "-show_entries",
            "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", video_path
        ]
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore')
        try:
            return float(result.stdout.strip())
        except Exception as e:
            print("获取视频时长失败:", e)
            return None

    duration = get_duration(ffprobe_path, main_video_abs)
    if not duration:
        print("无法获取主视频时长，进度无法显示。")

    # 智能选择编码器
    def select_video_encoder(ffmpeg_path):
        """
        智能选择视频编码器，优先使用 CUDA，失败时回退到 CPU
        """
        # 首先检查是否支持 CUDA 编码器
        if is_cuda_available():
            print("检测到 CUDA 支持，测试 CUDA 编码器...")
            if test_cuda_encoding(ffmpeg_path):
                print("✅ 使用 CUDA 编码器: h264_nvenc")
                return "h264_nvenc", "p1"
            else:
                print("⚠️ CUDA 编码器测试失败，可能是混合显卡环境，回退到 CPU 编码")
        else:
            print("未检测到 CUDA 支持")

        print("✅ 使用 CPU 编码器: libx264")
        return "libx264", "medium"

    video_codec, preset = select_video_encoder(ffmpeg_path)
    print(f"最终选择编码器: {video_codec}, 预设: {preset}")
    cmd = [
        ffmpeg_path,
        "-y",
        "-i", main_video_abs,
        "-stream_loop", "-1", "-i", secondary_video_abs,
        "-filter_complex",
        "[0:v]fps=60,scale=1080:1920,setsar=1[v1p];"
        "[v1p]split=2[v1i][v1r];"
        "[v1i]trim=end_frame=2,setpts=PTS-STARTPTS[init_v1f];"
        "[v1r]trim=start_frame=2,setpts=PTS-STARTPTS[v1rest];"
        "[1:v]fps=60,scale=1080:1920,setsar=1[v2p];"
        "[v2p]trim=start_frame=2,setpts=PTS-STARTPTS[v2trim];"
        "[v1rest][v2trim]interleave[inter];"
        "[init_v1f][inter]concat=n=2:v=1:a=0[vout];"
        "[0:a]anull[a1];"
        "[1:a]volume=0.001[a2];"
        "[a1][a2]amix=inputs=2:duration=first:dropout_transition=0[aout]",
        "-map", "[vout]",
        "-map", "[aout]",
        "-shortest",
        "-r", "120",
        "-c:v", video_codec,
    ]
    # 根据编码器类型添加质量参数
    if video_codec == "libx264":
        cmd += ["-crf", "28"]
    elif video_codec == "h264_nvenc":
        cmd += ["-qp", "28"]
    cmd += [
        "-preset", preset,
        "-c:a", "aac",
        "-b:a", "128k",
        output_path
    ]
    
    try:
        print("即将执行命令：", " ".join(cmd))
        # 用Popen实时读取stderr
        process = subprocess.Popen(
            cmd,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            encoding="utf-8",
            errors="replace"
        )
        time_pattern = re.compile(r'time=(\d+):(\d+):(\d+\.\d+)')
        while True:
            line = process.stderr.readline()
            if not line:
                break
            print(line.strip())
            match = time_pattern.search(line)
            if match and duration:
                h, m, s = map(float, match.groups())
                current = h * 3600 + m * 60 + s
                percent = min(current / duration * 100, 100)
                if percent < 100:
                    print(f"视频处理进度: {percent:.2f}%")
                else:
                    print("视频处理进度: 100.00%（等待文件写入完成）")
        process.wait()
        print("ffmpeg 执行完成，returncode:", process.returncode)
        process.wait()
        print("ffmpeg returncode:", process.returncode)
        if process.returncode == 0:
            # ----------- 生成封面图 -----------
            cover_name = f"{uuid_v1}_cover.jpg"
            cover_path = Path(BASE_DIR / "videoFile" / cover_name)
            ffmpeg_bin = FFMPEG_BIN
            # 提取第一帧
            subprocess.run([
                ffmpeg_bin,
                "-y",
                "-i", output_path,
                "-vf", "select=eq(n\\,0)",
                "-q:v", "2",
                str(cover_path)
            ], check=True, encoding='utf-8', errors='ignore')
            # ----------- end -----------
            # 录入数据库
            with sqlite3.connect(Path(BASE_DIR / "database.db")) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO file_records (filename, filesize, file_path, cover_image)
                    VALUES (?, ?, ?, ?)
                ''', (
                    final_filename,
                    round(float(os.path.getsize(output_path)) / (1024 * 1024), 2),
                    final_filename,
                    str(cover_path)
                ))
                conn.commit()
                print("AB处理后文件已记录")

            return jsonify({
                "code": 200,
                "msg": "AB帧去重处理成功",
                "data": {
                    "output": output_path,
                    "filename": final_filename,
                    "filepath": final_filename,
                    "cover_image": str(cover_path)
                }
            }), 200
        else:
            print("ffmpeg 执行失败")
        return jsonify({
            "code": 200,
            "msg": f"ffmpeg 执行失败",
            "data": None
        }), 200
    except Exception as e:
        import traceback
        print("changeAB异常:", str(e))
        traceback.print_exc()
        return jsonify({
            "code": 200,
            "msg": f"调用 ffmpeg 失败: {str(e)}",
            "data": None
        }), 200
        


@app.route('/changeABBySecondaryPath', methods=['POST'])
def changeABFolder():
    """
    批量AB帧去重处理，前端传入副视频文件夹路径和主视频数组，
    按顺序将文件夹下副视频与主视频数组一一对应合成。
    参数:
        secondaryFolderPath: 副视频文件夹绝对路径
        mainVideoList: 主视频文件名数组（相对 videoFile 路径）
    """
    data = request.get_json()
    print("收到参数：", data)
    secondary_folder = data.get('secondaryPath')
    main_video_list = data.get('materials')

    if not secondary_folder or not main_video_list or not isinstance(main_video_list, list):
        return jsonify({
            "code": 200,
            "msg": f"详情查询出错: {str(e)}",
            "data": None
        }), 200

    # 获取副视频文件夹下所有视频，按文件名排序
    secondary_files = sorted([
        f for f in os.listdir(secondary_folder)
        if os.path.isfile(os.path.join(secondary_folder, f))
    ])
    print("副视频文件夹内容：", secondary_files)

    if len(secondary_files) < len(main_video_list):
        return jsonify({
            "code": 400,
            "msg": "副视频数量不足",
            "data": None
        }), 400

    results = []
    for idx, main_video in enumerate(main_video_list):
        try:
            secondary_video = os.path.join(secondary_folder, secondary_files[idx])
            main_video_abs = str(Path(BASE_DIR / "videoFile" / main_video).resolve())

            # 生成新文件名和路径
            uuid_v1 = uuid.uuid1()
            filename = Path(main_video_abs).name
            final_filename = f"AB去重_{uuid_v1}_{filename}"
            output_path = str(Path(BASE_DIR / "videoFile" / final_filename))

            # ffmpeg 路径
            ffmpeg_path = FFMPEG_BIN
            ffprobe_path = FFPROBE_BIN

            # 获取主视频时长
            def get_duration(ffprobe_path, video_path):
                cmd = [
                    ffprobe_path, "-v", "error", "-show_entries",
                    "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", video_path
                ]
                result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore')
                try:
                    return float(result.stdout.strip())
                except Exception as e:
                    print("获取视频时长失败:", e)
                    return None

            duration = get_duration(ffprobe_path, main_video_abs)
            if not duration:
                print(f"{main_video} 无法获取主视频时长，进度无法显示。")

            # 智能选择编码器
            def select_video_encoder_batch(ffmpeg_path):
                """
                批量处理时的智能编码器选择
                """
                if is_cuda_available():
                    print("检测到 CUDA 支持，测试 CUDA 编码器...")
                    if test_cuda_encoding(ffmpeg_path):
                        print("✅ 批量处理使用 CUDA 编码器: h264_nvenc")
                        return "h264_nvenc", "p1"
                    else:
                        print("⚠️ CUDA 编码器测试失败，批量处理回退到 CPU 编码")
                else:
                    print("批量处理未检测到 CUDA 支持")

                print("✅ 批量处理使用 CPU 编码器: libx264")
                return "libx264", "medium"

            video_codec, preset = select_video_encoder_batch(ffmpeg_path)
            print(f"批量处理选择编码器: {video_codec}, 预设: {preset}")
            cmd = [
                ffmpeg_path,
                "-y",
                "-i", main_video_abs,
                "-stream_loop", "-1", "-i", secondary_video,
                "-filter_complex",
                "[0:v]fps=60,scale=1080:1920,setsar=1[v1p];"
                "[v1p]split=2[v1i][v1r];"
                "[v1i]trim=end_frame=2,setpts=PTS-STARTPTS[init_v1f];"
                "[v1r]trim=start_frame=2,setpts=PTS-STARTPTS[v1rest];"
                "[1:v]fps=60,scale=1080:1920,setsar=1[v2p];"
                "[v2p]trim=start_frame=2,setpts=PTS-STARTPTS[v2trim];"
                "[v1rest][v2trim]interleave[inter];"
                "[init_v1f][inter]concat=n=2:v=1:a=0[vout];"
                "[0:a]anull[a1];"
                "[1:a]volume=0.001[a2];"
                "[a1][a2]amix=inputs=2:duration=first:dropout_transition=0[aout]",
                "-map", "[vout]",
                "-map", "[aout]",
                "-shortest",
                "-r", "120",
                "-c:v", video_codec,
            ]
            # 根据编码器类型添加质量参数
            if video_codec == "libx264":
                cmd += ["-crf", "28"]
            elif video_codec == "h264_nvenc":
                cmd += ["-qp", "28"]
            cmd += [
                "-preset", preset,
                "-c:a", "aac",
                "-b:a", "128k",
                output_path
            ]

            print("即将执行命令：", " ".join(cmd))
            process = subprocess.Popen(
                cmd,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                encoding="utf-8",
                errors="replace"
            )
            while True:
                line = process.stderr.readline()
                if not line:
                    break
                print(line.strip())
            process.wait()
            print("ffmpeg 执行完成，returncode:", process.returncode)
            if process.returncode == 0:
                # ----------- 生成封面图 -----------
                cover_name = f"{uuid_v1}_cover.jpg"
                cover_path = Path(BASE_DIR / "videoFile" / cover_name)
                ffmpeg_bin = FFMPEG_BIN
                # 提取第一帧
                subprocess.run([
                    ffmpeg_bin,
                    "-y",
                    "-i", output_path,
                    "-vf", "select=eq(n\\,0)",
                    "-q:v", "2",
                    str(cover_path)
                ], check=True)
                # ----------- end -----------
                # 录入数据库
                with sqlite3.connect(Path(BASE_DIR / "database.db")) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO file_records (filename, filesize, file_path, cover_image)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        final_filename,
                        round(float(os.path.getsize(output_path)) / (1024 * 1024), 2),
                        final_filename,
                        str(cover_path)
                    ))
                    conn.commit()
                    print("AB处理后文件已记录")

                results.append({
                    "main_video": main_video,
                    "secondary_video": secondary_files[idx],
                    "output": output_path,
                    "filename": final_filename,
                    "filepath": final_filename,
                    "cover_image": str(cover_path),
                    "success": True
                })
            else:
                print("ffmpeg 执行失败")
                results.append({
                    "main_video": main_video,
                    "secondary_video": secondary_files[idx],
                    "success": False,
                    "msg": "ffmpeg 执行失败"
                })
        except Exception as e:
            import traceback
            print("changeABFolder异常:", str(e))
            traceback.print_exc()
            results.append({
                "main_video": main_video,
                "secondary_video": secondary_files[idx] if idx < len(secondary_files) else None,
                "success": False,
                "msg": str(e)
            })

    return jsonify({
        "code": 200,
        "msg": "批量AB帧去重处理完成",
        "data": results
    }), 200
    

@app.route('/changeABByFolder', methods=['POST'])
def changeABByFolder():
    """
    批量AB帧去重处理，前端传入主视频文件夹、副视频文件夹和输出文件夹路径，
    按顺序将两个文件夹下的 mp4 文件一一对应合成，合成后删除副视频文件夹内已处理的 mp4 文件。
    参数:
        mainVideoPath: 主视频文件夹绝对路径
        secondaryVideoPath: 副视频文件夹绝对路径
        outputPath: 输出文件夹绝对路径
    """
    data = request.get_json()
    print("收到参数：", data)
    main_folder = data.get('mainVideoPath')
    secondary_folder = data.get('secondaryVideoPath')
    output_folder = data.get('outputPath')

    if not main_folder or not secondary_folder :
        return jsonify({
            "code": 400,
            "msg": "mainVideoPath、secondaryVideoPath 和 outputPath 必须提供",
            "data": None
        }), 400
        
    if not output_folder:
        output_folder = str(Path(BASE_DIR / "videoFile").resolve())

    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)

    # 获取主/副视频文件夹下所有 mp4 文件，按文件名排序
    main_files = sorted([
        f for f in os.listdir(main_folder)
        if os.path.isfile(os.path.join(main_folder, f)) and f.lower().endswith('.mp4')
    ])
    secondary_files = sorted([
        f for f in os.listdir(secondary_folder)
        if os.path.isfile(os.path.join(secondary_folder, f)) and f.lower().endswith('.mp4')
    ])
    logging.info(f"请求主视频: {main_files}")
    logging.info(f"请求副视频: {secondary_files}")

    if len(main_files) == 0 or len(secondary_files) == 0:
        return jsonify({
            "code": 400,
            "msg": "主视频或副视频文件夹为空",
            "data": None
        }), 400

    min_count = min(len(main_files), len(secondary_files))
    if len(main_files) < len(secondary_files):
        logging.warning("主视频数量少于副视频，将只处理前%d个副视频", len(main_files))
    elif len(main_files) > len(secondary_files):
        logging.warning("副视频数量少于主视频，将只处理前%d个主视频", len(secondary_files))

    results = []
    for idx in range(min_count):
        main_video = main_files[idx]
        try:
            secondary_video = os.path.join(secondary_folder, secondary_files[idx])
            main_video_abs = os.path.join(main_folder, main_video)

            # 生成新文件名和路径
            uuid_v1 = uuid.uuid1()
            filename = os.path.basename(main_video_abs)
            final_filename = f"AB去重_{uuid_v1}_{filename}"
            output_path = str(Path(output_folder) / final_filename)

            # ffmpeg 路径
            ffmpeg_path = FFMPEG_BIN
            ffprobe_path = FFPROBE_BIN

            # 获取主视频时长
            def get_duration(ffprobe_path, video_path):
                cmd = [
                    ffprobe_path, "-v", "error", "-show_entries",
                    "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", video_path
                ]
                result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore')
                try:
                    return float(result.stdout.strip())
                except Exception as e:
                    print("获取视频时长失败:", e)
                    return None

            duration = get_duration(ffprobe_path, main_video_abs)
            if not duration:
                print(f"{main_video} 无法获取主视频时长，进度无法显示。")
            
            logging.info(f"主视频时长: {duration} 秒")
            logging.info(f"输出: {output_path}")
            # 智能选择编码器
            def select_video_encoder_folder(ffmpeg_path):
                """
                文件夹批量处理时的智能编码器选择
                """
                if is_cuda_available():
                    print("检测到 CUDA 支持，测试 CUDA 编码器...")
                    if test_cuda_encoding(ffmpeg_path):
                        print("✅ 文件夹批量处理使用 CUDA 编码器: h264_nvenc")
                        return "h264_nvenc", "p1"
                    else:
                        print("⚠️ CUDA 编码器测试失败，文件夹批量处理回退到 CPU 编码")
                else:
                    print("文件夹批量处理未检测到 CUDA 支持")

                print("✅ 文件夹批量处理使用 CPU 编码器: libx264")
                return "libx264", "medium"

            video_codec, preset = select_video_encoder_folder(ffmpeg_path)
            print(f"文件夹批量处理选择编码器: {video_codec}, 预设: {preset}")
            cmd = [
                ffmpeg_path,
                "-y",
                "-i", main_video_abs,
                "-stream_loop", "-1", "-i", secondary_video,
                "-filter_complex",
                "[0:v]fps=60,scale=1080:1920,setsar=1[v1p];"
                "[v1p]split=2[v1i][v1r];"
                "[v1i]trim=end_frame=2,setpts=PTS-STARTPTS[init_v1f];"
                "[v1r]trim=start_frame=2,setpts=PTS-STARTPTS[v1rest];"
                "[1:v]fps=60,scale=1080:1920,setsar=1[v2p];"
                "[v2p]trim=start_frame=2,setpts=PTS-STARTPTS[v2trim];"
                "[v1rest][v2trim]interleave[inter];"
                "[init_v1f][inter]concat=n=2:v=1:a=0[vout];"
                "[0:a]anull[a1];"
                "[1:a]volume=0.001[a2];"
                "[a1][a2]amix=inputs=2:duration=first:dropout_transition=0[aout]",
                "-map", "[vout]",
                "-map", "[aout]",
                "-shortest",
                "-r", "120",
                "-c:v", video_codec,
            ]
            # 根据编码器类型添加质量参数
            if video_codec == "libx264":
                cmd += ["-crf", "28"]
            elif video_codec == "h264_nvenc":
                cmd += ["-qp", "28"]
            cmd += [
                "-preset", preset,
                "-c:a", "aac",
                "-b:a", "128k",
                output_path
            ]

            print("即将执行命令：", " ".join(cmd))
            process = subprocess.Popen(
                cmd,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                encoding="utf-8",
                errors="replace"
            )
            while True:
                line = process.stderr.readline()
                if not line:
                    break
                print(line.strip())
            process.wait()
            print("ffmpeg 执行完成，returncode:", process.returncode)
            if process.returncode == 0:
                # ----------- 生成封面图 -----------
                cover_name = f"{uuid_v1}_cover.jpg"
                cover_path = Path(output_folder) / cover_name
                ffmpeg_bin = FFMPEG_BIN
                # 提取第一帧
                subprocess.run([
                    ffmpeg_bin,
                    "-y",
                    "-i", output_path,
                    "-vf", "select=eq(n\\,0)",
                    "-q:v", "2",
                    str(cover_path)
                ], check=True)
                # ----------- end -----------
                # 录入数据库
                with sqlite3.connect(Path(BASE_DIR / "database.db")) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO file_records (filename, filesize, file_path, cover_image)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        final_filename,
                        round(float(os.path.getsize(output_path)) / (1024 * 1024), 2),
                        final_filename,
                        str(cover_path)
                    ))
                    conn.commit()
                    file_id = cursor.lastrowid  # 获取自增ID
                    
                    logging.info(f"AB处理后文件已记录，ID: {file_id}, 文件名: {final_filename}")
                    print("AB处理后文件已记录")

                # 合成成功后删除副视频
                try:
                    os.remove(secondary_video)
                    print(f"已删除副视频: {secondary_video}")
                except Exception as e:
                    print(f"删除副视频失败: {e}")

                results.append({
                    "id": file_id,
                    "main_video": main_video,
                    "secondary_video": secondary_files[idx],
                    "output": output_path,
                    "filename": final_filename,
                    "filepath": final_filename,
                    "cover_image": str(cover_path),
                    "size": round(float(os.path.getsize(output_path)) / (1024 * 1024), 2),
                    "success": True
                })
            else:
                print("ffmpeg 执行失败")
                results.append({
                    "main_video": main_video,
                    "secondary_video": secondary_files[idx],
                    "success": False,
                    "msg": "ffmpeg 执行失败"
                })
        except Exception as e:
            import traceback
            print("changeABByFolder异常:", str(e))
            traceback.print_exc()
            results.append({
                "main_video": main_video,
                "secondary_video": secondary_files[idx] if idx < len(secondary_files) else None,
                "success": False,
                "msg": str(e)
            })

    return jsonify({
        "code": 200,
        "msg": "批量AB帧去重处理完成",
        "data": results
    }), 200
    
@app.route('/getCwd', methods=['GET'])
def get_cwd():
    """返回当前工作目录的绝对路径"""
    # from services import get_current_directory_handler
    
    # result = get_current_directory_handler(BASE_DIR)
    cwd = os.path.abspath(os.getcwd())
    return jsonify({
        "code": 200,
        "msg": "success",
        "data": cwd
    }), 200   

@app.route('/openFileInExplorer', methods=['POST'])
def open_file_in_explorer():
    """接收 filePath 参数，打开文件夹并定位到文件（仅支持 Windows）"""
    from services import open_file_in_explorer_handler
    
    data = request.get_json()
    file_path = data.get('filePath')
    
    result = open_file_in_explorer_handler(BASE_DIR, file_path)
    
    return jsonify({
        "code": result['code'],
        "msg": result['msg'],
        "data": result['data']
    }), result['code']
    
@app.route('/updateUserinfo', methods=['POST'])
def updateUserinfo():
    """更新用户信息"""
    from services import update_userinfo_handler
    
    # 获取JSON数据
    data = request.get_json()

    # 从JSON数据中提取参数
    user_id = data.get('id')
    type_value = data.get('type')
    user_name = data.get('userName', None)  # 允许 userName 不传

    result = update_userinfo_handler(BASE_DIR, user_id, type_value, user_name)
    
    return jsonify({
        "code": result['code'],
        "msg": result['msg'],
        "data": result['data']
    }), result['code']

@app.route('/updateAccountAutoPublish', methods=['POST'])
def updateAccountAutoPublish():
    """更新账号自动发布状态"""
    from services import update_auto_publish_handler
    
    # 获取JSON数据
    data = request.get_json()
    
    # 验证必需参数
    if not data:
        return jsonify({
            "code": 400,
            "msg": "请求数据不能为空",
            "data": None
        }), 400

    # 从JSON数据中提取参数
    user_id = data.get('id')
    auto_publish = data.get('autoPublish')
    
    # 验证参数
    if user_id is None:
        return jsonify({
            "code": 400,
            "msg": "用户ID不能为空",
            "data": None
        }), 400
        
    if auto_publish is None:
        return jsonify({
            "code": 400,
            "msg": "自动发布状态不能为空",
            "data": None
        }), 400

    result = update_auto_publish_handler(BASE_DIR, user_id, auto_publish)
    
    return jsonify({
        "code": result['code'],
        "msg": result['msg'],
        "data": result['data']
    }), result['code']

@app.route('/addGoodsToShowcase', methods=['POST'])
def add_goods_to_showcase():
    """
    将商品添加到快手橱窗接口
    
    参数:
        goodsId: 商品ID
    
    处理流程:
    1. 筛选快手账号（type=4）并且启用了自动发布（auto_publish=1）
    2. 对每个账号，先搜索商品，再添加到橱窗
    """
    try:
        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                "code": 400,
                "msg": "请求参数不能为空",
                "data": None
            }), 400

        goods_id = str(data.get('goodsId', '')).strip()
        
        if not goods_id:
            return jsonify({
                "code": 400,
                "msg": "商品ID不能为空",
                "data": None
            }), 400

        logging.info(f"开始为商品 {goods_id} 添加到快手橱窗")

        # 获取启用自动发布的快手账号，并包含 cookie 内容
        from services import get_kuaishou_accounts_with_cookie_handler
        accounts_result = get_kuaishou_accounts_with_cookie_handler(BASE_DIR)
        
        if not accounts_result.get('success'):
            return jsonify({
                "code": 500,
                "msg": f"获取快手账号失败: {accounts_result.get('msg')}",
                "data": None
            }), 500
        
        kuaishou_accounts = accounts_result.get('data', [])
        
        if not kuaishou_accounts:
            return jsonify({
                "code": 400,
                "msg": "没有找到启用自动发布的快手账号",
                "data": None
            }), 400

        logging.info(f"找到 {len(kuaishou_accounts)} 个启用自动发布的快手账号")

        results = []
        
        # 对每个账号进行商品添加操作
        for account in kuaishou_accounts:
            try:
                account_id = account.get('id')
                account_name = account.get('userName', f'账号{account_id}')
                cookie = account.get('cookieContent', '')
                
                if not cookie:
                    logging.warning(f"账号 {account_name} 没有有效的cookie，跳过")
                    results.append({
                        'account_id': account_id,
                        'account_name': account_name,
                        'success': False,
                        'msg': 'cookie为空或读取失败',
                        'goods_id': goods_id
                    })
                    continue

                logging.info(f"开始为账号 {account_name} 添加商品 {goods_id}")

                # 第一步：搜索商品
                search_result = search_goods_by_id(goods_id, cookie)
                
                if not search_result.get('success'):
                    logging.error(f"账号 {account_name} 搜索商品失败: {search_result.get('msg')}")
                    results.append({
                        'account_id': account_id,
                        'account_name': account_name,
                        'success': False,
                        'msg': f"搜索商品失败: {search_result.get('msg')}",
                        'goods_id': goods_id
                    })
                    continue

                # 解析搜索结果，提取商品列表
                api_response = search_result.get('data')
                if not api_response or api_response.get('result') != 1:
                    logging.warning(f"账号 {account_name} 搜索商品API返回失败: {api_response}")
                    results.append({
                        'account_id': account_id,
                        'account_name': account_name,
                        'success': False,
                        'msg': '搜索商品API返回失败',
                        'goods_id': goods_id
                    })
                    continue
                
                # 从API响应中提取商品列表
                goods_data = api_response.get('data', [])
                if not goods_data or len(goods_data) == 0:
                    logging.warning(f"账号 {account_name} 未找到商品 {goods_id}")
                    results.append({
                        'account_id': account_id,
                        'account_name': account_name,
                        'success': False,
                        'msg': '未找到指定商品',
                        'goods_id': goods_id
                    })
                    continue

                # 取第一个商品数据
                goods_info = goods_data[0]
                logging.info(f"账号 {account_name} 找到商品: {goods_info.get('itemTitle', 'N/A')}")

                # 第二步：添加到橱窗
                add_result = add_goods_to_shelf(goods_info, cookie)
                
                if add_result.get('success'):
                    logging.info(f"账号 {account_name} 成功添加商品到橱窗")
                    results.append({
                        'account_id': account_id,
                        'account_name': account_name,
                        'success': True,
                        'msg': '成功添加到橱窗',
                        'goods_id': goods_id,
                        'goods_title': goods_info.get('itemTitle', ''),
                        'goods_price': goods_info.get('zkFinalPrice', ''),
                        'commission_rate': goods_info.get('commissionRate', '')
                    })
                else:
                    logging.error(f"账号 {account_name} 添加商品到橱窗失败: {add_result.get('msg')}")
                    results.append({
                        'account_id': account_id,
                        'account_name': account_name,
                        'success': False,
                        'msg': f"添加到橱窗失败: {add_result.get('msg')}",
                        'goods_id': goods_id,
                        'goods_title': goods_info.get('itemTitle', '')
                    })

            except Exception as e:
                logging.error(f"处理账号 {account.get('userName', account.get('id'))} 时出错: {e}")
                results.append({
                    'account_id': account.get('id'),
                    'account_name': account.get('userName', f"账号{account.get('id')}"),
                    'success': False,
                    'msg': f'处理异常: {str(e)}',
                    'goods_id': goods_id
                })

        # 统计结果
        success_count = len([r for r in results if r.get('success')])
        total_count = len(results)
        
        if success_count == total_count:
            msg = f'所有 {total_count} 个账号都成功添加商品到橱窗'
            code = 200
        elif success_count > 0:
            msg = f'{success_count}/{total_count} 个账号成功添加商品到橱窗'
            code = 200
        else:
            msg = f'账号添加商品失败'
            code = 200

        logging.info(f"商品 {goods_id} 添加完成: {msg}")

        return jsonify({
            "code": code,
            "msg": msg,
            "data": {
                "goods_id": goods_id,
                "total_accounts": total_count,
                "success_count": success_count,
                "failed_count": total_count - success_count,
                "results": results
            }
        }), code

    except Exception as e:
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        return jsonify({
            "code": 500,
            "msg": f"添加商品到橱窗出错: {str(e)}",
            "data": None
        }), 500


@app.route('/testCoverGeneration', methods=['POST'])
def test_cover_generation():
    """测试封面图生成"""
    try:
        data = request.get_json()

        # 获取参数
        custom_cover_path = data.get('customCoverPath', '').strip()
        cover_title_lines = data.get('coverTitleLines', [])

        logging.info(f"🎨 测试封面图生成请求")
        logging.info(f"   自定义封面路径: {custom_cover_path}")
        logging.info(f"   标题行配置: {cover_title_lines}")

        # 验证参数
        if not custom_cover_path:
            return jsonify({
                "code": 400,
                "msg": "请先选择自定义封面图片",
                "data": None
            }), 400

        if not cover_title_lines or len(cover_title_lines) == 0:
            return jsonify({
                "code": 400,
                "msg": "请配置封面标题",
                "data": None
            }), 400

        # 检查自定义封面文件是否存在
        from pathlib import Path
        cover_path = Path(custom_cover_path)
        if not cover_path.exists():
            return jsonify({
                "code": 400,
                "msg": f"自定义封面文件不存在: {custom_cover_path}",
                "data": None
            }), 400

        # 生成测试封面图
        import asyncio
        from services.detail_enquire_service import DetailEnquireService

        service = DetailEnquireService()

        # 生成唯一的测试封面文件名
        import uuid
        test_cover_name = f"test_cover_{uuid.uuid4()}.jpg"
        test_cover_path = BASE_DIR / "videos" / test_cover_name

        # 确保目录存在
        test_cover_path.parent.mkdir(exist_ok=True)

        result = asyncio.run(service._generate_cover_with_title(
                video_path=None,  # 不使用视频，直接使用自定义封面
                cover_path=test_cover_path,
                cover_title='test',  # 不使用简单模式标题
                custom_cover_path=custom_cover_path,
                cover_font_size=72,  # 默认值，会被多行配置覆盖
                cover_font_color='#FFFF00',  # 默认值，会被多行配置覆盖
                cover_title_lines=cover_title_lines
            ))
        logging.info(f"测试封面图生成结果: {result}")
        if result and test_cover_path.exists():
            # 返回生成的封面图路径
            relative_path = f"videos/{test_cover_name}"

            return jsonify({
                "code": 200,
                "msg": "测试封面图生成成功",
                "data": {
                    "cover_path": relative_path,
                    "full_path": str(test_cover_path),
                    "cover_url": f"/api/file/{relative_path}"
                }
            })
        else:
            return jsonify({
                "code": 500,
                "msg": "封面图生成失败",
                "data": None
            }), 500

    except Exception as e:
        logging.error(f"测试封面图生成失败: {e}")
        logging.error(f"错误堆栈: {traceback.format_exc()}")
        return jsonify({
            "code": 500,
            "msg": f"生成失败: {str(e)}",
            "data": None
        }), 500


def search_goods_by_id(goods_id, cookie):
    """
    根据商品ID搜索商品信息
    
    Args:
        goods_id: 商品ID
        cookie: 快手账号cookie
        
    Returns:
        dict: 搜索结果
    """
    try:
        import requests
        import json
        import urllib3
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        url = 'https://cps.kwaixiaodian.com/gateway/distribute/match/selection/home/<USER>/item/list'
        
        headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'cookie': cookie,
            'dnt': '1',
            'kpf': 'PC_WEB',
            'kpn': '',
            'origin': 'https://cps.kwaixiaodian.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://cps.kwaixiaodian.com/pc/promoter/selection-center/home',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        }
        
        payload = {
            "orderType": 0,
            "channelId": 99,
            "keyWord": str(goods_id),
            "requestType": "1_1",
            "pcursor": 0
        }
        
        response = requests.post(url, headers=headers, data=json.dumps(payload), verify=False, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            logging.info(f"搜索商品 {goods_id} 成功，返回数据: {json.dumps(result, ensure_ascii=False)[:200]}...")
            return {
                'success': True,
                'data': result,
                'msg': '搜索成功'
            }
        else:
            logging.error(f"搜索商品 {goods_id} 失败，状态码: {response.status_code}, 响应: {response.text}")
            return {
                'success': False,
                'data': None,
                'msg': f'搜索失败，状态码: {response.status_code}'
            }
            
    except Exception as e:
        logging.error(f"搜索商品 {goods_id} 异常: {e}")
        return {
            'success': False,
            'data': None,
            'msg': f'搜索异常: {str(e)}'
        }


def add_goods_to_shelf(goods_info, cookie):
    """
    将商品添加到快手橱窗
    
    Args:
        goods_info: 商品信息（从搜索接口返回的数据）
        cookie: 快手账号cookie
        
    Returns:
        dict: 添加结果
    """
    try:
        import requests
        import json
        import urllib3
        import uuid
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        url = 'https://cps.kwaixiaodian.com/gateway/distribute/match/shelf/item/save'
        
        headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'cookie': cookie,
            'dnt': '1',
            'kpf': 'PC_WEB',
            'kpn': '',
            'origin': 'https://cps.kwaixiaodian.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://cps.kwaixiaodian.com/pc/promoter/selection-center/home',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        }
        
        # 从商品信息中提取必要字段
        distribute_item_id = goods_info.get('distributeItemId')
        rel_item_id = goods_info.get('relItemId')
        activity_id = goods_info.get('activityId', 0)
        second_activity_id = goods_info.get('secondActivityId', 0)
        
        # 构造请求数据
        payload = {
            "distributeItemId": distribute_item_id,
            "relItemId": rel_item_id,
            "activityId": activity_id,
            "secondActivityId": second_activity_id,
            "saveType": "add",
            "isReplace": 0,
            "bizCode": "pcShelfItemSelection",
            "statisticsInfo": {
                "pageUrl": "https://cps.kwaixiaodian.com/pc/promoter/selection-center/galaxy-landing?sceneId=678",
                "refer": "https://cps.kwaixiaodian.com/pc/promoter/selection-center/home",
                "appName": "PC",
                "terminal": "PC",
                "commissionId": goods_info.get('bestCommissionId'),
                "commissionType": goods_info.get('bestCommissionType', 1),
                "ext": goods_info.get('ext', {})
            }
        }
        
        # 为ext添加matchId
        if 'ext' in payload['statisticsInfo']:
            payload['statisticsInfo']['ext']['matchId'] = str(uuid.uuid4())
        
        logging.info(f"添加商品到橱窗，商品ID: {rel_item_id}, 分发商品ID: {distribute_item_id}")
        
        response = requests.post(url, headers=headers, data=json.dumps(payload), verify=False, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            logging.info(f"添加商品 {rel_item_id} 到橱窗成功，返回: {json.dumps(result, ensure_ascii=False)}")
            return {
                'success': True,
                'data': result,
                'msg': '添加成功'
            }
        else:
            logging.error(f"添加商品 {rel_item_id} 到橱窗失败，状态码: {response.status_code}, 响应: {response.text}")
            return {
                'success': False,
                'data': None,
                'msg': f'添加失败，状态码: {response.status_code}'
            }
            
    except Exception as e:
        logging.error(f"添加商品到橱窗异常: {e}")
        return {
            'success': False,
            'data': None,
            'msg': f'添加异常: {str(e)}'
        }
@app.route('/postVideoBatch', methods=['POST'])
def postVideoBatch():
    data_list = request.get_json()

    if not isinstance(data_list, list):
        return jsonify({"error": "Expected a JSON array"}), 400
    for data in data_list:
        # 从JSON数据中提取fileList和accountList
        file_list = data.get('fileList', [])
        account_list = data.get('accountList', [])
        type = data.get('type')
        title = data.get('title')
        tags = data.get('tags')
        category = data.get('category')
        enableTimer = data.get('enableTimer')
        if category == 0:
            category = None

        videos_per_day = data.get('videosPerDay')
        daily_times = data.get('dailyTimes')
        start_days = data.get('startDays')
        # 打印获取到的数据（仅作为示例）
        print("File List:", file_list)
        print("Account List:", account_list)
        match type:
            case 1:
                return
            case 2:
                post_video_tencent(title, file_list, tags, account_list, category, enableTimer, videos_per_day, daily_times,
                                   start_days)
            case 3:
                post_video_DouYin(title, file_list, tags, account_list, category, enableTimer, videos_per_day, daily_times,
                          start_days)
            case 4:
                post_video_ks(title, file_list, tags, account_list, category, enableTimer, videos_per_day, daily_times,
                          start_days)
    # 返回响应给客户端
    return jsonify(
        {
            "code": 200,
            "msg": None,
            "data": None
        }), 200

# 包装函数：在线程中运行异步函数
def run_async_function(type,id,status_queue):
    match type:
        case '1':
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(xiaohongshu_cookie_gen(id, status_queue))
            loop.close()
        case '2':
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(get_tencent_cookie(id,status_queue))
            loop.close()
        case '3':
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(douyin_cookie_gen(id,status_queue))
            loop.close()
        case '4':
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(get_ks_shop_cookie(id,status_queue))
            loop.close()
        case '5':
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(get_ks_shop_cookie(id,status_queue))
            loop.close()

# SSE 流生成器函数
def sse_stream(status_queue):
    while True:
        if not status_queue.empty():
            msg = status_queue.get()
            yield f"data: {msg}\n\n"
        else:
            # 避免 CPU 占满
            time.sleep(0.1)

@app.route('/api/logs/test', methods=['GET'])
def test_logs():
    """测试日志功能"""
    from services import process_test_logs_request, create_log_service
    
    # 创建日志服务实例
    log_service = create_log_service(log_cache, log_lock)
    
    # 调用服务处理请求
    return process_test_logs_request(log_service)

@app.route('/api/logs', methods=['GET'])
def get_logs():
    """
    获取服务器日志接口

    查询参数:
    - limit: 返回的日志条数，默认100，最大1000
    - level: 日志级别过滤 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    - since: 获取指定时间戳之后的日志
    """
    from services import process_get_logs_request, create_log_service
    
    # 创建日志服务实例
    log_service = create_log_service(log_cache, log_lock)
    
    # 调用服务处理请求
    return process_get_logs_request(log_service)

@app.route('/api/logs/clear', methods=['POST'])
def clear_logs():
    """清空日志缓存"""
    from services import process_clear_logs_request, create_log_service
    
    # 创建日志服务实例
    log_service = create_log_service(log_cache, log_lock)
    
    # 调用服务处理请求
    return process_clear_logs_request(log_service)

@app.route('/logs')
def logs_page():
    """日志查看页面"""
    from services import create_log_service
    
    # 创建日志服务实例
    log_service = create_log_service(log_cache, log_lock)
    
    # 调用服务处理请求
    return log_service.get_logs_page()

@app.route('/api/logs/stream', methods=['GET'])
def logs_stream():
    """
    实时日志流接口 (Server-Sent Events)
    """
    from services import process_logs_stream_request, create_log_service
    
    # 创建日志服务实例
    log_service = create_log_service(log_cache, log_lock)
    
    # 调用服务处理请求
    return process_logs_stream_request(log_service)
    response.headers['Content-Type'] = 'text/event-stream'
    response.headers['Connection'] = 'keep-alive'
    return response

if __name__ == '__main__':
    app.run(host='0.0.0.0' ,port=5409, debug=False)