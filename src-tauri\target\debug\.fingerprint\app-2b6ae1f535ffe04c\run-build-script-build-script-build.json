{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 10738080623338194979], [2069998946850810971, "build_script_build", false, 6004442947985132289], [3834743577069889284, "build_script_build", false, 11173995135369254946], [13890802266741835355, "build_script_build", false, 13969976156735227185], [15441187897486245138, "build_script_build", false, 4028724776219481343], [2253952315205409758, "build_script_build", false, 2049420980202614042]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-2b6ae1f535ffe04c\\output", "paths": ["src/test/ks_sig3.jar", "tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}