#!/usr/bin/env python3
"""
专门修复 Nuitka 运行时断言错误的打包脚本
使用更保守的打包策略
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def prepare_build_environment():
    """准备构建环境"""
    print("准备构建环境...")
    
    # 清理旧的构建文件
    build_dirs = ["build", "out", "dist"]
    for build_dir in build_dirs:
        if os.path.exists(build_dir):
            print(f"清理旧的构建目录: {build_dir}")
            try:
                if os.name == "nt":
                    result = subprocess.run(
                        ["powershell", "-Command", f"Remove-Item -Recurse -Force '{build_dir}' -ErrorAction SilentlyContinue"],
                        capture_output=True,
                        text=True
                    )
                else:
                    shutil.rmtree(build_dir)
            except Exception as e:
                print(f"警告: 清理目录 {build_dir} 时出错: {e}")
    
    os.makedirs("out", exist_ok=True)
    print("构建环境准备完成")
    return True

def build_with_nuitka():
    """使用 Nuitka 进行打包 - 运行时修复版本"""
    print("开始 Nuitka 打包...")
    
    # 运行时修复版本的 Nuitka 命令
    nuitka_cmd = [
        "nuitka.cmd" if os.name == "nt" else "nuitka",
        "--standalone",
        "--show-progress",
        "--output-dir=out",
        "--windows-icon-from-ico=logo.ico",
        
        # 关键修复参数
        "--disable-dll-dependency-cache",
        "--assume-yes-for-downloads",
        "--mingw64",
        
        # 禁用可能导致运行时问题的优化
        "--plugin-disable=anti-bloat",
        "--plugin-disable=pylint-warnings",
        
        # 使用更保守的导入策略
        "--nofollow-imports",
        
        # 只跟踪必要的项目模块
        "--follow-import-to=services",
        "--follow-import-to=utils",
        "--follow-import-to=myUtils", 
        "--follow-import-to=uploader",
        
        # 基本系统模块
        "--follow-import-to=sqlite3",
        "--follow-import-to=asyncio",
        "--follow-import-to=logging",
        "--follow-import-to=json",
        "--follow-import-to=pathlib",
        
        # Flask 相关
        "--follow-import-to=flask",
        "--follow-import-to=flask_cors",
        "--follow-import-to=requests",
        
        # 包含必要的包
        "--include-package=sqlite3",
        "--include-package=asyncio", 
        "--include-package=logging",
        "--include-package=json",
        "--include-package=pathlib",
        "--include-package=flask",
        "--include-package=requests",
        
        # 主文件
        "main.py"
    ]
    
    print("执行命令:", " ".join(nuitka_cmd))
    
    try:
        result = subprocess.run(nuitka_cmd, check=True, capture_output=True, text=True)
        print("Nuitka 打包成功!")
        return True
    except subprocess.CalledProcessError as e:
        print("Nuitka 打包失败!")
        print("错误输出:", e.stderr[-1000:] if e.stderr else "无错误输出")
        return False

def post_build_setup():
    """构建后的设置"""
    print("执行构建后设置...")
    
    dist_dir = Path("out/main.dist")
    if not dist_dir.exists():
        print("构建目录不存在!")
        return False
    
    # 复制项目文件
    project_files = [
        "logo.ico",
        "cookiesFile", 
        "videoFile",
        "database.db",
        "ffmpeg",
        "services",
        "utils",
        "myUtils",
        "uploader",
        "conf.py",
        "ffmpeg_config.py"
    ]
    
    for file_path in project_files:
        if os.path.exists(file_path):
            try:
                if os.path.isdir(file_path):
                    dest = dist_dir / file_path
                    if dest.exists():
                        shutil.rmtree(dest)
                    shutil.copytree(file_path, dest)
                    print(f"复制目录: {file_path}")
                else:
                    shutil.copy2(file_path, dist_dir)
                    print(f"复制文件: {file_path}")
            except Exception as e:
                print(f"复制失败 {file_path}: {e}")
    
    # 手动复制 KS-Downloader（作为数据文件）
    copy_ks_downloader(dist_dir)
    
    # 创建运行时修复的启动脚本
    create_runtime_fix_script(dist_dir)
    
    return True

def copy_ks_downloader(dist_dir):
    """手动复制 KS-Downloader"""
    print("复制 KS-Downloader...")
    
    if os.path.exists("KS-Downloader"):
        try:
            dest = dist_dir / "KS-Downloader"
            if dest.exists():
                shutil.rmtree(dest)
            shutil.copytree("KS-Downloader", dest)
            print("✓ KS-Downloader 复制成功")
        except Exception as e:
            print(f"❌ KS-Downloader 复制失败: {e}")

def create_runtime_fix_script(dist_dir):
    """创建运行时修复的启动脚本"""
    script_content = """@echo off
echo ========================================
echo 小超媒体管理系统启动中...
echo ========================================
echo.

REM 设置环境变量以修复运行时问题
set PYTHONPATH=%~dp0;%~dp0\\services;%~dp0\\utils;%~dp0\\myUtils;%~dp0\\uploader
set NUITKA_RUNTIME_DEBUG=1

echo 设置运行时环境...
echo PYTHONPATH=%PYTHONPATH%
echo.

echo 启动主程序...
main.exe

echo.
echo ========================================
if errorlevel 1 (
    echo 程序异常退出，错误代码: %errorlevel%
    echo.
    echo 可能的解决方案:
    echo 1. 检查依赖版本兼容性
    echo 2. 确保所有必要文件已复制
    echo 3. 尝试在开发环境中运行 python main.py
) else (
    echo 程序正常退出
)
echo ========================================
pause
"""
    
    script_path = dist_dir / "启动.bat"
    with open(script_path, 'w', encoding='gbk') as f:
        f.write(script_content)
    
    print(f"创建运行时修复启动脚本: {script_path}")

def main():
    """主函数"""
    print("=" * 70)
    print("小超媒体管理系统 - 运行时断言错误修复版打包工具")
    print("=" * 70)
    
    # 检查基本文件
    if not os.path.exists("main.py"):
        print("错误: 未找到 main.py 文件")
        sys.exit(1)
    
    if not os.path.exists("services"):
        print("错误: 未找到 services 目录")
        sys.exit(1)
    
    # 检查 Nuitka
    nuitka_cmd = "nuitka.cmd" if os.name == "nt" else "nuitka"
    try:
        result = subprocess.run([nuitka_cmd, "--version"], check=True, capture_output=True, text=True)
        print("✓ Nuitka 已安装")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("错误: 未找到 Nuitka")
        sys.exit(1)
    
    # 执行构建步骤
    steps = [
        ("准备构建环境", prepare_build_environment),
        ("Nuitka 打包", build_with_nuitka),
        ("构建后设置", post_build_setup),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*25} {step_name} {'='*25}")
        if not step_func():
            print(f"❌ 步骤失败: {step_name}")
            sys.exit(1)
        print(f"✓ {step_name} 完成")
    
    print("\n" + "="*70)
    print("🎉 运行时修复版打包完成!")
    print("📁 可执行文件: out/main.dist/main.exe")
    print("🚀 启动脚本: out/main.dist/启动.bat")
    print()
    print("💡 如果仍有问题，请:")
    print("   1. 使用启动脚本运行")
    print("   2. 检查错误日志")
    print("   3. 确认开发环境运行正常")
    print("=" * 70)

if __name__ == "__main__":
    main()
