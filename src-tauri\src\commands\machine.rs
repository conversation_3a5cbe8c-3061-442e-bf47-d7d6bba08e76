use sysinfo::{Networks, System};


#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct MachineInfo {
    mac: String,
    ip_address: String,
    machine_name: String,
}

#[tauri::command]
pub fn get_machine_info() -> MachineInfo {
    let mut machine_name= "".to_string();
    // let mut sys = System::new_all();

    if let Some(name) = System::host_name() {
        machine_name = name;
    }

    // println!("System host name:        {:?}", System::host_name());
    println!("name : {}", machine_name);

    let networks = Networks::new_with_refreshed_list();
    let mut ip_addrs = vec![];
    let mut mac_addrs = vec![];
    
    for (interface_name, network) in &networks {
        // 获取 MAC 地址
        let ips = network.ip_networks();
        for ip in ips {
            if ip.addr.is_ipv4() {
                ip_addrs.push(ip.addr.to_string());
            }
        }
        let mac_address = network.mac_address();
        mac_addrs.push(mac_address.to_string());
        println!("Interface: {}", interface_name);
        println!("MAC Address: {}", mac_address);
    }

    MachineInfo {
        mac:  mac_addrs.join(" & "),
        ip_address: ip_addrs.join(" & "),
        machine_name,
    }
}