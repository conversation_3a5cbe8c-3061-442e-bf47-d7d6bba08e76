/**
 * 链接提取工具
 * 用于从文本中提取链接并从HTML中提取MP4链接
 */

/**
 * 从文本中提取链接
 * @param {string} text - 输入文本
 * @returns {string[]} - 提取到的链接数组
 */
export const extractLinksFromText = (text) => {
  // 匹配 HTTP/HTTPS 链接的正则表达式
  const urlRegex = /https?:\/\/[^\s\n\r]+/g;
  const matches = text.match(urlRegex) || [];
  
  // 清理链接，移除可能的尾随字符
  return matches.map(link => {
    // 移除常见的尾随标点符号
    return link.replace(/[.,;!?。，；！？]*$/, '');
  }).filter(link => link.length > 0);
};

/**
 * 从字符串中提取MP4链接
 * @param {string} text - 输入字符串（可能是HTML内容）
 * @returns {string[]} - 提取到的MP4链接数组
 */
export const extractMp4Links = (text) => {
  console.log('🔍 开始提取MP4链接，输入文本长度:', text.length);
  
  let mp4Links = [];
  
  // 首先尝试从 window.INIT_STATE 中提取
  try {
    console.log('📋 尝试提取 window.INIT_STATE...');
    
    // 匹配 window.INIT_STATE 的多种可能格式
    const initStatePatterns = [
      /window\.INIT_STATE\s*=\s*({.*?});/gs,
      /window\.INIT_STATE\s*=\s*(\[.*?\]);/gs,
      /window\["INIT_STATE"\]\s*=\s*({.*?});/gs,
      /window\["INIT_STATE"\]\s*=\s*(\[.*?\]);/gs,
    ];
    
    let initStateData = null;
    
    for (const pattern of initStatePatterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        console.log('✅ 找到 INIT_STATE 匹配:', match[1].substring(0, 200) + '...');
        try {
          initStateData = JSON.parse(match[1]);
          console.log('📊 成功解析 INIT_STATE JSON:', JSON.stringify(initStateData, null, 2));
          break;
        } catch (parseError) {
          console.log('❌ JSON解析失败:', parseError.message);
          console.log('🔍 原始匹配内容:', match[1]);
        }
      }
    }
    
    // 如果找到了 INIT_STATE 数据，从中提取 MP4 链接
    if (initStateData) {
      console.log('🎬 开始从 INIT_STATE 中提取 MP4 链接...');
      const extractedLinks = extractMp4FromObject(initStateData);
      mp4Links = mp4Links.concat(extractedLinks);
      console.log(`🎯 从 INIT_STATE 中提取到 ${extractedLinks.length} 个 MP4 链接:`, extractedLinks);
    }
  } catch (error) {
    console.log('❌ 处理 INIT_STATE 时出错:', error.message);
  }
  
  // 如果从 INIT_STATE 中没有找到链接，使用原有的正则表达式方法
  if (mp4Links.length === 0) {
    console.log('🔄 使用正则表达式方法提取 MP4 链接...');
    
    // MP4链接匹配模式
    const patterns = [
      // 直接的MP4链接（包含查询参数）
      /https?:\/\/[^\s]+\.mp4(?:\?[^\s]*)?/gi,
      // 快手CDN链接模式
      /https?:\/\/v\d*\.kwaicdn\.com\/[^\s]*\.mp4(?:\?[^\s]*)?/gi,
      // 在JSON或其他格式中的MP4链接（去除引号）
      /"(https?:\/\/[^"]+\.mp4(?:\?[^"]*)?)"/gi,
      /'(https?:\/\/[^']+\.mp4(?:\?[^']*)?)'/gi,
    ];

    patterns.forEach((pattern, index) => {
      console.log(`🔍 应用模式 ${index + 1}:`, pattern);
      let match;
      let patternMatches = 0;
      while ((match = pattern.exec(text)) !== null) {
        const url = match[1] || match[0];
        if (url && !mp4Links.includes(url)) {
          mp4Links.push(url);
          patternMatches++;
        }
      }
      console.log(`📊 模式 ${index + 1} 匹配到 ${patternMatches} 个链接`);
    });
  }

  // 去重并过滤有效链接
  const uniqueLinks = [...new Set(mp4Links)].filter(link => {
    // 检查是否是有效的HTTP链接且包含.mp4
    if (!link.startsWith('http') || !link.includes('.mp4')) {
      return false;
    }

    // 特别处理快手CDN链接
    if (link.includes('kwaicdn.com') && link.includes('.mp4')) {
      return true;
    }

    // 检查.mp4是否在查询参数之前（确保是真正的MP4文件）
    const mp4Index = link.indexOf('.mp4');
    const queryIndex = link.indexOf('?');

    if (queryIndex === -1) {
      // 没有查询参数，直接检查是否以.mp4结尾或后面跟着#
      return mp4Index > 0 && (link.endsWith('.mp4') || link.charAt(mp4Index + 4) === '#');
    } else {
      // 有查询参数，检查.mp4是否在?之前
      return mp4Index > 0 && mp4Index < queryIndex;
    }
  });

  console.log(`🎉 最终提取到 ${uniqueLinks.length} 个有效的 MP4 链接:`, uniqueLinks);
  return uniqueLinks;
};

/**
 * 递归提取对象中的MP4链接
 * @param {any} obj - 要搜索的对象
 * @returns {string[]} - 找到的MP4链接数组
 */
const extractMp4FromObject = (obj) => {
  const mp4Links = [];
  
  const searchObject = (item, path = '') => {
    if (typeof item === 'string') {
      // 检查字符串是否是MP4链接
      if (item.includes('.mp4') && (item.startsWith('http://') || item.startsWith('https://'))) {
        console.log(`🔗 在路径 "${path}" 找到 MP4 链接:`, item);
        mp4Links.push(item);
      }
    } else if (Array.isArray(item)) {
      item.forEach((element, index) => {
        searchObject(element, `${path}[${index}]`);
      });
    } else if (typeof item === 'object' && item !== null) {
      Object.keys(item).forEach(key => {
        searchObject(item[key], path ? `${path}.${key}` : key);
      });
    }
  };
  
  searchObject(obj);
  return mp4Links;
};

/**
 * 测试快手链接格式的函数
 * @param {string} testUrl - 测试用的快手MP4链接
 */
