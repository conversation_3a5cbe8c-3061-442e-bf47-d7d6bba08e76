<template>
  <div class="material-management">
    <div class="page-header">
      <h1>素材管理</h1>
    </div>

    <div class="material-list-container">
      <div class="material-search">
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索素材..."
            clearable
            @input="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="action-section">
          <div>
            <!-- <el-button type="primary" @click="handleABFormFolderInline"
              >批量AB帧去重</el-button
            >
            <el-button type="primary" @click="handleSelectionAB"
              >素材AB帧去重</el-button
            >
            <el-button type="danger" @click="handleSelectionDelete"
              >批量删除</el-button
            > -->
            <el-button type="primary" @click="handleSelectionABPush"
              >去发布</el-button
            >
            <el-button type="danger" @click="handleSelectionDelete"
              >批量删除</el-button
            >
          </div>
          <div class="action-buttons">
            <!-- <el-button type="primary" @click="handleUploadMaterial"
              >添加素材</el-button
            > -->
            <el-button type="info" @click="fetchMaterials" :loading="false">
              <el-icon :class="{ 'is-loading': isRefreshing }"
                ><Refresh
              /></el-icon>
              <span v-if="isRefreshing">刷新中</span>
            </el-button>
          </div>
        </div>
      </div>

      <div v-if="totalMaterials > 0" class="material-list">
        <el-table
          @selection-change="handleSelectionChange"
          :data="paginatedMaterials"
          style="width: 100%"
        >
          <el-table-column
            type="selection"
            :selectable="selectable"
            width="55"
          />
          <el-table-column prop="filename" label="文件名" />
          <el-table-column prop="cover_image" label="封面图">
            <template #default="scope, index">
              <div style="display: flex; align-items: center">
                <el-image
                  style="width: 80px; margin-right: 10px"
                  :src="
                    convertFileSrc(scope.row.cover_image) + '?t=' + Date.now()
                  "
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="srcList"
                  :hide-on-click-modal="true"
                  show-progress
                  :initial-index="scope.$index"
                  fit="cover"
                  :preview-teleported="true"
                />
                <el-button @click="handelSelectCoverFile(scope.row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="filesize" label="文件大小" width="120">
            <template #default="scope"> {{ scope.row.filesize }} MB </template>
          </el-table-column>
          <el-table-column prop="upload_time" label="上传时间" width="180" />
          <el-table-column prop="file_path" label="查看文件" width="80">
            <template #default="scope">
              <el-button @click="goToFolder(scope.row.file_path)">
                <el-icon><Folder /></el-icon>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <!-- <el-button size="small" @click="handlePreview(scope.row)"
                >预览</el-button
              > -->

              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
              <el-button
                size="small"
                type="warning"
                @click="handleAB(scope.row)"
                >AB帧去重</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalMaterials"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <div v-else class="empty-data">
        <el-empty description="暂无素材数据" />
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传素材"
      width="40%"
      @close="handleUploadDialogClose"
    >
      <div class="upload-form">
        <el-form label-width="80px">
          <!-- <el-form-item label="文件名称:">
            <el-input v-model="customFilename" placeholder="选填" clearable />
          </el-form-item> -->
          <el-form-item label="选择文件">
            <el-upload
              class="upload-demo"
              drag
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
              accept=".mp4"
              multiple
              :limit="20"
            >
              <el-icon class="el-icon--upload"><Upload /></el-icon>
              <div class="el-upload__text">
                支持批量上传 MP4 视频文件（最多 20 个）
              </div>
              <template #tip>
                <div class="el-upload__tip">
                  仅支持 MP4 视频文件，最多上传 20 个
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitUpload"
            :loading="isUploading"
          >
            {{ isUploading ? "上传中" : "确认上传" }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="素材预览"
      width="50%"
      :top="'10vh'"
    >
      <div class="preview-container" v-if="currentMaterial">
        <div
          v-if="isVideoFile(currentMaterial.filename) && previewDialogVisible"
          class="video-preview"
        >
          <video controls style="max-width: 100%; max-height: 60vh">
            <source
              :src="getPreviewUrl(currentMaterial.file_path)"
              type="video/mp4"
            />
            您的浏览器不支持视频播放
          </video>
        </div>
        <div
          v-else-if="isImageFile(currentMaterial.filename)"
          class="image-preview"
        >
          <img
            :src="getPreviewUrl(currentMaterial.file_path)"
            style="max-width: 100%; max-height: 60vh"
          />
        </div>
        <div v-else class="file-info">
          <p>文件名: {{ currentMaterial.filename }}</p>
          <p>文件大小: {{ currentMaterial.filesize }} MB</p>
          <p>上传时间: {{ currentMaterial.upload_time }}</p>
          <el-button type="primary" @click="downloadFile(currentMaterial)"
            >下载文件</el-button
          >
        </div>
      </div>
    </el-dialog>
    <el-dialog
      v-model="abVisible"
      title="AB帧单个去重"
      width="50%"
      :top="'10vh'"
    >
      <el-form :model="ABformInline" class="demo-form-inline">
        <el-form-item label="选择副视频">
          <el-input
            v-model="ABformInline.secondaryVideoPath"
            readonly
            autocomplete="off"
          >
            <template #append>
              <el-button
                @click="
                  () => {
                    selectSecondaryVideoPath();
                  }
                "
              >
                选择副视频
              </el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitAB">开始去重</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      v-model="ABFormFolderInline.visible"
      title="AB帧文件夹去重上传"
      width="50%"
      :top="'10vh'"
    >
      <p style="margin-bottom: 20px">
        提示：为避免利用副视频重复去重不过审核，去重后将自动删除副视频文件夹视频
      </p>
      <el-form :model="ABFormFolderInline" class="demo-form-inline">
        <el-form-item label="">
          <el-input
            v-model="ABFormFolderInline.mainVideoPath"
            readonly
            autocomplete="off"
          >
            <template #append>
              <el-button
                @click="
                  () => {
                    selectSecondaryVideoFolderPath();
                  }
                "
              >
                选择主视频文件夹
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="ABFormFolderInline.secondaryVideoPath"
            readonly
            autocomplete="off"
          >
            <template #append>
              <el-button
                @click="
                  () => {
                    selectSecondaryVideoFolderPath(false);
                  }
                "
              >
                选择副视频文件夹
              </el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitABFolder"
            >开始去重</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      v-model="abBrachVisible"
      title="添加副视频文件夹去重"
      width="50%"
      :top="'10vh'"
    >
      <el-form :model="ABBatchformInline" class="demo-form-inline">
        <el-form-item label="选择副视频文件夹">
          <el-input
            v-model="ABBatchformInline.secondaryPath"
            readonly
            autocomplete="off"
          >
            <template #append>
              <el-button
                @click="
                  () => {
                    selectSecondaryFilePath();
                  }
                "
              >
                选择副视频文件夹
              </el-button>
            </template>
          </el-input>
          <p>文件夹内视频数量需跟选中文件数量一致</p>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmitBatchAB"
            >开始根据文件夹去重</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { Refresh, Upload, Search } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { materialApi } from "@/api/material";
import { useAppStore } from "@/stores/app";
import {
  selectFile,
  selectFolder,
  goFolder,
  selectCoverFile,
} from "@/utils/fileUtils";
import { convertFileSrc } from "@tauri-apps/api/core";
import { useRouter } from "vue-router";
const router = useRouter();

// 获取应用状态管理
const appStore = useAppStore();

// 搜索和状态控制
const searchKeyword = ref("");
const isRefreshing = ref(false);
const isUploading = ref(false);

// 分页控制
const currentPage = ref(1);
const pageSize = ref(10);

// 对话框控制
const uploadDialogVisible = ref(false);
const previewDialogVisible = ref(false);
const currentMaterial = ref(null);

// 文件上传
const fileList = ref([]);
const customFilename = ref("");

const abVisible = ref(false);
const abBrachVisible = ref(false);
// ab帧去重
const ABformInline = reactive({
  secondaryVideoPath: "",
});
// ab帧去重 文件夹方式
const ABFormFolderInline = reactive({
  visible: false,
  mainVideoPath: "",
  secondaryVideoPath: "",
  outputPath: "",
});

const srcList = computed(() => {
  return appStore.materials.map((material) => {
    return convertFileSrc(material.cover_image);
  });
});

const goToFolder = async (path) => {
  // 请求获取当前工作目录
  const response = await materialApi.getBasePath();

  if (response.code === 200) {
    const filePath = response.data + "/" + "videoFile" + "/" + path; // 替换反斜杠为正斜杠
    await materialApi.openFolder({ filePath });

    ElMessage.success("打开路径" + filePath);
  }
};

const handleABFormFolderInline = async () => {
  // 打开AB帧去重对话框
  ABFormFolderInline.visible = true;
  // 清空表单
  ABFormFolderInline.mainVideoPath = "";
  ABFormFolderInline.secondaryVideoPath = "";
  ABFormFolderInline.outputPath = "";
};

const ABBatchformInline = reactive({
  secondaryPath: "",
});

const multipleSelection = ref([]);

const handleSelectionChange = (val) => {
  // 处理选中行的逻辑
  multipleSelection.value = val;
};

// 控制行是否可选择
const selectable = (row, index) => {
  return true; // 所有行都可选择，可以根据需要添加逻辑
};
// 获取素材列表
const fetchMaterials = async () => {
  isRefreshing.value = true;
  try {
    const response = await materialApi.getAllMaterials();

    if (response.code === 200) {
      appStore.setMaterials(response.data);
      // 重置分页到第一页
      currentPage.value = 1;
      // 清空选中的项目
      multipleSelection.value = [];
      ElMessage.success("刷新成功");
    } else {
      ElMessage.error("获取素材列表失败");
    }
  } catch (error) {
    console.error("获取素材列表出错:", error);
    ElMessage.error("获取素材列表失败");
  } finally {
    isRefreshing.value = false;
  }
};

const handleSelectionDelete = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning("请先选择要删除的素材");
    return;
  }
  ElMessageBox.confirm(
    `确定要删除选中的 ${multipleSelection.value.length} 个素材吗？`,
    "警告",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(async () => {
      try {
        const ids = multipleSelection.value.map((item) => item.id);
        const response = await materialApi.deleteMaterials(ids);
        if (response.code === 200) {
          ElMessage.success("批量删除成功");
          await fetchMaterials();
        } else {
          ElMessage.error(response.msg || "批量删除失败");
        }
      } catch (error) {
        console.error("批量删除出错:", error);
        ElMessage.error("批量删除失败");
      }
    })
    .catch(() => {
      // 用户取消
    });
};

// 过滤和分页后的素材
const filteredMaterials = computed(() => {
  let filtered = [...appStore.materials];

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter((material) =>
      material.filename.toLowerCase().includes(keyword)
    );
  }

  // 倒序排列（按id或upload_time）
  filtered.sort((a, b) => {
    // 如果有upload_time，按时间倒序
    if (a.upload_time && b.upload_time) {
      return new Date(b.upload_time) - new Date(a.upload_time);
    }
    // 否则按id倒序
    return (b.id || 0) - (a.id || 0);
  });

  return filtered;
});

// 分页后的素材
const paginatedMaterials = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredMaterials.value.slice(start, end);
});

// 总数
const totalMaterials = computed(() => filteredMaterials.value.length);

// 搜索处理
const handleSearch = () => {
  // 搜索时重置到第一页
  currentPage.value = 1;
  // 清空选中的项目
  multipleSelection.value = [];
};

// 分页处理
const handleCurrentChange = (page) => {
  currentPage.value = page;
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; // 切换每页显示数量时重置到第一页
};

// AB帧去重处理
const handleAB = (row) => {
  abVisible.value = !abVisible.value;
  if (abVisible.value) {
    // 打开对话框时初始化表单
    ABformInline.mainVideoPath = row.file_path; // 设置主视频路径
    ABformInline.secondaryVideoPath = ""; // 清空副视频路径
  } else {
    // 关闭对话框时清空表单
    ABformInline.mainVideoPath = "";
    ABformInline.secondaryVideoPath = "";
  }
};

const onSubmitAB = async () => {
  console.log("submit!", ABformInline);
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  try {
    const response = await materialApi.changeMaterialAB(ABformInline);

    if (response.code === 200) {
      ElMessage.success("快手AB帧去重成功");
      // 刷新素材列表
      await fetchMaterials();
    } else {
      ElMessage.error(response.msg || "快手AB帧去重失败");
    }
  } catch (error) {
    console.error("快手AB帧去重出错:", error);
    ElMessage.error("快手AB帧去重失败");
  } finally {
    loading.close();
    abVisible.value = false; // 关闭对话框
  }
};

const onSubmitABFolder = async () => {
  console.log("submit!", ABFormFolderInline);
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  try {
    const response = await materialApi.changeABByFolder(ABFormFolderInline);

    if (response.code === 200) {
      ElMessage.success("快手AB帧去重成功");
      // 刷新素材列表
      await fetchMaterials();
    }
  } catch (error) {
    console.error("快手AB帧去重出错:", error);
    ElMessage.error("快手AB帧去重失败");
  } finally {
    loading.close();
    ABFormFolderInline.visible = false; // 关闭对话框
  }
};

const onSubmitBatchAB = async () => {
  console.log("submit!", ABformInline);
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  try {
    const response = await materialApi.changeABBySecondaryPath({
      secondaryPath: ABBatchformInline.secondaryPath,
      materials: multipleSelection.value.map((item) => item.file_path), // 传入选中的素材文件路径
    });

    if (response.code === 200) {
      ElMessage.success("快手AB帧去重成功");
      // 刷新素材列表
      await fetchMaterials();
    } else {
      ElMessage.error(response.msg || "快手AB帧去重失败");
    }
  } catch (error) {
    console.error("快手AB帧去重出错:", error);
    // ElMessage.error("快手AB帧去重失败");
  } finally {
    loading.close();
    abBrachVisible.value = false; // 关闭对话框
  }
};

const onSubmitBatchABFolder = async () => {
  console.log("submit!", ABformInline);
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)",
  });
  try {
    const response = await materialApi.changeABByFolder({
      secondaryPath: ABBatchformInline.secondaryPath,
      materials: multipleSelection.value.map((item) => item.file_path), // 传入选中的素材文件路径
    });

    if (response.code === 200) {
      ElMessage.success("快手AB帧去重成功");
      // 刷新素材列表
      await fetchMaterials();
    } else {
      ElMessage.error(response.msg || "快手AB帧去重失败");
    }
  } catch (error) {
    console.error("快手AB帧去重出错:", error);
    // ElMessage.error("快手AB帧去重失败");
  } finally {
    loading.close();
    abBrachVisible.value = false; // 关闭对话框
  }
};

const handleSelectionAB = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning("请先选择要去重的素材");
    return;
  }

  console.log("选择的素材:", multipleSelection.value);
  // changeMaterialAB(multipleSelection.value[0]);

  // 打开AB帧去重对话框
  abBrachVisible.value = true;

  // 清空表单
  ABBatchformInline.secondaryPath = "";
};

const handleSelectionABPush = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning("请先选择素材");
    return;
  }

  console.log("选择的素材:", multipleSelection.value);
  // 跳转并传递参数
  router.push({
    path: "/publish-center",
    query: {
      ids: multipleSelection.value.map((item) => item.id).join(","),
    },
    state: {
      materials: multipleSelection.value,
    },
  });
};

// 编辑时的文件选择函数
const selectSecondaryVideoPath = async () => {
  const fileUrl = await selectFile(["MP4", "mp4"], "请选择副视频文件");
  if (fileUrl) {
    ABformInline.secondaryVideoPath = fileUrl;
    console.log("路径:", fileUrl);
  }
};

// 编辑时的文件选择函数
const selectSecondaryVideoFolderPath = async (isMain = true) => {
  const fileUrl = await selectFolder("请选择文件夹");
  if (fileUrl) {
    if (isMain) {
      ABFormFolderInline.mainVideoPath = fileUrl;
    } else {
      ABFormFolderInline.secondaryVideoPath = fileUrl;
    }
    console.log("路径:", fileUrl);
  }
};

const handelSelectCoverFile = async (material) => {
  try {
    const response = await selectCoverFile(material);
    if (response.success) {
      ElMessage.success(response.msg || "封面图更新成功");
      // 刷新素材列表
      await fetchMaterials();
    } else {
      ElMessage.error(response.msg || "封面图更新失败");
    }
  } catch (error) {
    console.error("更新封面图出错:", error);
    ElMessage.error("封面图更新失败");
  }
};

// 编辑时的文件选择函数
const selectOutputPathPath = async () => {
  const fileUrl = await selectFolder("请选择输出文件夹");
  if (fileUrl) {
    ABFormFolderInline.outputPath = fileUrl;
    console.log("路径:", fileUrl);
  }
};

// 编辑时的文件选择函数
const selectSecondaryFilePath = async () => {
  const fileUrl = await selectFolder("请选择副视频文件文件夹");
  if (fileUrl) {
    ABBatchformInline.secondaryPath = fileUrl;
    console.log("路径:", fileUrl);
  }
};

// 上传素材
const handleUploadMaterial = () => {
  // 清空变量
  fileList.value = [];
  customFilename.value = "";
  uploadDialogVisible.value = true;
};

// 关闭上传对话框时清空变量
const handleUploadDialogClose = () => {
  fileList.value = [];
  customFilename.value = "";
};

// 文件选择变更
const handleFileChange = (file, uploadFileList) => {
  // 只保留 MP4 文件
  fileList.value = uploadFileList.filter(
    (f) =>
      f.raw &&
      (f.raw.type === "video/mp4" || f.raw.name.toLowerCase().endsWith(".mp4"))
  );
};

// 提交上传
const submitUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning("请选择要上传的文件");
    return;
  }

  isUploading.value = true;

  try {
    const formData = new FormData();
    fileList.value.forEach((f) => {
      if (f.raw) formData.append("file", f.raw);
    });
    if (customFilename.value.trim()) {
      formData.append("filename", customFilename.value.trim());
    }
    const response = await materialApi.uploadMaterial(formData);

    if (response.code === 200) {
      ElMessage.success("上传成功");
      uploadDialogVisible.value = false;
      await fetchMaterials();
    } else {
      ElMessage.error(response.msg || "上传失败");
    }
  } catch (error) {
    console.error("上传素材出错:", error);
    ElMessage.error("上传失败: " + (error.message || "未知错误"));
  } finally {
    isUploading.value = false;
  }
};

// 预览素材
const handlePreview = async (material) => {
  currentMaterial.value = null;
  previewDialogVisible.value = true;
  ElMessage.info("加载中...");
  try {
    // 等待一小段时间以确保对话框已打开
    await new Promise((resolve) => setTimeout(resolve, 100));
    currentMaterial.value = material;
  } catch (error) {
    console.error("预览素材出错:", error);
    ElMessage.error("预览加载失败");
    previewDialogVisible.value = false;
  }
};

// 删除素材
const handleDelete = (material) => {
  ElMessageBox.confirm(`确定要删除素材 ${material.filename} 吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        const response = await materialApi.deleteMaterial(material.id);

        if (response.code === 200) {
          appStore.removeMaterial(material.id);
          ElMessage.success("删除成功");
        } else {
          ElMessage.error(response.msg || "删除失败");
        }
      } catch (error) {
        console.error("删除素材出错:", error);
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 获取预览URL
const getPreviewUrl = (filePath) => {
  const filename = filePath.split("/").pop();
  return materialApi.getMaterialPreviewUrl(filename);
};

// 下载文件
const downloadFile = (material) => {
  const url = materialApi.downloadMaterial(material.file_path);
  window.open(url, "_blank");
};

// 判断文件类型
const isVideoFile = (filename) => {
  const videoExtensions = [".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv"];
  return videoExtensions.some((ext) => filename.toLowerCase().endsWith(ext));
};

const isImageFile = (filename) => {
  const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
  return imageExtensions.some((ext) => filename.toLowerCase().endsWith(ext));
};

// 组件挂载时获取素材列表
onMounted(() => {
  // 只有store中没有数据时才获取
  // multipleSelection= [];
  fetchMaterials();
});
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.material-management {
  display: flex;
  flex: 1;
  height: 100%;
  flex-direction: column;
  .page-header {
    margin-bottom: 20px;

    h1 {
      font-size: 24px;
      font-weight: 500;
      color: $text-primary;
      margin: 0;
    }
  }

  .material-list-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    min-height: 0; // 关键，防止子元素溢出

    .material-search {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
      flex-wrap: wrap;
      gap: 20px;

      .search-section {
        flex: 1;
        min-width: 300px;

        .search-input {
          max-width: 400px;
        }
      }

      .action-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
      }

      .action-buttons {
        display: flex;
        gap: 10px;

        .is-loading {
          animation: rotate 1s linear infinite;
        }
      }
    }

    .material-list {
      flex: 1 1 0;
      min-height: 0;
      overflow: auto;
      margin-top: 20px;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
      padding: 20px 0;
      border-top: 1px solid #ebeef5;
    }

    .empty-data {
      padding: 40px 0;
    }
  }

  .material-upload {
    width: 100%;
  }

  .preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 0 20px;

    .file-info {
      text-align: center;
      margin-top: 20px;
    }
  }
}

.upload-form {
  padding: 0 20px;

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }

  .upload-demo {
    width: 100%;
  }
}

.dialog-footer {
  padding: 0 20px;
  display: flex;
  justify-content: flex-end;
}

/* 覆盖Element Plus对话框样式 */
:deep(.el-dialog__body) {
  padding: 20px 0;
}

:deep(.el-dialog__header) {
  padding-left: 20px;
  padding-right: 20px;
  margin-right: 0;
}

:deep(.el-dialog__footer) {
  padding-top: 10px;
  padding-bottom: 15px;
}
</style>
