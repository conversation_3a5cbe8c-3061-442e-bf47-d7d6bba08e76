<template>
  <div class="test-http-container">
    <h2>Tauri HTTP 插件测试</h2>
    
    <div class="test-section">
      <h3>测试结果</h3>
      <div class="test-results">
        <pre>{{ testResults }}</pre>
      </div>
    </div>
    
    <div class="test-buttons">
      <el-button @click="runBasicTest" type="primary">基本 GET 测试</el-button>
      <el-button @click="runPostTest" type="success">POST 测试</el-button>
      <el-button @click="runErrorTest" type="warning">错误处理测试</el-button>
      <el-button @click="runAllTests" type="info">运行所有测试</el-button>
      <el-button @click="clearResults" type="danger">清除结果</el-button>
    </div>
    
    <div class="auth-test-section">
      <h3>认证请求测试</h3>
      <el-button @click="testAuthRequest" type="primary">测试认证请求</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { 
  testBasicFetch, 
  testPostRequest, 
  testErrorHandling, 
  runAllTests as runAllHttpTests 
} from '@/utils/testTauriHttp'
import { authHttp } from '@/utils/authRequest'

const testResults = ref('')

const addResult = (title, result) => {
  const timestamp = new Date().toLocaleTimeString()
  testResults.value += `\n[${timestamp}] ${title}:\n${JSON.stringify(result, null, 2)}\n${'='.repeat(50)}\n`
}

const runBasicTest = async () => {
  try {
    ElMessage.info('开始基本 GET 测试...')
    const result = await testBasicFetch()
    addResult('基本 GET 测试', result)
    if (result.success) {
      ElMessage.success('基本 GET 测试通过!')
    } else {
      ElMessage.error('基本 GET 测试失败!')
    }
  } catch (error) {
    addResult('基本 GET 测试异常', { error: error.message, stack: error.stack })
    ElMessage.error('基本 GET 测试异常!')
  }
}

const runPostTest = async () => {
  try {
    ElMessage.info('开始 POST 测试...')
    const result = await testPostRequest()
    addResult('POST 测试', result)
    if (result.success) {
      ElMessage.success('POST 测试通过!')
    } else {
      ElMessage.error('POST 测试失败!')
    }
  } catch (error) {
    addResult('POST 测试异常', { error: error.message, stack: error.stack })
    ElMessage.error('POST 测试异常!')
  }
}

const runErrorTest = async () => {
  try {
    ElMessage.info('开始错误处理测试...')
    const result = await testErrorHandling()
    addResult('错误处理测试', result)
    if (result.success) {
      ElMessage.success('错误处理测试通过!')
    } else {
      ElMessage.error('错误处理测试失败!')
    }
  } catch (error) {
    addResult('错误处理测试异常', { error: error.message, stack: error.stack })
    ElMessage.error('错误处理测试异常!')
  }
}

const runAllTests = async () => {
  try {
    ElMessage.info('开始运行所有测试...')
    const results = await runAllHttpTests()
    addResult('所有测试结果', results)
    
    const allSuccess = Object.values(results).every(result => result.success)
    if (allSuccess) {
      ElMessage.success('所有测试通过!')
    } else {
      ElMessage.warning('部分测试失败!')
    }
  } catch (error) {
    addResult('所有测试异常', { error: error.message, stack: error.stack })
    ElMessage.error('所有测试异常!')
  }
}

const testAuthRequest = async () => {
  try {
    ElMessage.info('开始认证请求测试...')
    
    // 测试一个简单的认证请求
    const result = await authHttp.get('/test', { test: 'value' })
    addResult('认证请求测试', { success: true, result })
    ElMessage.success('认证请求测试通过!')
    
  } catch (error) {
    addResult('认证请求测试', { 
      success: false, 
      error: error.message, 
      stack: error.stack 
    })
    ElMessage.error('认证请求测试失败!')
  }
}

const clearResults = () => {
  testResults.value = ''
  ElMessage.info('测试结果已清除')
}
</script>

<style scoped>
.test-http-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 20px;
}

.test-results {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  white-space: pre-wrap;
}

.test-buttons {
  margin-bottom: 20px;
}

.test-buttons .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.auth-test-section {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}

h3 {
  color: #666;
  margin-bottom: 15px;
}
</style>
