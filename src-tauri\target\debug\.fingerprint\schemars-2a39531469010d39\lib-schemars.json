{"rustc": 16591470773350601817, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 12301657607982069544, "profile": 2225463790103693989, "path": 3962391000231692252, "deps": [[79555249104361687, "dyn_clone", false, 9003223478866891381], [494707368875582417, "schemars_derive", false, 5432805507383874772], [666257361909484702, "build_script_build", false, 5729934364467623629], [3150220818285335163, "url", false, 7873035309852176416], [8324636962323428845, "serde_json", false, 4833084022683961222], [10967960060725374459, "serde", false, 6766045105638999282], [14923790796823607459, "indexmap", false, 8666269561266021241], [16829695972291065638, "uuid1", false, 17123160507215050589]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-2a39531469010d39\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}