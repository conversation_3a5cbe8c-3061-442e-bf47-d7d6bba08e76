<template>
  <div class="utils-test">
    <h2>Utils 功能测试</h2>
    
    <el-card class="test-card">
      <template #header>
        <span>签名生成测试</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="查询参数">
          <el-input 
            v-model="testForm.queryParams" 
            placeholder='{"user_id": "12345", "timestamp": "1640995200"}'
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item label="数据参数">
          <el-input 
            v-model="testForm.dataParams" 
            placeholder='{"action": "login", "device": "mobile"}'
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testSigPy" :loading="loading.sigPy">
            生成 MD5 签名
          </el-button>
        </el-form-item>
        
        <el-form-item label="生成的签名" v-if="results.signature">
          <el-input v-model="results.signature" readonly />
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>令牌生成测试</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="签名">
          <el-input v-model="testForm.sig" placeholder="输入签名或使用上面生成的签名" />
        </el-form-item>
        
        <el-form-item label="客户端盐值">
          <el-input v-model="testForm.clientSalt" placeholder="client_salt_123" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testTokenPy" :loading="loading.tokenPy">
            生成 SHA256 令牌
          </el-button>
        </el-form-item>
        
        <el-form-item label="生成的令牌" v-if="results.token">
          <el-input v-model="results.token" readonly />
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>签名提取测试</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="输入文本">
          <el-input 
            v-model="testForm.extractText" 
            placeholder="Java output: ##abc123def456## end"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testExtractSignature" :loading="loading.extract">
            提取签名
          </el-button>
        </el-form-item>
        
        <el-form-item label="提取结果" v-if="results.extracted !== null">
          <el-input v-model="results.extracted" readonly />
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>Java JAR 签名测试</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="Java 状态">
          <el-tag :type="javaStatus.available ? 'success' : 'danger'">
            {{ javaStatus.available ? 'Java 可用' : 'Java 不可用' }}
          </el-tag>
          <el-button size="small" @click="checkJava" style="margin-left: 10px;">
            检查 Java
          </el-button>
        </el-form-item>
        
        <el-form-item label="JAR 文件路径" v-if="javaStatus.jarPath">
          <el-input v-model="javaStatus.jarPath" readonly />
        </el-form-item>
        
        <el-form-item label="参数">
          <el-input v-model="testForm.sig3Param" placeholder="test_parameter" />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="testGetSig3" 
            :loading="loading.sig3"
            :disabled="!javaStatus.available"
          >
            生成 Sig3 签名
          </el-button>
        </el-form-item>
        
        <el-form-item label="Sig3 结果" v-if="results.sig3">
          <el-alert 
            :title="results.sig3.success ? '成功' : '失败'" 
            :type="results.sig3.success ? 'success' : 'error'"
            :description="results.sig3.success ? results.sig3.signature : results.sig3.error"
            show-icon
          />
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <span>批量测试</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="参数列表">
          <el-input 
            v-model="testForm.batchParams" 
            placeholder='["param1", "param2", "param3"]'
            type="textarea"
            :rows="2"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testBatchSig3" :loading="loading.batch">
            批量生成签名
          </el-button>
        </el-form-item>
        
        <el-form-item label="批量结果" v-if="results.batch.length > 0">
          <div v-for="(result, index) in results.batch" :key="index" style="margin-bottom: 10px;">
            <el-alert 
              :title="`参数 ${index + 1}: ${result.success ? '成功' : '失败'}`"
              :type="result.success ? 'success' : 'error'"
              :description="result.success ? result.signature : result.error"
              show-icon
            />
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import { ElMessage } from 'element-plus'

const testForm = reactive({
  queryParams: '{"user_id": "12345", "timestamp": "1640995200"}',
  dataParams: '{"action": "login", "device": "mobile"}',
  sig: '',
  clientSalt: 'client_salt_123',
  extractText: 'Java output: ##abc123def456## end',
  sig3Param: 'test_parameter',
  batchParams: '["param1", "param2", "param3"]'
})

const loading = reactive({
  sigPy: false,
  tokenPy: false,
  extract: false,
  sig3: false,
  batch: false
})

const results = reactive({
  signature: '',
  token: '',
  extracted: null,
  sig3: null,
  batch: []
})

const javaStatus = reactive({
  available: false,
  jarPath: ''
})

// 测试 MD5 签名生成
const testSigPy = async () => {
  loading.sigPy = true
  try {
    const queryParams = JSON.parse(testForm.queryParams)
    const dataParams = JSON.parse(testForm.dataParams)
    
    const signature = await invoke('sig_py', { queryParams, dataParams })
    results.signature = signature
    testForm.sig = signature // 自动填充到令牌测试
    
    ElMessage.success('MD5 签名生成成功')
  } catch (error) {
    ElMessage.error(`签名生成失败: ${error}`)
  } finally {
    loading.sigPy = false
  }
}

// 测试 SHA256 令牌生成
const testTokenPy = async () => {
  loading.tokenPy = true
  try {
    const token = await invoke('token_py', { 
      sig: testForm.sig, 
      clientSalt: testForm.clientSalt 
    })
    results.token = token
    
    ElMessage.success('SHA256 令牌生成成功')
  } catch (error) {
    ElMessage.error(`令牌生成失败: ${error}`)
  } finally {
    loading.tokenPy = false
  }
}

// 测试签名提取
const testExtractSignature = async () => {
  loading.extract = true
  try {
    const extracted = await invoke('extract_signature', { text: testForm.extractText })
    results.extracted = extracted || '未找到签名'
    
    ElMessage.success('签名提取完成')
  } catch (error) {
    ElMessage.error(`签名提取失败: ${error}`)
  } finally {
    loading.extract = false
  }
}

// 检查 Java 环境
const checkJava = async () => {
  try {
    javaStatus.available = await invoke('check_java_available')
    
    if (javaStatus.available) {
      try {
        javaStatus.jarPath = await invoke('get_jar_path')
      } catch (error) {
        javaStatus.jarPath = `JAR 文件错误: ${error}`
      }
    }
    
    ElMessage({
      type: javaStatus.available ? 'success' : 'warning',
      message: javaStatus.available ? 'Java 环境正常' : 'Java 环境不可用'
    })
  } catch (error) {
    ElMessage.error(`检查 Java 失败: ${error}`)
  }
}

// 测试 Sig3 生成
const testGetSig3 = async () => {
  loading.sig3 = true
  try {
    const result = await invoke('get_sig3', { param: testForm.sig3Param })
    results.sig3 = result
    
    ElMessage({
      type: result.success ? 'success' : 'error',
      message: result.success ? 'Sig3 签名生成成功' : 'Sig3 签名生成失败'
    })
  } catch (error) {
    ElMessage.error(`Sig3 生成失败: ${error}`)
  } finally {
    loading.sig3 = false
  }
}

// 测试批量生成
const testBatchSig3 = async () => {
  loading.batch = true
  try {
    const params = JSON.parse(testForm.batchParams)
    const batchResults = await invoke('batch_get_sig3', { params })
    results.batch = batchResults
    
    const successCount = batchResults.filter(r => r.success).length
    ElMessage.success(`批量生成完成: ${successCount}/${batchResults.length} 成功`)
  } catch (error) {
    ElMessage.error(`批量生成失败: ${error}`)
  } finally {
    loading.batch = false
  }
}

// 组件挂载时检查 Java 环境
onMounted(() => {
  checkJava()
})
</script>

<style scoped>
.utils-test {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.test-card :deep(.el-card__header) {
  background-color: #f5f7fa;
  font-weight: bold;
}
</style>
