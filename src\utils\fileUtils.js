import { invoke } from "@tauri-apps/api/core";
import { open } from "@tauri-apps/plugin-dialog";
import { Command } from "@tauri-apps/plugin-shell";
import { materialApi } from "@/api/material";
import { ElMessage } from "element-plus";
import { appDataDir } from "@tauri-apps/api/path";

export const selectFile = async (
  type = ["audio"],
  selectText = "请选择文件"
) => {
  const fileSrc = await open({
    directory: false,
    multiple: false,
    title: selectText,
    filters: [
      {
        name: "All Files",
        extensions: type,
      },
    ],
  });

  if (fileSrc) {
    return fileSrc;
  }
};

export async function selectFolder(text = "请选择文件夹", defaultPath = "") {
  // const path = await appDataDir();
  // console.log("选择文件夹:", path);
  if (defaultPath) {
    // 兼容 Windows 路径
    const normalizedPath = defaultPath.replace(/\//g, "\\");
    const lastSep = normalizedPath.lastIndexOf("\\");
    if (lastSep > 0) {
      defaultPath = normalizedPath.substring(0, lastSep);
    }
  }
  try {
    const selectedFolder = await open({
      directory: true,
      multiple: false,
      title: text,
      defaultPath: defaultPath,
    });

    if (selectedFolder) {
      return selectedFolder;
    }
    throw new Error("No Folder Selected");
  } catch (error) {
    throw error;
  }
}

export const goFolder = async (filePath) => {
  if (!filePath) return;
  // Windows 下 explorer.exe /select, 可定位到文件
  filePath = filePath.replace(/\//g, "\\");
  console.log("打开文件夹路径:", filePath);

  const command = new Command("explorer", ["/select,", filePath]);
  await command.spawn();
};

export const selectCoverFile = async (material) => {
  const fileUrl = await selectFile(["jpg", "jpeg", "png"], "请选择封面图");
  if (fileUrl) {
    try {
      const response = await materialApi.updateMaterialCoverImage({
        materialId: material.id,
        fileUrl: fileUrl,
      });
      if (response.code === 200) {
        // 返回成功
        return { success: true, msg: "封面图更新成功", data: response.data };
      } else {
        // 返回失败
        return { success: false, msg: response.msg || "封面图更新失败" };
      }
    } catch (error) {
      console.error("更新封面图出错:", error);
      return { success: false, msg: "封面图更新失败" };
    }
  } else {
    return { success: false, msg: "未选择封面图" };
  }
};
