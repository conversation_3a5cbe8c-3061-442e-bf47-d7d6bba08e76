#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手上传工具类
包含各种辅助函数和工具方法
"""

import hashlib
import json
import mimetypes
import os
import re
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import urllib.parse


class VideoValidator:
    """视频文件验证器"""
    
    SUPPORTED_FORMATS = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm'}
    MAX_FILE_SIZE = 2 * 1024 * 1024 * 1024  # 2GB
    MIN_FILE_SIZE = 1024  # 1KB
    
    @classmethod
    def validate_file(cls, file_path: str) -> Tuple[bool, str]:
        """
        验证视频文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            path = Path(file_path)
            
            # 检查文件是否存在
            if not path.exists():
                return False, f"文件不存在: {file_path}"
            
            # 检查是否为文件
            if not path.is_file():
                return False, f"路径不是文件: {file_path}"
            
            # 检查文件扩展名
            if path.suffix.lower() not in cls.SUPPORTED_FORMATS:
                return False, f"不支持的文件格式: {path.suffix}"
            
            # 检查文件大小
            file_size = path.stat().st_size
            if file_size < cls.MIN_FILE_SIZE:
                return False, f"文件过小: {file_size} bytes"
            
            if file_size > cls.MAX_FILE_SIZE:
                return False, f"文件过大: {file_size} bytes (最大: {cls.MAX_FILE_SIZE} bytes)"
            
            # 检查MIME类型
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type and not mime_type.startswith('video/'):
                return False, f"文件类型不是视频: {mime_type}"
            
            return True, ""
            
        except Exception as e:
            return False, f"文件验证异常: {str(e)}"
    
    @classmethod
    def get_file_info(cls, file_path: str) -> Dict[str, Union[str, int]]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            path = Path(file_path)
            stat = path.stat()
            
            return {
                'name': path.name,
                'stem': path.stem,
                'suffix': path.suffix,
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'created_time': stat.st_ctime,
                'modified_time': stat.st_mtime,
                'mime_type': mimetypes.guess_type(file_path)[0] or 'unknown'
            }
        except Exception as e:
            return {'error': str(e)}


class TextValidator:
    """文本验证器"""
    
    MAX_TITLE_LENGTH = 100
    MAX_DESCRIPTION_LENGTH = 2000
    
    @classmethod
    def validate_title(cls, title: str) -> Tuple[bool, str]:
        """
        验证标题
        
        Args:
            title: 标题文本
            
        Returns:
            (是否有效, 错误信息)
        """
        if not title or not title.strip():
            return False, "标题不能为空"
        
        title = title.strip()
        
        if len(title) > cls.MAX_TITLE_LENGTH:
            return False, f"标题过长: {len(title)} 字符 (最大: {cls.MAX_TITLE_LENGTH})"
        
        # 检查是否包含非法字符
        if cls._contains_illegal_chars(title):
            return False, "标题包含非法字符"
        
        return True, ""
    
    @classmethod
    def validate_description(cls, description: str) -> Tuple[bool, str]:
        """
        验证描述
        
        Args:
            description: 描述文本
            
        Returns:
            (是否有效, 错误信息)
        """
        if not description:
            description = ""
        
        description = description.strip()
        
        if len(description) > cls.MAX_DESCRIPTION_LENGTH:
            return False, f"描述过长: {len(description)} 字符 (最大: {cls.MAX_DESCRIPTION_LENGTH})"
        
        return True, ""
    
    @classmethod
    def _contains_illegal_chars(cls, text: str) -> bool:
        """检查是否包含非法字符"""
        # 定义非法字符模式
        illegal_patterns = [
            r'[<>:"/\\|?*]',  # 文件名非法字符
            r'[\x00-\x1f]',   # 控制字符
        ]
        
        for pattern in illegal_patterns:
            if re.search(pattern, text):
                return True
        
        return False
    
    @classmethod
    def clean_text(cls, text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除首尾空白
        text = text.strip()
        
        # 替换多个连续空白为单个空格
        text = re.sub(r'\s+', ' ', text)
        
        # 移除非法字符
        text = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '', text)
        
        return text


class HashGenerator:
    """哈希生成器"""
    
    @staticmethod
    def md5_hash(data: Union[str, bytes]) -> str:
        """生成MD5哈希"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        return hashlib.md5(data).hexdigest()
    
    @staticmethod
    def sha256_hash(data: Union[str, bytes]) -> str:
        """生成SHA256哈希"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        return hashlib.sha256(data).hexdigest()
    
    @staticmethod
    def file_hash(file_path: str, algorithm: str = 'md5') -> str:
        """计算文件哈希"""
        hash_func = hashlib.md5() if algorithm == 'md5' else hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        
        return hash_func.hexdigest()


class UrlBuilder:
    """URL构建器"""
    
    @staticmethod
    def build_url(base_url: str, endpoint: str, params: Optional[Dict[str, str]] = None) -> str:
        """
        构建URL
        
        Args:
            base_url: 基础URL
            endpoint: 端点路径
            params: 查询参数
            
        Returns:
            完整URL
        """
        # 确保base_url不以/结尾，endpoint以/开头
        base_url = base_url.rstrip('/')
        endpoint = endpoint if endpoint.startswith('/') else '/' + endpoint
        
        url = base_url + endpoint
        
        if params:
            query_string = urllib.parse.urlencode(params)
            url = f"{url}?{query_string}"
        
        return url
    
    @staticmethod
    def parse_url(url: str) -> Dict[str, str]:
        """解析URL"""
        parsed = urllib.parse.urlparse(url)
        return {
            'scheme': parsed.scheme,
            'netloc': parsed.netloc,
            'path': parsed.path,
            'params': parsed.params,
            'query': parsed.query,
            'fragment': parsed.fragment
        }


class TimeUtils:
    """时间工具类"""
    
    @staticmethod
    def current_timestamp() -> int:
        """获取当前时间戳（毫秒）"""
        return int(time.time() * 1000)
    
    @staticmethod
    def format_time(timestamp: Optional[float] = None, fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
        """格式化时间"""
        if timestamp is None:
            timestamp = time.time()
        return time.strftime(fmt, time.localtime(timestamp))
    
    @staticmethod
    def parse_duration(duration_str: str) -> int:
        """解析时长字符串为秒数"""
        # 支持格式: "1:23", "01:23:45", "123"
        parts = duration_str.split(':')
        
        if len(parts) == 1:
            return int(parts[0])
        elif len(parts) == 2:
            return int(parts[0]) * 60 + int(parts[1])
        elif len(parts) == 3:
            return int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
        else:
            raise ValueError(f"无效的时长格式: {duration_str}")


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def ensure_dir(dir_path: str) -> bool:
        """确保目录存在"""
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception:
            return False
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """生成安全的文件名"""
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = re.sub(r'[\x00-\x1f]', '', filename)
        
        # 限制长度
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename
    
    @staticmethod
    def get_unique_filename(file_path: str) -> str:
        """获取唯一的文件名"""
        path = Path(file_path)
        
        if not path.exists():
            return file_path
        
        counter = 1
        while True:
            new_name = f"{path.stem}_{counter}{path.suffix}"
            new_path = path.parent / new_name
            
            if not new_path.exists():
                return str(new_path)
            
            counter += 1


# 使用示例
if __name__ == "__main__":
    # 测试视频验证
    is_valid, error = VideoValidator.validate_file("test_video.mp4")
    print(f"视频验证: {is_valid}, 错误: {error}")
    
    # 测试文本验证
    is_valid, error = TextValidator.validate_title("测试标题")
    print(f"标题验证: {is_valid}, 错误: {error}")
    
    # 测试哈希生成
    hash_value = HashGenerator.md5_hash("test data")
    print(f"MD5哈希: {hash_value}")
    
    # 测试URL构建
    url = UrlBuilder.build_url("https://api.example.com", "/upload", {"token": "123"})
    print(f"构建的URL: {url}")
    
    # 测试时间工具
    current_time = TimeUtils.format_time()
    print(f"当前时间: {current_time}")
