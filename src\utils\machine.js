import { invoke } from '@tauri-apps/api/core'

/**
 * 机器信息类型定义
 * @typedef {Object} MachineInfo
 * @property {string} mac - MAC地址
 * @property {string} ip_address - IP地址
 * @property {string} machine_name - 机器名称
 * @property {string} os_name - 操作系统名称
 * @property {string} os_version - 操作系统版本
 */

/**
 * 获取机器信息
 * @returns {Promise<MachineInfo>} 机器信息
 */
export async function getMachineInfo() {
  try {
    // 调用Tauri后端命令获取机器信息
    const machineInfo = await invoke('get_machine_info')
    
    console.log('获取到的机器信息:', machineInfo)
    
    return {
      mac: machineInfo.mac || '',
      ip_address: machineInfo.ip_address || '',
      machine_name: machineInfo.machine_name || '',
      os_name: machineInfo.os_name || '',
      os_version: machineInfo.os_version || ''
    }
  } catch (error) {
    console.error('获取机器信息失败:', error)
    
    // 如果获取失败，返回默认值
    return {
      mac: '',
      ip_address: '',
      machine_name: '',
      os_name: '',
      os_version: ''
    }
  }
}

/**
 * 获取网络接口信息（备用方法）
 * @returns {Promise<string>} MAC地址
 */
export async function getMacAddress() {
  try {
    const machineInfo = await getMachineInfo()
    return machineInfo.mac
  } catch (error) {
    console.error('获取MAC地址失败:', error)
    return ''
  }
}

/**
 * 获取本机IP地址（备用方法）
 * @returns {Promise<string>} IP地址
 */
export async function getIpAddress() {
  try {
    const machineInfo = await getMachineInfo()
    return machineInfo.ip_address
  } catch (error) {
    console.error('获取IP地址失败:', error)
    return ''
  }
}

/**
 * 获取机器名称（备用方法）
 * @returns {Promise<string>} 机器名称
 */
export async function getMachineName() {
  try {
    const machineInfo = await getMachineInfo()
    return machineInfo.machine_name
  } catch (error) {
    console.error('获取机器名称失败:', error)
    return ''
  }
}

// 导出类型定义（用于TypeScript）
export const MachineInfo = {}
