import axios from 'axios'
import { ElMessage } from 'element-plus'
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5409'

// 创建 axios 实例
const axiosRequest = axios.create({
  baseURL: BASE_URL, // 根据你的项目实际配置
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
axiosRequest.interceptors.request.use(
  config => {
    // 可在此处添加 token 等操作
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器
axiosRequest.interceptors.response.use(
  response => response.data,
  error => Promise.reject(error)
);

// 响应拦截器
axiosRequest.interceptors.response.use(
  (response) => {
    const data = response
    
    // 根据后端接口规范处理响应
    if (data.code === 200 || data.success) {
      return data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  (error) => {
    console.error('响应错误:', error)
    
    // 处理HTTP错误状态码
    if (error.response) {
      const { status } = error.response
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 可以在这里处理登录跳转
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求地址不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error('网络错误')
      }
    } else {
      ElMessage.error('网络连接失败')
    }
    
    return Promise.reject(error)
  }
)

// 封装常用的请求方法
export const httpAxios = {
  get(url, params) {
    return axiosRequest.get(url, { params })
  },
  
  post(url, data, config = {}) {
    return axiosRequest.post(url, data, config)
  },
  
  put(url, data, config = {}) {
    return axiosRequest.put(url, data, config)
  },
  
  delete(url, params) {
    return axiosRequest.delete(url, { params })
  },
  
  upload(url, formData) {
    return axiosRequest.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export default axiosRequest;