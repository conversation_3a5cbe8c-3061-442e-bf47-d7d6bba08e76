"""
系统工具服务模块
处理系统相关的工具功能，如文件操作、路径管理等
"""

import os
import subprocess
import logging
from pathlib import Path


class SystemService:
    """系统工具服务类"""
    
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
    
    def get_current_directory(self):
        """
        获取当前工作目录的绝对路径
        返回原始格式以保持前端兼容性
        """
        try:
            # 返回基础目录路径，保持与原接口兼容
            return str(self.base_dir.absolute())
        except Exception as e:
            logging.error(f"获取当前目录失败: {str(e)}")
            raise e
    
    def open_file_in_explorer(self, file_path):
        """
        在资源管理器中打开文件位置（仅支持 Windows）
        """
        if not file_path:
            return {
                "success": False,
                "code": 400,
                "msg": "文件路径不能为空",
                "data": None
            }
        
        try:
            # 检查操作系统
            if os.name != 'nt':
                return {
                    "success": False,
                    "code": 400,
                    "msg": "此功能仅支持 Windows 系统",
                    "data": None
                }
            
            # 规范化路径格式
            # 将正斜杠转换为反斜杠，处理路径分隔符混用的问题
            normalized_path = file_path.replace('/', '\\')
            # 获取绝对路径
            absolute_path = os.path.abspath(normalized_path)
            
            logging.info(f"原始路径: {file_path}")
            logging.info(f"规范化路径: {normalized_path}")
            logging.info(f"绝对路径: {absolute_path}")
            
            # 检查文件是否存在
            if not os.path.exists(absolute_path):
                logging.error(f"文件不存在: {absolute_path}")
                return {
                    "success": False,
                    "code": 404,
                    "msg": f"文件不存在: {absolute_path}",
                    "data": None
                }
            
            # 在资源管理器中打开并选中文件
            try:
                logging.info(f"尝试打开文件: {absolute_path}")
                
                # 尝试多种方法来选中文件
                success = False
                
                # 方法1: 使用短路径（8.3格式）来避免特殊字符问题
                try:
                    import win32api
                    short_path = win32api.GetShortPathName(absolute_path)
                    cmd_str = f'explorer /select,"{short_path}"'
                    result = subprocess.run(
                        cmd_str,
                        shell=True,
                        capture_output=True,
                        text=True,
                        encoding='gbk',
                        errors='ignore',
                        timeout=10  # 添加超时
                    )
                    
                    logging.info(f"短路径方法返回码: {result.returncode}")
                    
                    # Explorer 可能返回非零退出码但仍然成功，所以我们假设成功了
                    # 除非有明确的错误信息表明失败
                    if result.stderr and "error" in result.stderr.lower():
                        logging.warning(f"短路径方法有错误信息: {result.stderr}")
                    else:
                        logging.info("使用短路径执行完成（假设成功）")
                        success = True
                        
                except ImportError:
                    logging.info("win32api 不可用，跳过短路径方法")
                except Exception as e:
                    logging.warning(f"短路径方法异常: {e}")
                
                # 方法2: 使用标准的 explorer /select 命令
                if not success:
                    try:
                        # 使用标准格式
                        cmd_str = f'explorer /select,"{absolute_path}"'
                        logging.info(f"尝试标准格式: {cmd_str}")
                        
                        result = subprocess.run(
                            cmd_str,
                            shell=True,
                            capture_output=True,
                            text=True,
                            encoding='gbk',
                            errors='ignore',
                            timeout=10  # 添加超时
                        )
                        
                        logging.info(f"标准格式返回码: {result.returncode}")
                        
                        # 同样假设成功，除非有明确错误
                        if result.stderr and "error" in result.stderr.lower():
                            logging.warning(f"标准格式有错误信息: {result.stderr}")
                        else:
                            logging.info("使用标准格式执行完成（假设成功）")
                            success = True
                                
                    except Exception as e:
                        logging.warning(f"标准格式方法异常: {e}")
                
                # 方法3: 尝试使用PowerShell调用
                if not success:
                    try:
                        # 使用PowerShell的Invoke-Item和Select-Object
                        ps_cmd = f'powershell -Command "& {{Invoke-Item -Path \\"{os.path.dirname(absolute_path)}\\"; Start-Sleep -Milliseconds 500; Add-Type -AssemblyName Microsoft.VisualBasic; [Microsoft.VisualBasic.FileIO.FileSystem]::OpenExplorerAndSelectFile(\\"{absolute_path}\\");}}"'
                        logging.info("尝试使用PowerShell方法")
                        
                        result = subprocess.run(
                            ps_cmd,
                            shell=True,
                            capture_output=True,
                            text=True,
                            encoding='utf-8',
                            errors='ignore'
                        )
                        
                        if result.returncode == 0:
                            logging.info("PowerShell方法成功")
                            success = True
                        else:
                            logging.warning(f"PowerShell方法失败: {result.stderr}")
                    except Exception as e:
                        logging.warning(f"PowerShell方法异常: {e}")
                
                # 方法4: 如果所有选中方法都失败，使用WIN32 API
                if not success:
                    try:
                        import ctypes
                        from ctypes import wintypes
                        
                        # 使用SHOpenFolderAndSelectItems API
                        shell32 = ctypes.windll.shell32
                        ole32 = ctypes.windll.ole32
                        
                        # 初始化COM
                        ole32.CoInitialize(None)
                        
                        # 获取文件的PIDL
                        pidl = shell32.ILCreateFromPathW(absolute_path)
                        if pidl:
                            # 调用SHOpenFolderAndSelectItems
                            result = shell32.SHOpenFolderAndSelectItems(pidl, 0, None, 0)
                            shell32.ILFree(pidl)
                            
                            if result == 0:  # S_OK
                                logging.info("WIN32 API方法成功选中文件")
                                success = True
                            else:
                                logging.warning(f"WIN32 API方法失败，错误代码: {result}")
                        
                        ole32.CoUninitialize()
                        
                    except Exception as e:
                        logging.warning(f"WIN32 API方法异常: {e}")
                
                # 最后的备选方案：只打开目录
                if not success:
                    logging.info("所有选中文件的方法都失败，改为打开目录")
                    try:
                        if os.path.isfile(absolute_path):
                            directory = os.path.dirname(absolute_path)
                            os.startfile(directory)
                            logging.info("使用 os.startfile 成功打开文件所在目录")
                        else:
                            os.startfile(absolute_path)
                            logging.info("使用 os.startfile 成功打开目录")
                    except Exception as startfile_e:
                        logging.error(f"os.startfile 也失败: {startfile_e}")
                        raise subprocess.CalledProcessError(1, 'os.startfile', str(startfile_e))
                    
            except Exception as e:
                logging.error(f"执行explorer命令异常: {e}")
                raise subprocess.CalledProcessError(1, 'explorer', str(e))
            
            return {
                "success": True,
                "code": 200,
                "msg": "已在资源管理器中打开文件位置",
                "data": {"file_path": absolute_path}
            }
            
        except subprocess.CalledProcessError as e:
            logging.error(f"打开资源管理器失败: {e}")
            return {
                "success": False,
                "code": 500,
                "msg": f"打开资源管理器失败: {str(e)}",
                "data": None
            }
        except Exception as e:
            logging.error(f"打开文件位置失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }
    
    def check_system_resources(self):
        """
        检查系统资源状态
        """
        try:
            import psutil
            
            # CPU 使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024**3)
            memory_total_gb = memory.total / (1024**3)
            
            # 磁盘使用情况
            disk = psutil.disk_usage(str(self.base_dir))
            disk_percent = disk.percent
            disk_used_gb = disk.used / (1024**3)
            disk_total_gb = disk.total / (1024**3)
            
            return {
                "success": True,
                "code": 200,
                "msg": "success",
                "data": {
                    "cpu_percent": cpu_percent,
                    "memory": {
                        "percent": memory_percent,
                        "used_gb": round(memory_used_gb, 2),
                        "total_gb": round(memory_total_gb, 2)
                    },
                    "disk": {
                        "percent": disk_percent,
                        "used_gb": round(disk_used_gb, 2),
                        "total_gb": round(disk_total_gb, 2)
                    }
                }
            }
            
        except ImportError:
            # psutil 未安装
            return {
                "success": False,
                "code": 500,
                "msg": "psutil 库未安装，无法获取系统资源信息",
                "data": None
            }
        except Exception as e:
            logging.error(f"检查系统资源失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }
    
    def check_directories(self):
        """
        检查重要目录是否存在
        """
        try:
            directories = {
                "base_dir": self.base_dir,
                "video_dir": self.base_dir / "videoFile",
                "cookies_dir": self.base_dir / "cookiesFile",
                "db_dir": self.base_dir,
                "logs_dir": self.base_dir / "logs"
            }
            
            status = {}
            for name, path in directories.items():
                status[name] = {
                    "path": str(path),
                    "exists": path.exists(),
                    "is_directory": path.is_dir() if path.exists() else False
                }
            
            return {
                "success": True,
                "code": 200,
                "msg": "success",
                "data": status
            }
            
        except Exception as e:
            logging.error(f"检查目录状态失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }
    
    def ensure_directories(self):
        """
        确保重要目录存在，如果不存在则创建
        """
        try:
            directories = [
                self.base_dir / "videoFile",
                self.base_dir / "cookiesFile", 
                self.base_dir / "cookies",
                self.base_dir / "logs",
                self.base_dir / "temp"
            ]
            
            created_dirs = []
            for directory in directories:
                if not directory.exists():
                    directory.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(str(directory))
                    logging.info(f"创建目录: {directory}")
            
            return {
                "success": True,
                "code": 200,
                "msg": "目录检查完成",
                "data": {
                    "created_directories": created_dirs,
                    "total_checked": len(directories)
                }
            }
            
        except Exception as e:
            logging.error(f"创建目录失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }


# 便捷函数供路由调用
def get_current_directory_handler(base_dir):
    """获取当前目录处理函数 - 保持原格式兼容性"""
    try:
        service = SystemService(base_dir)
        directory_path = service.get_current_directory()
        return {
            "code": 200,
            "data": directory_path
        }
    except Exception as e:
        logging.error(f"获取当前目录失败: {str(e)}")
        return {
            "code": 500,
            "message": str(e)
        }


def open_file_in_explorer_handler(base_dir, file_path):
    """在资源管理器中打开文件处理函数"""
    service = SystemService(base_dir)
    return service.open_file_in_explorer(file_path)


def check_system_resources_handler(base_dir):
    """检查系统资源处理函数"""
    service = SystemService(base_dir)
    return service.check_system_resources()


def check_directories_handler(base_dir):
    """检查目录状态处理函数"""
    service = SystemService(base_dir)
    return service.check_directories()


def ensure_directories_handler(base_dir):
    """确保目录存在处理函数"""
    service = SystemService(base_dir)
    return service.ensure_directories()
