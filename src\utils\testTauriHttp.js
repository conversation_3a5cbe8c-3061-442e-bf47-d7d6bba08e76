// 测试 Tauri HTTP 插件功能
import { fetch } from '@tauri-apps/plugin-http'

/**
 * 测试基本的 fetch 功能
 */
export async function testBasicFetch() {
  console.log("=== 测试基本 fetch 功能 ===");
  
  try {
    console.log("1. 检查 fetch 函数是否可用...");
    console.log("fetch 类型:", typeof fetch);
    console.log("fetch 函数:", fetch.toString().substring(0, 100));
    
    console.log("2. 发送简单的 GET 请求...");
    const response = await fetch('https://httpbin.org/get', {
      method: 'GET',
      headers: {
        'User-Agent': 'Tauri-Test/1.0',
        'Accept': 'application/json'
      }
    });
    
    console.log("3. 检查响应对象...");
    console.log("response 类型:", typeof response);
    console.log("response.ok:", response.ok);
    console.log("response.status:", response.status);
    console.log("response.headers:", response.headers);
    console.log("response.text 方法存在:", typeof response.text === 'function');
    
    console.log("4. 尝试读取响应文本...");
    const text = await response.text();
    console.log("响应文本长度:", text.length);
    console.log("响应文本预览:", text.substring(0, 200));
    
    const data = JSON.parse(text);
    console.log("解析后的数据:", data);
    
    return {
      success: true,
      status: response.status,
      dataLength: text.length,
      data: data
    };
    
  } catch (error) {
    console.error("测试失败:", error);
    return {
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
}

/**
 * 测试 POST 请求
 */
export async function testPostRequest() {
  console.log("=== 测试 POST 请求 ===");
  
  try {
    const testData = {
      message: "Hello from Tauri",
      timestamp: Date.now()
    };
    
    const response = await fetch('https://httpbin.org/post', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    console.log("POST 响应状态:", response.status);
    const text = await response.text();
    const data = JSON.parse(text);
    
    console.log("POST 响应数据:", data);
    
    return {
      success: true,
      status: response.status,
      sentData: testData,
      receivedData: data
    };
    
  } catch (error) {
    console.error("POST 测试失败:", error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试错误处理
 */
export async function testErrorHandling() {
  console.log("=== 测试错误处理 ===");
  
  try {
    // 测试 404 错误
    const response = await fetch('https://httpbin.org/status/404', {
      method: 'GET'
    });
    
    console.log("404 响应状态:", response.status);
    console.log("404 响应 ok:", response.ok);
    
    const text = await response.text();
    console.log("404 响应文本:", text);
    
    return {
      success: true,
      status: response.status,
      ok: response.ok,
      text: text
    };
    
  } catch (error) {
    console.error("错误处理测试失败:", error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log("🚀 开始运行 Tauri HTTP 插件测试...");
  
  const results = {
    basicFetch: await testBasicFetch(),
    postRequest: await testPostRequest(),
    errorHandling: await testErrorHandling()
  };
  
  console.log("📊 测试结果汇总:", results);
  
  const allSuccess = Object.values(results).every(result => result.success);
  console.log(allSuccess ? "✅ 所有测试通过!" : "❌ 部分测试失败!");
  
  return results;
}

// 导出单个测试函数
export default {
  testBasicFetch,
  testPostRequest,
  testErrorHandling,
  runAllTests
};
