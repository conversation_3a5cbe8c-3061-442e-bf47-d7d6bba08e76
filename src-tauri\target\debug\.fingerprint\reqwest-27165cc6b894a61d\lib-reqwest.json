{"rustc": 16591470773350601817, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"brotli\", \"charset\", \"cookies\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 18301831581511721250, "deps": [[40386456601120721, "percent_encoding", false, 9389606893816955030], [784494742817713399, "tower_service", false, 1174622840264666335], [1655997011631319712, "webpki_roots", false, 14249991956284279895], [1861048441542724925, "bytes", false, 15734092130910943032], [2357392144306194149, "tokio_util", false, 13744716615631086753], [2517136641825875337, "sync_wrapper", false, 7269100576445307642], [2877347214279964928, "pin_project_lite", false, 10431634156803823253], [3150220818285335163, "url", false, 11966484956960746278], [4920660634395069245, "hyper_util", false, 12904087735913788721], [5070769681332304831, "once_cell", false, 1077203143867369845], [5501164552881223878, "rustls_pki_types", false, 14327559253002116693], [6572616762007422061, "h2", false, 15558621795247858590], [7314894124883917868, "log", false, **********40765158], [7620660491849607393, "futures_core", false, 5227853956270100733], [8298091525883606470, "cookie_store", false, 1831379281013342130], [8324636962323428845, "serde_json", false, 12397612473663235593], [9027827595814673957, "rustls", false, 4727676188276325104], [9538054652646069845, "tokio", false, 2051528350863639385], [9788322052190318600, "async_compression", false, 806647265202730480], [10036721834787556336, "http_body_util", false, 4692412623965365817], [10229185211513642314, "mime", false, 17767382888373566784], [10629569228670356391, "futures_util", false, 5529326595165766766], [10967960060725374459, "serde", false, 15592400294397051589], [11723101755950938843, "ipnet", false, 7895437454220403529], [12940741154263179976, "hyper_rustls", false, 7071227445908657423], [13077212702700853852, "base64", false, 7756246574865547920], [14084095096285906100, "http_body", false, 2079723812076525467], [14564311161534545801, "encoding_rs", false, 17087654108008187760], [15032952994102373905, "rustls_pemfile", false, 7606374064629759083], [16542808166767769916, "serde_urlencoded", false, 9614522372827370614], [16727543399706004146, "cookie_crate", false, 16853601063074676329], [17301905655554462353, "tokio_rustls", false, 18352157032600750705], [17860019243264344128, "http", false, 9568133891094124003], [17936921378057108349, "hyper", false, 10926049433159936393], [18222702447897302889, "windows_registry", false, 5061117166087376844]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-27165cc6b894a61d\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}