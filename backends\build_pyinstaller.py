#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller 打包脚本（备选方案）
"""

import os
import sys
import subprocess
from pathlib import Path

def build_with_pyinstaller():
    """使用 PyInstaller 打包"""
    
    # 确保在正确的目录
    script_dir = Path(__file__).parent.absolute()
    os.chdir(script_dir)
    
    print(f"当前工作目录: {os.getcwd()}")
    
    # PyInstaller 命令
    pyinstaller_cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--distpath=dist_pyinstaller",
        "--workpath=build_pyinstaller", 
        "--specpath=.",
        "--name=xiaochao-server",
        
        # 包含数据文件
        "--add-data=cookies;cookies",
        "--add-data=cookiesFile;cookiesFile",
        "--add-data=videoFile;videoFile", 
        "--add-data=logs;logs",
        "--add-data=ffmpeg;ffmpeg",
        "--add-data=utils;utils",
        "--add-data=database.db;.",
        
        # 隐藏导入
        "--hidden-import=playwright",
        "--hidden-import=playwright._impl",
        "--hidden-import=flask",
        "--hidden-import=flask_cors",
        "--hidden-import=requests",
        "--hidden-import=loguru",
        
        # 其他选项
        "--console",  # 保留控制台窗口
        "--clean",    # 清理临时文件
        
        "main.py"
    ]
    
    print("开始 PyInstaller 打包...")
    print("命令:", " ".join(pyinstaller_cmd))
    print("-" * 50)
    
    try:
        result = subprocess.run(
            pyinstaller_cmd,
            cwd=script_dir,
            capture_output=False,
            text=True
        )
        
        if result.returncode == 0:
            print("\n PyInstaller 打包成功!")
            
            # 检查输出文件
            dist_dir = script_dir / "dist_pyinstaller"
            if dist_dir.exists():
                print(f"输出目录: {dist_dir}")
                for item in dist_dir.iterdir():
                    print(f"  - {item.name}")
            
            return True
        else:
            print(f"\n PyInstaller 打包失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f" 打包过程中发生错误: {e}")
        return False

def install_pyinstaller():
    """安装 PyInstaller"""
    try:
        import PyInstaller
        print(" PyInstaller 已安装")
        return True
    except ImportError:
        print("📦 正在安装 PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print(" PyInstaller 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(" PyInstaller 安装失败")
            return False

if __name__ == "__main__":
    print("=" * 50)
    print("PyInstaller 打包脚本（备选方案）")
    print("=" * 50)
    
    # 检查并安装 PyInstaller
    if not install_pyinstaller():
        sys.exit(1)
    
    # 开始打包
    success = build_with_pyinstaller()
    
    if success:
        print("\n PyInstaller 打包完成!")
    else:
        print("\n PyInstaller 打包失败")
        sys.exit(1)
