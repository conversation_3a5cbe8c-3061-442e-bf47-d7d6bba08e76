{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 12687594469746301899, "profile": 3316208278650011218, "path": 4942398508502643691, "deps": [[1322478694103194923, "build_script_build", false, 11392127864548163429], [2069998946850810971, "tauri", false, 6217613720679260382], [2253952315205409758, "tauri_plugin_shell", false, 18293664173408919543], [3834743577069889284, "tauri_plugin_dialog", false, 9008268589997148257], [5236433071915784494, "sha2", false, 4757170262606056133], [8324636962323428845, "serde_json", false, 7393924985922554751], [9451456094439810778, "regex", false, 3491262781102691180], [10967960060725374459, "serde", false, 14771690952189957965], [12316149723851658110, "command_group", false, 4481664513630527362], [13890802266741835355, "tauri_plugin_fs", false, 13858027519614704400], [15441187897486245138, "tauri_plugin_http", false, 10804001727369527706], [17121285809968213651, "md5", false, 1052830708535802158], [18057500336552735601, "sysinfo", false, 4063723584716310849]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\app-535475a88e4acb00\\dep-test-bin-app", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}