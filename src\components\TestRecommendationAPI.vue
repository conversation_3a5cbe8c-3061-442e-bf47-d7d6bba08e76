<template>
  <div class="test-recommendation-api">
    <el-card class="test-card">
      <template #header>
        <span>推荐 API 测试 - 使用 test-7-31.py 逻辑和 Rust Utils</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="Photo ID">
          <el-input v-model="testForm.photoId" placeholder="5216857383469532746" />
        </el-form-item>
        
        <el-form-item label="User ID">
          <el-input v-model="testForm.userId" placeholder="4808775036" />
        </el-form-item>
        
        <el-form-item label="Page Source">
          <el-input v-model="testForm.pageSource" placeholder="0" />
        </el-form-item>
        
        <el-form-item label="Display Type">
          <el-input v-model="testForm.displayType" placeholder="3" />
        </el-form-item>
        
        <el-form-item label="Client Key">
          <el-input v-model="testForm.clientKey" placeholder="3c2cd3f3" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testAPI" :loading="loading.api">
            测试推荐 API
          </el-button>
          <el-button type="success" @click="validateUtils" :loading="loading.utils">
            验证 Rust Utils
          </el-button>
          <el-button @click="clearResults">
            清空结果
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 验证结果 -->
    <el-card class="result-card" v-if="utilsResult">
      <template #header>
        <span>Rust Utils 验证结果</span>
      </template>
      
      <div v-if="utilsResult.success">
        <el-alert title="验证成功" type="success" show-icon />
        <div class="result-item">
          <strong>MD5 签名:</strong> {{ utilsResult.results.sig }}
        </div>
        <div class="result-item">
          <strong>SHA256 令牌:</strong> {{ utilsResult.results.token }}
        </div>
        <div class="result-item">
          <strong>提取结果:</strong> {{ utilsResult.results.extracted }}
        </div>
        <div class="result-item">
          <strong>Java 可用性:</strong> 
          <el-tag :type="utilsResult.results.javaAvailable ? 'success' : 'danger'">
            {{ utilsResult.results.javaAvailable ? '可用' : '不可用' }}
          </el-tag>
        </div>
      </div>
      <div v-else>
        <el-alert :title="utilsResult.error" type="error" show-icon />
      </div>
    </el-card>

    <!-- API 测试结果 -->
    <el-card class="result-card" v-if="apiResult">
      <template #header>
        <span>推荐 API 测试结果</span>
      </template>
      
      <div v-if="apiResult.success">
        <el-alert title="API 调用成功" type="success" show-icon />
        
        <el-tabs>
          <el-tab-pane label="响应数据" name="response">
            <div class="result-item">
              <strong>HTTP 状态:</strong> {{ apiResult.status }}
            </div>
            <div class="result-item">
              <strong>响应数据:</strong>
              <pre class="json-display">{{ JSON.stringify(apiResult.data, null, 2) }}</pre>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="生成的签名" name="signatures">
            <div class="result-item">
              <strong>MD5 签名 (sig):</strong> {{ apiResult.signatures.sig }}
            </div>
            <div class="result-item">
              <strong>Sig3 签名:</strong> {{ apiResult.signatures.sig3 }}
            </div>
            <div class="result-item">
              <strong>Token:</strong> {{ apiResult.signatures.token }}
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="请求信息" name="request">
            <div class="result-item">
              <strong>请求 URL:</strong>
              <div class="url-display">{{ apiResult.requestInfo.url }}</div>
            </div>
            <div class="result-item">
              <strong>请求体:</strong>
              <pre class="json-display">{{ apiResult.requestInfo.body }}</pre>
            </div>
            <div class="result-item">
              <strong>请求头:</strong>
              <pre class="json-display">{{ JSON.stringify(apiResult.requestInfo.headers, null, 2) }}</pre>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div v-else>
        <el-alert :title="apiResult.error" type="error" show-icon />
        <div v-if="apiResult.stack" class="error-stack">
          <strong>错误堆栈:</strong>
          <pre>{{ apiResult.stack }}</pre>
        </div>
      </div>
    </el-card>

    <!-- 实时日志 -->
    <el-card class="log-card" v-if="logs.length > 0">
      <template #header>
        <span>实时日志</span>
        <el-button size="small" @click="clearLogs" style="float: right;">
          清空日志
        </el-button>
      </template>
      
      <div class="log-container">
        <div 
          v-for="(log, index) in logs" 
          :key="index" 
          :class="['log-item', `log-${log.level}`]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { testRecommendationAPIStandalone, validateRustUtils } from '@/utils/testRecommendationAPI'

const testForm = reactive({
  photoId: '5216857383469532746',
  userId: '4808775036',
  pageSource: '0',
  displayType: '3',
  clientKey: '3c2cd3f3'
})

const loading = reactive({
  api: false,
  utils: false
})

const apiResult = ref(null)
const utilsResult = ref(null)
const logs = ref([])

// 添加日志
const addLog = (level, message) => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    level,
    message
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value.shift()
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 清空结果
const clearResults = () => {
  apiResult.value = null
  utilsResult.value = null
  clearLogs()
}

// 测试推荐 API
const testAPI = async () => {
  loading.api = true
  addLog('info', '开始测试推荐 API...')
  
  try {
    const testData = {
      photoId: testForm.photoId,
      userId: testForm.userId,
      pageSource: testForm.pageSource,
      displayType: testForm.displayType,
      client_key: testForm.clientKey
    }
    
    addLog('info', `测试数据: ${JSON.stringify(testData)}`)
    
    const result = await testRecommendationAPIStandalone(testData)
    apiResult.value = result
    
    if (result.success) {
      addLog('success', 'API 测试成功')
      ElMessage.success('推荐 API 测试成功')
    } else {
      addLog('error', `API 测试失败: ${result.error}`)
      ElMessage.error(`API 测试失败: ${result.error}`)
    }
  } catch (error) {
    addLog('error', `API 测试异常: ${error.message}`)
    ElMessage.error(`API 测试异常: ${error.message}`)
    apiResult.value = {
      success: false,
      error: error.message,
      stack: error.stack
    }
  } finally {
    loading.api = false
  }
}

// 验证 Rust Utils
const validateUtils = async () => {
  loading.utils = true
  addLog('info', '开始验证 Rust Utils...')
  
  try {
    const result = await validateRustUtils()
    utilsResult.value = result
    
    if (result.success) {
      addLog('success', 'Rust Utils 验证成功')
      ElMessage.success('Rust Utils 验证成功')
    } else {
      addLog('error', `Rust Utils 验证失败: ${result.error}`)
      ElMessage.error(`Rust Utils 验证失败: ${result.error}`)
    }
  } catch (error) {
    addLog('error', `Rust Utils 验证异常: ${error.message}`)
    ElMessage.error(`Rust Utils 验证异常: ${error.message}`)
    utilsResult.value = {
      success: false,
      error: error.message
    }
  } finally {
    loading.utils = false
  }
}
</script>

<style scoped>
.test-recommendation-api {
  padding: 20px;
}

.test-card,
.result-card,
.log-card {
  margin-bottom: 20px;
}

.result-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.json-display {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.url-display {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  word-break: break-all;
}

.error-stack {
  margin-top: 15px;
}

.error-stack pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.log-item {
  margin-bottom: 5px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-time {
  color: #6c757d;
  margin-right: 10px;
}

.log-level {
  font-weight: bold;
  margin-right: 10px;
  min-width: 60px;
  display: inline-block;
}

.log-info .log-level {
  color: #17a2b8;
}

.log-success .log-level {
  color: #28a745;
}

.log-error .log-level {
  color: #dc3545;
}

.log-message {
  color: #495057;
}
</style>
