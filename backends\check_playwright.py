#!/usr/bin/env python3
"""
Playwright环境检查脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_playwright_installation():
    """检查Playwright安装"""
    print("检查Playwright安装...")
    
    try:
        import playwright
        print(f"✓ Playwright已安装，版本: {playwright.__version__}")
        print(f"✓ Playwright路径: {playwright.__file__}")
        return True
    except ImportError:
        print("✗ Playwright未安装")
        return False

def check_playwright_browsers():
    """检查Playwright浏览器"""
    print("\n检查Playwright浏览器...")
    
    # 可能的浏览器位置
    possible_locations = [
        Path.home() / "AppData" / "Local" / "ms-playwright",
        Path(sys.executable).parent.parent / "Lib" / "site-packages" / "playwright" / "driver" / "package" / ".local-browsers",
        Path(sys.prefix) / "Lib" / "site-packages" / "playwright" / "driver" / "package" / ".local-browsers",
    ]
    
    found_browsers = []
    
    for location in possible_locations:
        if location.exists():
            print(f"✓ 找到浏览器目录: {location}")
            try:
                browsers = list(location.iterdir())
                for browser in browsers:
                    if browser.is_dir():
                        print(f"  - {browser.name}")
                        found_browsers.append(browser)
            except Exception as e:
                print(f"  错误: {e}")
    
    if not found_browsers:
        print("✗ 未找到Playwright浏览器")
        print("请运行: playwright install")
        return False
    
    return True

def check_system_browsers():
    """检查系统浏览器"""
    print("\n检查系统浏览器...")
    
    browsers = [
        ("Chrome", r"C:\Program Files\Google\Chrome\Application\chrome.exe"),
        ("Chrome (x86)", r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"),
        ("Edge", r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"),
        ("Edge", r"C:\Program Files\Microsoft\Edge\Application\msedge.exe"),
    ]
    
    found_system_browsers = []
    
    for name, path in browsers:
        if os.path.exists(path):
            print(f"✓ 找到{name}: {path}")
            found_system_browsers.append((name, path))
        else:
            print(f"✗ 未找到{name}: {path}")
    
    return found_system_browsers

def test_playwright_launch():
    """测试Playwright启动"""
    print("\n测试Playwright启动...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # 尝试启动浏览器
            browser = p.chromium.launch(headless=True)
            print("✓ Playwright浏览器启动成功")
            
            # 创建页面
            page = browser.new_page()
            print("✓ 页面创建成功")
            
            # 访问测试页面
            page.goto("https://www.baidu.com")
            title = page.title()
            print(f"✓ 页面访问成功，标题: {title}")
            
            browser.close()
            print("✓ 浏览器关闭成功")
            
        return True
        
    except Exception as e:
        print(f"✗ Playwright测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    print("\n检查Python依赖...")
    
    required_packages = [
        "playwright",
        "asyncio",
        "pathlib",
        "sqlite3",
        "logging"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖: {', '.join(missing_packages)}")
        return False
    
    return True

def install_playwright_browsers():
    """安装Playwright浏览器"""
    print("\n安装Playwright浏览器...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "playwright", "install", "chromium"
        ], check=True, capture_output=True, text=True)
        
        print("✓ Playwright浏览器安装成功")
        print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print("✗ Playwright浏览器安装失败")
        print(e.stderr)
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Playwright环境检查")
    print("=" * 60)
    
    all_checks_passed = True
    
    # 检查步骤
    checks = [
        ("Playwright安装", check_playwright_installation),
        ("Python依赖", check_dependencies),
        ("Playwright浏览器", check_playwright_browsers),
        ("系统浏览器", check_system_browsers),
    ]
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        if not check_func():
            all_checks_passed = False
    
    # 如果基础检查通过，进行功能测试
    if all_checks_passed:
        print(f"\n{'='*20} 功能测试 {'='*20}")
        test_playwright_launch()
    
    # 总结
    print("\n" + "="*60)
    if all_checks_passed:
        print("✓ 所有检查通过，Playwright环境正常")
    else:
        print("✗ 部分检查失败，请解决上述问题")
        print("\n建议解决方案:")
        print("1. 安装Playwright: pip install playwright")
        print("2. 安装浏览器: playwright install")
        print("3. 或安装系统Chrome浏览器")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
