#!/usr/bin/env python3
"""
调试数据库连接问题
"""

import os
import sys
import sqlite3
from pathlib import Path

def debug_database():
    """调试数据库连接"""
    print("=" * 50)
    print("数据库连接调试")
    print("=" * 50)
    
    # 获取当前工作目录
    cwd = os.getcwd()
    print(f"当前工作目录: {cwd}")
    
    # 检查是否是打包环境
    is_frozen = getattr(sys, 'frozen', False)
    print(f"是否打包环境: {is_frozen}")
    
    if is_frozen:
        executable_path = os.path.dirname(sys.executable)
        base_dir = Path(executable_path)
    else:
        base_dir = Path(cwd)
    
    print(f"BASE_DIR: {base_dir}")
    
    # 检查数据库文件
    db_path = base_dir / "database.db"
    print(f"数据库路径: {db_path}")
    print(f"数据库文件存在: {db_path.exists()}")
    
    if db_path.exists():
        print(f"数据库文件大小: {db_path.stat().st_size} bytes")
        
        # 尝试连接数据库
        try:
            with sqlite3.connect(str(db_path)) as conn:
                cursor = conn.cursor()
                
                # 检查表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                print(f"数据库表: {[table[0] for table in tables]}")
                
                # 检查 file_records 表
                if ('file_records',) in tables:
                    cursor.execute("SELECT COUNT(*) FROM file_records;")
                    count = cursor.fetchone()[0]
                    print(f"file_records 表记录数: {count}")
                    
                    # 获取表结构
                    cursor.execute("PRAGMA table_info(file_records);")
                    columns = cursor.fetchall()
                    print(f"file_records 表结构: {[col[1] for col in columns]}")
                    
                    # 尝试查询
                    cursor.execute("SELECT * FROM file_records LIMIT 3;")
                    rows = cursor.fetchall()
                    print(f"前3条记录: {rows}")
                else:
                    print("❌ file_records 表不存在")
                    
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
    else:
        print("❌ 数据库文件不存在")
        
        # 检查其他可能的位置
        possible_paths = [
            Path(cwd) / "database.db",
            Path(cwd) / "_internal" / "database.db",
            Path(cwd).parent / "database.db"
        ]
        
        print("\n检查其他可能的数据库位置:")
        for path in possible_paths:
            print(f"  {path}: {path.exists()}")

if __name__ == "__main__":
    debug_database()
