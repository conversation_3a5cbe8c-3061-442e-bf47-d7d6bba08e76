{"$schema": "gen/schemas/desktop-schema.json", "build": {"beforeBuildCommand": "pnpm build", "beforeDevCommand": "pnpm dev", "frontendDist": "../dist", "devUrl": "http://localhost:1420"}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "shortDescription": "", "longDescription": "", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}, "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": "-"}, "linux": {"deb": {"depends": []}}}, "productName": "xiaochao-media", "mainBinaryName": "xiaochao-media", "identifier": "com.xiaochaomedia.obrew", "plugins": {"shell": {"open": true}}, "app": {"windows": [{"fullscreen": false, "height": 950, "resizable": true, "title": "小超媒体", "width": 1300}], "security": {"csp": null, "assetProtocol": {"enable": true, "scope": {"requireLiteralLeadingDot": false, "allow": ["**/*"]}}}}}