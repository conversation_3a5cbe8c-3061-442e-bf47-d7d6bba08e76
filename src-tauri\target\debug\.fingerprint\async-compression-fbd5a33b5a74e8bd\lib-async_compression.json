{"rustc": 16591470773350601817, "features": "[\"brotli\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 15657897354478470176, "path": 13553764646249236501, "deps": [[2877347214279964928, "pin_project_lite", false, 10431634156803823253], [3129130049864710036, "memchr", false, 3283004761576330561], [7620660491849607393, "futures_core", false, 5227853956270100733], [9538054652646069845, "tokio", false, 2051528350863639385], [9556762810601084293, "brotli", false, 12455817229971204329]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-compression-fbd5a33b5a74e8bd\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}