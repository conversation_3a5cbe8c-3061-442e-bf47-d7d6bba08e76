{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"devtools\", \"http-range\", \"objc-exception\", \"protocol-asset\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webview-data-url\", \"wry\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 5135290845315897407, "deps": [[3872875809809309661, "tauri_build", false, 11888608302971047865], [7801942656278641025, "tauri_utils", false, 4641139828269746955], [13077543566650298139, "heck", false, 8091741224224843662]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-70c792ca5c6922be\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}