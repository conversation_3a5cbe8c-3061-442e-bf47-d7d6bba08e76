<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fetch 调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button-group {
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .success {
            background: #28a745;
        }
        .danger {
            background: #dc3545;
        }
        .warning {
            background: #ffc107;
            color: #212529;
        }
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-info { color: #17a2b8; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .result-container {
            background: #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }
        pre {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Tauri Fetch 调试工具</h1>
        <p>这个页面可以帮助诊断 Tauri fetch 的问题，特别是 response.text() 中断的问题。</p>
        
        <div class="button-group">
            <button onclick="testSimpleFetch()" id="btn-simple">简单 Fetch 测试</button>
            <button onclick="testAuthPattern()" id="btn-auth">AuthRequest 模式测试</button>
            <button onclick="testResponseMethods()" id="btn-methods">响应方法测试</button>
            <button onclick="clearLogs()" class="warning">清空日志</button>
        </div>
        
        <div class="button-group">
            <button onclick="runAllTests()" class="success" id="btn-all">运行所有测试</button>
            <button onclick="testRealAuthRequest()" class="danger" id="btn-real">测试真实 AuthRequest</button>
        </div>
    </div>

    <div class="container">
        <h2>📋 实时日志</h2>
        <div id="log-container" class="log-container">
            <div class="log-entry log-info">等待测试开始...</div>
        </div>
    </div>

    <div class="container" id="result-container" style="display: none;">
        <h2>📊 测试结果</h2>
        <div id="result-content" class="result-container">
        </div>
    </div>

    <script>
        // 日志函数
        function addLog(level, message) {
            const container = document.getElementById('log-container');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${level}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('log-container').innerHTML = '<div class="log-entry log-info">日志已清空</div>';
            document.getElementById('result-container').style.display = 'none';
        }

        function showResult(result) {
            const container = document.getElementById('result-container');
            const content = document.getElementById('result-content');
            content.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            container.style.display = 'block';
        }

        function setButtonLoading(buttonId, loading) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.disabled = loading;
                if (loading) {
                    button.textContent = button.textContent + ' (运行中...)';
                } else {
                    button.textContent = button.textContent.replace(' (运行中...)', '');
                }
            }
        }

        // 检查是否在 Tauri 环境中
        function checkTauriEnvironment() {
            if (typeof window.__TAURI__ === 'undefined') {
                addLog('error', '❌ 不在 Tauri 环境中，无法测试 Tauri fetch');
                return false;
            }
            addLog('success', '✅ 检测到 Tauri 环境');
            return true;
        }

        // 简单 fetch 测试
        async function testSimpleFetch() {
            if (!checkTauriEnvironment()) return;
            
            setButtonLoading('btn-simple', true);
            addLog('info', '🚀 开始简单 fetch 测试...');
            
            try {
                // 动态导入 Tauri fetch
                const { fetch } = await import('@tauri-apps/plugin-http');
                addLog('success', '✅ Tauri fetch 导入成功');
                
                addLog('info', '📡 发送 GET 请求到 httpbin.org...');
                const response = await fetch('https://httpbin.org/get', {
                    method: 'GET',
                    headers: {
                        'User-Agent': 'Tauri-Debug/1.0'
                    }
                });
                
                addLog('success', `✅ 请求成功，状态: ${response.status}`);
                addLog('info', '📖 开始读取响应体...');
                
                const text = await response.text();
                addLog('success', `✅ 响应体读取成功，长度: ${text.length}`);
                
                const result = {
                    success: true,
                    status: response.status,
                    textLength: text.length,
                    preview: text.substring(0, 200)
                };
                
                showResult(result);
                addLog('success', '🎉 简单 fetch 测试完成');
                
            } catch (error) {
                addLog('error', `❌ 简单 fetch 测试失败: ${error.message}`);
                showResult({
                    success: false,
                    error: error.message,
                    stack: error.stack
                });
            } finally {
                setButtonLoading('btn-simple', false);
            }
        }

        // AuthRequest 模式测试
        async function testAuthPattern() {
            if (!checkTauriEnvironment()) return;
            
            setButtonLoading('btn-auth', true);
            addLog('info', '🚀 开始 AuthRequest 模式测试...');
            
            try {
                const { fetch } = await import('@tauri-apps/plugin-http');
                
                const url = 'https://httpbin.org/post';
                const headers = {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': 'Bearer test-token'
                };
                const data = {
                    username: 'test',
                    password: 'test123',
                    timestamp: Date.now()
                };
                
                addLog('info', `📡 发送 POST 请求到 ${url}...`);
                addLog('info', `📋 请求头: ${JSON.stringify(headers)}`);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers,
                    body: JSON.stringify(data)
                });
                
                addLog('success', `✅ 请求成功，状态: ${response.status}`);
                addLog('info', '📖 开始读取响应体 (关键步骤)...');
                
                // 这里是 authRequest.js 中断的地方
                const text = await response.text();
                addLog('success', `✅ 响应体读取成功，长度: ${text.length}`);
                
                let jsonData = {};
                try {
                    jsonData = JSON.parse(text);
                    addLog('success', '✅ JSON 解析成功');
                } catch (parseError) {
                    addLog('warning', `⚠️ JSON 解析失败: ${parseError.message}`);
                }
                
                const result = {
                    success: true,
                    status: response.status,
                    textLength: text.length,
                    data: jsonData
                };
                
                showResult(result);
                addLog('success', '🎉 AuthRequest 模式测试完成');
                
            } catch (error) {
                addLog('error', `❌ AuthRequest 模式测试失败: ${error.message}`);
                showResult({
                    success: false,
                    error: error.message,
                    stack: error.stack
                });
            } finally {
                setButtonLoading('btn-auth', false);
            }
        }

        // 响应方法测试
        async function testResponseMethods() {
            if (!checkTauriEnvironment()) return;
            
            setButtonLoading('btn-methods', true);
            addLog('info', '🚀 开始响应方法测试...');
            
            try {
                const { fetch } = await import('@tauri-apps/plugin-http');
                
                addLog('info', '📡 发送请求到 httpbin.org/json...');
                const response = await fetch('https://httpbin.org/json');
                
                const results = {};
                
                // 测试 text() 方法
                try {
                    addLog('info', '📖 测试 response.text()...');
                    const response1 = response.clone();
                    const text = await response1.text();
                    addLog('success', `✅ text() 成功，长度: ${text.length}`);
                    results.text = { success: true, length: text.length };
                } catch (error) {
                    addLog('error', `❌ text() 失败: ${error.message}`);
                    results.text = { success: false, error: error.message };
                }
                
                // 测试 json() 方法
                try {
                    addLog('info', '📖 测试 response.json()...');
                    const response2 = response.clone();
                    const json = await response2.json();
                    addLog('success', '✅ json() 成功');
                    results.json = { success: true, data: json };
                } catch (error) {
                    addLog('error', `❌ json() 失败: ${error.message}`);
                    results.json = { success: false, error: error.message };
                }
                
                showResult(results);
                addLog('success', '🎉 响应方法测试完成');
                
            } catch (error) {
                addLog('error', `❌ 响应方法测试失败: ${error.message}`);
                showResult({
                    success: false,
                    error: error.message
                });
            } finally {
                setButtonLoading('btn-methods', false);
            }
        }

        // 运行所有测试
        async function runAllTests() {
            setButtonLoading('btn-all', true);
            addLog('info', '🚀 开始运行所有测试...');
            
            try {
                await testSimpleFetch();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testAuthPattern();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testResponseMethods();
                
                addLog('success', '🎉 所有测试完成');
            } catch (error) {
                addLog('error', `❌ 运行所有测试失败: ${error.message}`);
            } finally {
                setButtonLoading('btn-all', false);
            }
        }

        // 测试真实的 authRequest
        async function testRealAuthRequest() {
            if (!checkTauriEnvironment()) return;
            
            setButtonLoading('btn-real', true);
            addLog('info', '🚀 开始测试真实 authRequest...');
            
            try {
                // 动态导入 authRequest
                const authRequestModule = await import('./src/utils/authRequest.js');
                const { authHttp } = authRequestModule;
                
                addLog('success', '✅ authRequest 模块导入成功');
                addLog('info', '📡 调用 authHttp.post...');
                
                // 这里会使用真实的 authRequest 逻辑
                const result = await authHttp.post('/test', {
                    test: 'data',
                    timestamp: Date.now()
                });
                
                addLog('success', '✅ authRequest 调用成功');
                showResult(result);
                
            } catch (error) {
                addLog('error', `❌ 真实 authRequest 测试失败: ${error.message}`);
                addLog('info', '这可能是正常的，因为测试服务器可能不存在');
                showResult({
                    success: false,
                    error: error.message,
                    note: '这可能是正常的，因为测试服务器可能不存在'
                });
            } finally {
                setButtonLoading('btn-real', false);
            }
        }

        // 页面加载时的初始化
        window.addEventListener('load', () => {
            addLog('info', '📄 页面加载完成');
            addLog('info', '💡 点击按钮开始测试 Tauri fetch 功能');
            
            if (checkTauriEnvironment()) {
                addLog('info', '🔧 建议先运行"简单 Fetch 测试"来验证基本功能');
            }
        });
    </script>
</body>
</html>
