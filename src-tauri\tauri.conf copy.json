{"$schema": "gen/schemas/desktop-schema.json", "build": {"beforeBuildCommand": "pnpm build", "beforeDevCommand": "pnpm dev", "frontendDist": "../dist", "devUrl": "http://localhost:1420"}, "bundle": {"active": true, "category": "DeveloperTool", "copyright": "", "targets": "all", "externalBin": ["bin/api/main"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "shortDescription": "", "longDescription": "", "resources": ["bin/api/ms-playwright", "bin/api/ffmpeg", "bin/api/database.db"], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}, "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": "-"}, "linux": {"deb": {"depends": []}}}, "productName": "tauri-python-sidecar", "mainBinaryName": "tauri-python-sidecar", "identifier": "com.openbrewai.obrew", "plugins": {}, "app": {"windows": [{"fullscreen": false, "height": 950, "resizable": true, "title": "tauri-python-sidecar", "width": 1200, "useHttpsScheme": true}], "security": {"csp": null}}}