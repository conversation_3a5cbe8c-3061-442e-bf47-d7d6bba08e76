{"rustc": 16591470773350601817, "features": "[\"once\"]", "declared_features": "[\"barrier\", \"default\", \"fair_mutex\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"portable-atomic\", \"portable_atomic\", \"rwlock\", \"spin_mutex\", \"std\", \"ticket_mutex\", \"use_ticket_mutex\"]", "target": 4260413527236709406, "profile": 15657897354478470176, "path": 17243383736694844962, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\spin-b3458e900467b9d0\\dep-lib-spin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}