<template>
  <div class="fetch-debugger">
    <el-card class="debug-card">
      <template #header>
        <span><PERSON><PERSON> 调试工具</span>
      </template>
      
      <el-space direction="vertical" style="width: 100%">
        <el-button-group>
          <el-button type="primary" @click="runSingleTest('basic')" :loading="loading.basic">
            基本 GET 测试
          </el-button>
          <el-button type="success" @click="runSingleTest('post')" :loading="loading.post">
            POST 测试
          </el-button>
          <el-button type="warning" @click="runSingleTest('timeout')" :loading="loading.timeout">
            超时测试
          </el-button>
          <el-button type="info" @click="runSingleTest('errorCodes')" :loading="loading.errorCodes">
            错误码测试
          </el-button>
        </el-button-group>
        
        <el-button-group>
          <el-button type="danger" @click="runSingleTest('authPattern')" :loading="loading.authPattern">
            AuthRequest 模式测试
          </el-button>
          <el-button @click="checkHttpInfo" :loading="loading.info">
            检查 HTTP 插件信息
          </el-button>
          <el-button type="primary" @click="runAllTests" :loading="loading.all">
            运行所有测试
          </el-button>
        </el-button-group>
        
        <el-button @click="clearResults">清空结果</el-button>
      </el-space>
    </el-card>

    <!-- HTTP 插件信息 -->
    <el-card class="result-card" v-if="httpInfo">
      <template #header>
        <span>HTTP 插件信息</span>
      </template>
      
      <div class="info-grid">
        <div class="info-item">
          <strong>Fetch 函数:</strong>
          <el-tag :type="httpInfo.fetchAvailable ? 'success' : 'danger'">
            {{ httpInfo.fetchAvailable ? '可用' : '不可用' }}
          </el-tag>
        </div>
        <div class="info-item">
          <strong>Response 对象:</strong>
          <el-tag :type="httpInfo.responseAvailable ? 'success' : 'danger'">
            {{ httpInfo.responseAvailable ? '可用' : '不可用' }}
          </el-tag>
        </div>
        <div class="info-item">
          <strong>Headers 对象:</strong>
          <el-tag :type="httpInfo.headersAvailable ? 'success' : 'danger'">
            {{ httpInfo.headersAvailable ? '可用' : '不可用' }}
          </el-tag>
        </div>
      </div>
      
      <div v-if="httpInfo.error" class="error-info">
        <el-alert :title="httpInfo.error" type="error" show-icon />
      </div>
    </el-card>

    <!-- 测试结果 -->
    <el-card class="result-card" v-if="testResults && Object.keys(testResults).length > 0">
      <template #header>
        <span>测试结果</span>
      </template>
      
      <el-tabs>
        <el-tab-pane 
          v-for="(result, testName) in testResults" 
          :key="testName"
          :label="getTestLabel(testName)"
          :name="testName"
        >
          <div v-if="result">
            <div v-if="result.success" class="success-result">
              <el-alert title="测试成功" type="success" show-icon />
              <div class="result-details">
                <pre>{{ JSON.stringify(result, null, 2) }}</pre>
              </div>
            </div>
            <div v-else class="error-result">
              <el-alert :title="result.error || '测试失败'" type="error" show-icon />
              <div v-if="result.stack" class="error-stack">
                <strong>错误堆栈:</strong>
                <pre>{{ result.stack }}</pre>
              </div>
              <div v-if="result.details" class="error-details">
                <strong>详细信息:</strong>
                <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
              </div>
            </div>
          </div>
          <div v-else>
            <el-alert title="测试未运行" type="info" show-icon />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 实时日志 -->
    <el-card class="log-card" v-if="logs.length > 0">
      <template #header>
        <span>实时日志</span>
        <el-button size="small" @click="clearLogs" style="float: right;">
          清空日志
        </el-button>
      </template>
      
      <div class="log-container">
        <div 
          v-for="(log, index) in logs" 
          :key="index" 
          :class="['log-item', `log-${log.level}`]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  testBasicFetch,
  testPostFetch,
  testFetchTimeout,
  testErrorStatusCodes,
  testAuthRequestPattern,
  runAllFetchTests,
  checkTauriHttpInfo
} from '@/utils/debugFetch'

const loading = reactive({
  basic: false,
  post: false,
  timeout: false,
  errorCodes: false,
  authPattern: false,
  info: false,
  all: false
})

const testResults = ref({})
const httpInfo = ref(null)
const logs = ref([])

// 添加日志
const addLog = (level, message) => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    level,
    message
  })
  
  // 限制日志数量
  if (logs.value.length > 200) {
    logs.value.shift()
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 清空结果
const clearResults = () => {
  testResults.value = {}
  httpInfo.value = null
  clearLogs()
}

// 获取测试标签
const getTestLabel = (testName) => {
  const labels = {
    basic: '基本 GET',
    post: 'POST 请求',
    timeout: '超时测试',
    errorCodes: '错误码测试',
    authPattern: 'AuthRequest 模式'
  }
  return labels[testName] || testName
}

// 运行单个测试
const runSingleTest = async (testName) => {
  loading[testName] = true
  addLog('info', `开始运行 ${getTestLabel(testName)} 测试...`)
  
  try {
    let result
    
    switch (testName) {
      case 'basic':
        result = await testBasicFetch()
        break
      case 'post':
        result = await testPostFetch()
        break
      case 'timeout':
        result = await testFetchTimeout()
        break
      case 'errorCodes':
        result = await testErrorStatusCodes()
        break
      case 'authPattern':
        result = await testAuthRequestPattern()
        break
      default:
        throw new Error(`未知的测试类型: ${testName}`)
    }
    
    testResults.value[testName] = result
    
    if (result.success || (typeof result === 'object' && !result.error)) {
      addLog('success', `${getTestLabel(testName)} 测试成功`)
      ElMessage.success(`${getTestLabel(testName)} 测试成功`)
    } else {
      addLog('error', `${getTestLabel(testName)} 测试失败: ${result.error}`)
      ElMessage.error(`${getTestLabel(testName)} 测试失败`)
    }
  } catch (error) {
    addLog('error', `${getTestLabel(testName)} 测试异常: ${error.message}`)
    ElMessage.error(`${getTestLabel(testName)} 测试异常: ${error.message}`)
    testResults.value[testName] = {
      success: false,
      error: error.message,
      stack: error.stack
    }
  } finally {
    loading[testName] = false
  }
}

// 检查 HTTP 插件信息
const checkHttpInfo = async () => {
  loading.info = true
  addLog('info', '检查 HTTP 插件信息...')
  
  try {
    const info = await checkTauriHttpInfo()
    httpInfo.value = info
    
    addLog('success', 'HTTP 插件信息检查完成')
    ElMessage.success('HTTP 插件信息检查完成')
  } catch (error) {
    addLog('error', `HTTP 插件信息检查失败: ${error.message}`)
    ElMessage.error(`HTTP 插件信息检查失败: ${error.message}`)
    httpInfo.value = { error: error.message }
  } finally {
    loading.info = false
  }
}

// 运行所有测试
const runAllTests = async () => {
  loading.all = true
  addLog('info', '开始运行所有测试...')
  
  try {
    const results = await runAllFetchTests()
    testResults.value = results
    
    addLog('success', '所有测试完成')
    ElMessage.success('所有测试完成')
  } catch (error) {
    addLog('error', `运行所有测试失败: ${error.message}`)
    ElMessage.error(`运行所有测试失败: ${error.message}`)
  } finally {
    loading.all = false
  }
}

// 页面加载时自动检查 HTTP 插件信息
checkHttpInfo()
</script>

<style scoped>
.fetch-debugger {
  padding: 20px;
}

.debug-card,
.result-card,
.log-card {
  margin-bottom: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.error-info {
  margin-top: 15px;
}

.success-result,
.error-result {
  margin-bottom: 15px;
}

.result-details,
.error-stack,
.error-details {
  margin-top: 15px;
}

.result-details pre,
.error-stack pre,
.error-details pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
}

.log-item {
  margin-bottom: 5px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-time {
  color: #6c757d;
  margin-right: 10px;
}

.log-level {
  font-weight: bold;
  margin-right: 10px;
  min-width: 60px;
  display: inline-block;
}

.log-info .log-level {
  color: #17a2b8;
}

.log-success .log-level {
  color: #28a745;
}

.log-error .log-level {
  color: #dc3545;
}

.log-message {
  color: #495057;
}
</style>
