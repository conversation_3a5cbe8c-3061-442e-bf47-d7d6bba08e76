{"rustc": 16591470773350601817, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 2225463790103693989, "path": 3966551323784293027, "deps": [[10411997081178400487, "cfg_if", false, 17273532339252434183]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-266ba6a6ef91c5fd\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}