import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAccountStore = defineStore('account', () => {
  // 存储所有账号信息
  const accounts = ref([])
  
  // 平台类型映射
  const platformTypes = {
    1: '小红书',
    2: '视频号',
    3: '抖音',
    4: '快手'
  }
  
  // 设置账号列表
  const setAccounts = (accountsData) => {
    // 转换后端返回的数据格式为前端使用的格式
    accounts.value = accountsData.map(item => {
      return {
        id: item.id,
        type: item.type,
        filePath: item.filePath,
        name: item.userName,
        status: item.status === 1 ? '正常' : '异常',
        platform: platformTypes[item.type] || '未知',
        avatar: item.avatar, // 默认使用vite.svg作为头像
        autoPublish: item.auto_publish || 1 // 添加自动发布状态，默认支持
      }
    })
  }
  
  // 添加账号
  const addAccount = (account) => {
    accounts.value.push(account)
  }
  
  // 更新账号
  const updateAccount = (id, updatedAccount) => {
    const index = accounts.value.findIndex(acc => acc.id === id)
    if (index !== -1) {
      accounts.value[index] = { ...accounts.value[index], ...updatedAccount }
    }
  }
  
  // 删除账号
  const deleteAccount = (id) => {
    accounts.value = accounts.value.filter(acc => acc.id !== id)
  }
  
  // 根据平台获取账号
  const getAccountsByPlatform = (platform) => {
    return accounts.value.filter(acc => acc.platform === platform)
  }

  // 更新账号自动发布状态
  const updateAccountAutoPublish = (id, autoPublish) => {
    const index = accounts.value.findIndex(acc => acc.id === id)
    if (index !== -1) {
      accounts.value[index].autoPublish = autoPublish
    }
  }

  // 获取支持自动发布的账号
  const getAutoPublishAccounts = () => {
    return accounts.value.filter(acc => acc.autoPublish === 1)
  }

  return {
    accounts,
    setAccounts,
    addAccount,
    updateAccount,
    deleteAccount,
    getAccountsByPlatform,
    updateAccountAutoPublish,
    getAutoPublishAccounts
  }
})