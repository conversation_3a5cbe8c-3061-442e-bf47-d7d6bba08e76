# -*- coding: utf-8 -*-
from datetime import datetime

from playwright.async_api import Playwright, async_playwright
import os
import asyncio

# 设置环境变量以禁用 Playwright 依赖检查
os.environ['PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD'] = '1'
os.environ['PLAYWRIGHT_BROWSERS_PATH'] = '0'
os.environ['PLAYWRIGHT_SKIP_VALIDATE_HOST_REQUIREMENTS'] = '1'
os.environ['PLAYWRIGHT_SKIP_BROWSER_GC'] = '1'

from conf import LOCAL_CHROME_PATH
from utils.base_social_media import set_init_script
from utils.files_times import get_absolute_path
from utils.log import kuaishou_logger
import re
import json


async def cookie_auth(account_file):
    async with async_playwright() as playwright:
        # 浏览器启动选项
        launch_options = {
            'headless': True,
            'args': [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu'
            ]
        }

        try:
            browser = await playwright.chromium.launch(**launch_options)
        except Exception as e:
            kuaishou_logger.error(f"cookie_auth 浏览器启动失败: {e}")
            raise

        context = await browser.new_context(storage_state=account_file)
        context = await set_init_script(context)
        # 创建一个新的页面
        page = await context.new_page()
        # 访问指定的 URL
        await page.goto("https://cp.kuaishou.com/article/publish/video")
        try:
            await page.wait_for_selector("div.names div.container div.name:text('机构服务')", timeout=5000)  # 等待5秒

            kuaishou_logger.info("[+] 等待5秒 cookie 失效")
            return False
        except:
            kuaishou_logger.success("[+] cookie 有效")
            return True


async def ks_setup(account_file, handle=False):
    account_file = get_absolute_path(account_file, "ks_uploader")
    if not os.path.exists(account_file) or not await cookie_auth(account_file):
        if not handle:
            return False
        kuaishou_logger.info('[+] cookie文件不存在或已失效，即将自动打开浏览器，请扫码登录，登陆后会自动生成cookie文件')
        await get_ks_cookie(account_file)
    return True


async def get_ks_cookie(account_file):
    async with async_playwright() as playwright:
        options = {
            'args': [
                '--lang en-GB'
            ],
            'headless': False,  # Set headless option here
        }
        # Make sure to run headed.
        browser = await playwright.chromium.launch(**options)
        # Setup context however you like.
        context = await browser.new_context()  # Pass any options
        context = await set_init_script(context)
        # Pause the page, and start recording manually.
        page = await context.new_page()
        await page.goto("https://cp.kuaishou.com")
        # 点击调试器的继续，保存cookie
        await context.storage_state(path=account_file)
        
async def retry_async(func, retries=5, delay=1, *args, **kwargs):
        """通用异步重试工具"""
        for attempt in range(retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if attempt < retries - 1:
                    kuaishou_logger.warning(f"重试 {func.__name__} 第{attempt+1}次，原因: {e}")
                    await asyncio.sleep(delay)
                else:
                    raise


class KSVideo(object):
    def __init__(self, title, file_path, tags, publish_date: datetime, account_file, goods_id, cover_path):
        self.title = title  # 视频标题
        self.file_path = file_path
        self.tags = tags
        self.publish_date = publish_date
        self.account_file = account_file
        self.date_format = '%Y-%m-%d %H:%M'
        self.local_executable_path = LOCAL_CHROME_PATH
        self.goods_id = goods_id
        self.cover_path = cover_path

        # 调试信息
        print(f"DEBUG KSVideo: publish_date={publish_date}, type={type(publish_date)}")

    async def handle_upload_error(self, page):
        kuaishou_logger.error("视频出错了，重新上传中")
        await page.locator('div.progress-div [class^="upload-btn-input"]').set_input_files(self.file_path)

    
    
    async def upload(self, playwright: Playwright) -> None:
        # 使用 Chromium 浏览器启动一个浏览器实例
        print(f"DEBUG: local_executable_path = {self.local_executable_path}")

        # 浏览器启动选项
        launch_options = {
            'headless': True,
            'args': [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu'
            ]
        }

        # 如果有指定的浏览器路径且文件存在，使用它
        if self.local_executable_path and os.path.exists(self.local_executable_path):
            launch_options['executable_path'] = self.local_executable_path
            kuaishou_logger.info(f"使用指定工具")
        else:
            kuaishou_logger.info("默认发布工具")

        try:
            browser = await playwright.chromium.launch(**launch_options)
        except Exception as e:
            kuaishou_logger.error(f"浏览器启动失败: {e}")
            # 如果指定路径失败，尝试使用默认浏览器
            if 'executable_path' in launch_options:
                kuaishou_logger.info("尝试使用默认浏览器...")
                del launch_options['executable_path']
                browser = await playwright.chromium.launch(**launch_options)
            else:
                raise
        context = await browser.new_context(storage_state=f"{self.account_file}")
        context = await set_init_script(context)
        # 创建一个新的页面
        page = await context.new_page()
        # 访问指定的 URL
        await page.goto("https://cp.kuaishou.com/article/publish/video")
        
        kuaishou_logger.info('正在上传-------{}.mp4'.format(self.title))
        # 等待页面跳转到指定的 URL，没进入，则自动等待到超时
        kuaishou_logger.info('正在打开主页...')
        await page.wait_for_url("https://cp.kuaishou.com/article/publish/video")
        
        
        
        # 点击 "上传视频" 按钮
        upload_button = page.locator("button[class^='_upload-btn']")
        await upload_button.wait_for(state='visible')  # 确保按钮可见

        async with page.expect_file_chooser() as fc_info:
            await upload_button.click()
        file_chooser = await fc_info.value 
        await file_chooser.set_files(self.file_path)
        
        await asyncio.sleep(2)

        # 如果有下一步就点击关闭
        next_btn = page.get_by_role("button", name="Skip")
        if await next_btn.count() > 0:
            await next_btn.click()
            
        # 等待按钮可交互
        new_feature_button = page.locator('button[type="button"] span:text("我知道了")')
        if await new_feature_button.count() > 0:
            await new_feature_button.click()
        # ====== 新增：上传封面图逻辑 ======
         # ====== 上传封面图流程，失败回退到上一步 ======
        if self.cover_path:
            async def step1():
                cover_setting_btns = page.get_by_text("封面设置")
                count = await cover_setting_btns.count()
                if count > 1:
                    await cover_setting_btns.nth(1).wait_for(state="visible", timeout=5000)
                    await cover_setting_btns.nth(1).click()
                elif count > 0:
                    await cover_setting_btns.first.wait_for(state="visible", timeout=5000)
                    await cover_setting_btns.first.click()
                else:
                    raise Exception("未找到封面设置按钮")

            async def step2():
                upload_cover_btn = page.get_by_text("上传封面", exact=True)
                await upload_cover_btn.first.wait_for(state="visible", timeout=5000)
                if await upload_cover_btn.count() > 0:
                    await upload_cover_btn.first.click()
                else:
                    raise Exception("未找到上传封面按钮")

            async def step3():
                file_input = page.locator('[class^="_cropper-upload-upload"] input[type="file"]')
                if await file_input.count() > 0:
                    await file_input.first.set_input_files(self.cover_path)
                    await page.wait_for_selector('[class*="_cutter_y1mcy_1"]', timeout=10000)
                else:
                    raise Exception("未找到封面图上传 input")

            async def step4():
                confirm_btn = page.get_by_role("button", name="确认")
                if await confirm_btn.count() > 0:
                    await confirm_btn.first.wait_for(state="visible", timeout=5000)
                    await confirm_btn.first.click()
                else:
                    raise Exception("未找到确认按钮")

            # 控制流程
            max_retries = 3
            i = 0
            while i < max_retries:
                try:
                    await step1()
                    try:
                        await step2()
                        try:
                            await step3()
                            try:
                                await step4()
                                kuaishou_logger.success("封面图上传成功")
                                break
                            except Exception as e:
                                kuaishou_logger.warning(f"step4 等待封面图上传...: {e}")
                                continue  # 回到step3
                        except Exception as e:
                            kuaishou_logger.warning(f"step3失败，等待封面图上传...: {e}")
                            continue  # 回到step2
                    except Exception as e:
                        kuaishou_logger.warning(f"step2失败，等待封面图上传...: {e}")
                        continue  # 回到step1
                except Exception as e:
                    kuaishou_logger.warning(f"step1失败: {e}")
                    i += 1
                    continue
            else:
                kuaishou_logger.error("封面图上传多次失败，跳过上传")

        await asyncio.sleep(1)
        
        

        kuaishou_logger.info("正在填充标题和话题...")
        await page.get_by_text("描述").locator("xpath=following-sibling::div").click()
        await page.keyboard.press("Backspace")
        await page.keyboard.press("Control+KeyA")
        await page.keyboard.press("Delete")
        await page.keyboard.type(self.title)
        await page.keyboard.press("Enter")
        # debugger
        
        if self.goods_id:
            # 关联商品
            try:
                kuaishou_logger.info("检测到商品ID，正在关联商品..." + str(self.goods_id))
                # 等待并点击“选择服务类型”
                service_type_btns = page.locator("div").filter(has_text=re.compile(r"^选择服务类型$"))
                if await service_type_btns.count() > 1:
                    await service_type_btns.nth(1).click()
                else:
                    await service_type_btns.first.click()
                await asyncio.sleep(1)

                # 点击“关联商品”
                link_goods_btn = page.get_by_title("关联商品")
                if await link_goods_btn.count() > 0:
                    await link_goods_btn.click()
                    await asyncio.sleep(1)
                    # 选择商品说明
                    desc_btns = page.locator("div").filter(has_text=re.compile(r"^关联商品获得更多收入$"))
                    if await desc_btns.count() > 1:
                        await desc_btns.nth(1).click()
                    elif await desc_btns.count() > 0:
                        await desc_btns.first.click()
                    # await page.pause()  # 调试用，手动检查标题是否正确


                    # 选择具体商品（此处商品名请根据实际页面调整）
                    goods_id_str = str(self.goods_id) if self.goods_id is not None else ""
                    # 步骤1：等待输入框可见
                    try:
                        await page.wait_for_selector("#rc_select_4", state="visible", timeout=5000)
                    except Exception as e:
                        kuaishou_logger.error(f"商品ID输入框未出现: {e}")
                        raise

                    # 步骤2：点击输入框
                    input_box = page.locator("#rc_select_4")
                    if await input_box.count() == 0:
                        kuaishou_logger.error("未找到商品ID输入框")
                        raise Exception("未找到商品ID输入框")
                    await input_box.click()
                    await asyncio.sleep(0.2)

                    # 步骤3：填入商品ID
                    await input_box.fill(goods_id_str)
                    await asyncio.sleep(2)

                    # 步骤4：按下回车
                    # await page.keyboard.press("Enter")

                    # 步骤5：等待商品筛选结果并直接点击
                    try:
                        # 等待商品筛选结果出现：检测隐藏的 rc_select_4_list_0 元素
                        kuaishou_logger.info("等待商品筛选结果...")
                        target_item_selector = "#rc_select_4_list_0"

                        # 等待目标商品选项出现（包括隐藏元素）
                        await page.wait_for_selector(target_item_selector, state="attached", timeout=10000)
                        kuaishou_logger.info("✅ 检测到商品筛选结果（隐藏DOM）")

                        # 获取隐藏元素的商品信息内容
                        target_item = page.locator(target_item_selector)
                        item_content = await target_item.inner_text()

                        if item_content and item_content.strip():
                            kuaishou_logger.info(f"找到商品信息: {item_content[:100]}...")

                            # 查找对应的可见商品选项并点击
                            # 由于 rc_select_4_list_0 是隐藏的，需要找到对应的可见 .ant-select-item
                            kuaishou_logger.info("查找对应的可见商品选项...")

                            # 解析商品信息获取标题
                            try:
                                # 尝试从JSON中提取商品标题
                                import json
                                if item_content.startswith('{') and item_content.endswith('}'):
                                    goods_data = json.loads(item_content)
                                    goods_title = goods_data.get('title', '')
                                    kuaishou_logger.info(f"提取到商品标题: {goods_title[:50]}...")
                                else:
                                    goods_title = item_content[:50]  # 使用前50个字符
                                    kuaishou_logger.info(f"使用内容前缀: {goods_title}...")
                            except:
                                goods_title = item_content[:50]  # 使用前50个字符作为备选
                                kuaishou_logger.info(f"解析失败，使用内容前缀: {goods_title}...")

                            # 等待可见的商品选项出现
                            await asyncio.sleep(1)  # 给页面一点时间渲染

                            # 获取所有可见的商品选项
                            visible_items = page.locator(".ant-select-item")
                            visible_count = await visible_items.count()
                            kuaishou_logger.info(f"找到 {visible_count} 个可见商品选项")

                            if visible_count > 0:
                                # 点击第一个可见选项（通常就是筛选结果）
                                first_visible_item = visible_items.first
                                await first_visible_item.click()
                                kuaishou_logger.info("✅ 成功点击第一个可见商品选项")
                            else:
                                kuaishou_logger.warning("❌ 未找到可见的商品选项")
                                raise Exception("未找到可见的商品选项")

                        else:
                            kuaishou_logger.warning("❌ 商品选项内容为空")
                            raise Exception("商品选项内容为空")

                    except Exception as e:
                        kuaishou_logger.error(f"商品选择失败: {e}")
                        raise

                    kuaishou_logger.info("商品关联流程完成")
                    await asyncio.sleep(1)
            except Exception as e:
                await page.pause()
                kuaishou_logger.warning(f"关联商品失败，请手动关联: {e}")
        
        
        # 确保聚焦到标签输入框
        await page.get_by_text("描述").locator("xpath=following-sibling::div").click()
        await asyncio.sleep(0.5)

        # 添加标签 快手只能添加3个话题
        for index, tag in enumerate(self.tags[:3], start=1):
            kuaishou_logger.info("正在添加第%s个话题" % index)
            await page.keyboard.type(f"#{tag} ")
            await asyncio.sleep(2)

        max_retries = 60  # 设置最大重试次数,最大等待时间为 2 分钟
        retry_count = 0
          # 调试用，手动检查标题是否正确

        while retry_count < max_retries:
            try:
                # 获取包含 '上传中' 文本的元素数量
                number = await page.locator("text=上传中").count()

                if number == 0:
                    kuaishou_logger.success("视频上传完毕")
                    break
                else:
                    if retry_count % 5 == 0:
                        kuaishou_logger.info("正在上传视频中...")
                    await asyncio.sleep(2)
            except Exception as e:
                kuaishou_logger.error(f"检查上传状态时发生错误: {e}")
                await asyncio.sleep(2)  # 等待 2 秒后重试
            retry_count += 1

        if retry_count == max_retries:
            kuaishou_logger.warning("超过最大重试次数，视频上传可能未完成。")
        # await page.pause()
        # 定时任务
        if self.publish_date is not None and self.publish_date != 0:
            kuaishou_logger.info(f"开始设置定时发布，目标时间: {self.publish_date}")
            await self.set_schedule_time(page, self.publish_date)
            kuaishou_logger.success("定时发布设置完成")
        else:
            kuaishou_logger.info("选择立即发布模式")

        # 判断视频是否发布成功
        kuaishou_logger.info("开始执行发布流程")
        while True:
            try:
                # 查找发布按钮
                publish_button = page.get_by_text("发布", exact=True)
                if await publish_button.count() > 0:
                    if self.publish_date is not None and self.publish_date != 0:
                        kuaishou_logger.info("点击定时发布按钮")
                    else:
                        kuaishou_logger.info("点击立即发布按钮")
                    await publish_button.click()

                await asyncio.sleep(1)

                # 查找确认发布按钮
                confirm_button = page.get_by_text("确认发布")
                if await confirm_button.count() > 0:
                    kuaishou_logger.info("点击确认发布按钮")
                    await confirm_button.click()

                # 等待页面跳转，确认发布成功
                kuaishou_logger.info("等待发布完成...")
                await page.wait_for_url(
                    "https://cp.kuaishou.com/article/manage/video?status=2&from=publish",
                    timeout=5000,
                )

                if self.publish_date is not None and self.publish_date != 0:
                    kuaishou_logger.success(f"视频定时发布设置成功，将在 {self.publish_date} 发布")
                else:
                    kuaishou_logger.success("视频立即发布成功")
                break
            except Exception as e:
                kuaishou_logger.warning(f"发布流程中遇到异常，继续等待: {e}")
                await page.screenshot(full_page=True)
                await asyncio.sleep(1)

        # 保存登录状态
        kuaishou_logger.info("开始保存登录状态...")
        await context.storage_state(path=self.account_file)  # 保存cookie
        kuaishou_logger.success('登录状态保存完毕！')
        await asyncio.sleep(2)  # 这里延迟是为了方便眼睛直观的观看
        # 关闭浏览器上下文和浏览器实例
        await context.close()
        await browser.close()

    async def main(self):
        if self.goods_id:
            print(f"goods_id: {self.goods_id} 检测是否挂车")
            # await self.add_to_shelf(self.goods_id)  # 自动化加入货架流程 已在前端处理
        async with async_playwright() as playwright:
            await self.upload(playwright)
    
    async def add_to_shelf(self, goods_id):
        """
        自动化加入货架流程
        :param goods_id: 要搜索并加入货架的商品ID
        """
        async with async_playwright() as playwright:
            browser = await playwright.chromium.launch(
                headless=False,
                executable_path=self.local_executable_path,
            )
            context = await browser.new_context(storage_state=f"{self.account_file}")
            page = await context.new_page()
            await page.goto("https://cps.kwaixiaodian.com/pc/promoter/selection-center/home", wait_until="networkidle")
            # await page.reload(ignore_cache=True)
            # 等待搜索框出现并输入 goods_id
            await page.wait_for_selector("#rc_select_0", timeout=10000)
            await page.fill("#rc_select_0", str(goods_id))

            # 点击搜索按钮
            await page.click(".cps-ant-select-selection-search > span > span > span.cps-ant-input-group-addon > button")
            await asyncio.sleep(1)  # 等待搜索结果加载

            # debugger
            # 如果有这个就点击
            close_btn = page.get_by_role("button", name="Close")
            if await close_btn.count() > 0:
                await close_btn.click()
            
            
            # 查找所有商品容器
            goods_containers = page.locator("[class*='index-module__goodsList']")
            count = await goods_containers.count()
            found = False
            for i in range(count):
                container = goods_containers.nth(i)
                
                # 查找所有商品标题
                title = container.locator("[class*='index-module__title']").first
                if await title.count() > 0:
                    good_text = await title.inner_text()
                else:
                    good_text = ""
                self.good_text = good_text
                print(f"商品标题: {self.good_text}")
                # 查找 class 包含 'cps-ant-btn' 的 button（只取第一个）
                btn = container.locator("button.cps-ant-btn").first
                if await btn.count() > 0:
                     # 判断按钮是否为 disabled，若是则跳过
                    is_disabled = await btn.get_attribute("disabled") is not None
                    if is_disabled:
                        kuaishou_logger.warning(f"商品 {goods_id}（{self.good_text}）的按钮为 disabled，跳过")
                        continue
                    await btn.click()
                    found = True
                    # 成功点击按钮后，打印日志
                    kuaishou_logger.success(f"商品 {goods_id}（{self.good_text}）已点击 cps-ant-btn 按钮")
                    await asyncio.sleep(1)  # 等待弹窗或页面变化
                    promise_letter = page.get_by_text("承诺函", exact=True)
                    if await promise_letter.count() > 0:
                        kuaishou_logger.info("检测到承诺函弹窗")
                    else:
                        kuaishou_logger.info("未检测到承诺函弹窗")
                        
                    continue_btn = page.get_by_role("button", name="继续加架")
                    if await continue_btn.count() > 0:
                        await continue_btn.click()
                        kuaishou_logger.info("已点击继续加架按钮")
                    break
                
            if not found:
                kuaishou_logger.warning(f"未找到商品 {goods_id} 的 cps-ant-btn 按钮")
            await asyncio.sleep(1)
            await context.close()
            await browser.close()            
    

    async def set_schedule_time(self, page, publish_date):
        try:
            kuaishou_logger.info("开始设置定时发布时间")
            publish_date_hour = publish_date.strftime("%Y-%m-%d %H:%M:%S")
            kuaishou_logger.info(f"格式化发布时间: {publish_date_hour}")

            # 点击定时发布选项
            kuaishou_logger.info("点击定时发布选项")
            await page.locator("label:text('发布时间')").locator('xpath=following-sibling::div').locator(
                '.ant-radio-input').nth(1).click()
            await asyncio.sleep(1)
            kuaishou_logger.info("定时发布选项已选中")

            # 点击时间选择器
            kuaishou_logger.info("点击时间选择器")
            await page.locator('div.ant-picker-input input[placeholder="选择日期时间"]').click()
            await asyncio.sleep(1)
            kuaishou_logger.info("时间选择器已打开")

            # 输入时间
            kuaishou_logger.info(f"输入发布时间: {publish_date_hour}")
            await page.keyboard.press("Control+KeyA")
            await page.keyboard.type(str(publish_date_hour))
            await page.keyboard.press("Enter")
            await asyncio.sleep(1)
            kuaishou_logger.success(f"定时发布时间设置成功: {publish_date_hour}")

        except Exception as e:
            kuaishou_logger.error(f"设置定时发布时间失败: {e}")
            raise
