#!/usr/bin/env python3
"""
Playwright 环境检查和修复脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_playwright_installation():
    """检查 Playwright 安装情况"""
    print("检查 Playwright 安装情况...")
    
    try:
        import playwright
        # 尝试获取版本信息
        version = "未知版本"
        try:
            version = playwright.__version__
        except AttributeError:
            # 如果没有 __version__ 属性，尝试其他方式
            import pkg_resources
            try:
                version = pkg_resources.get_distribution("playwright").version
            except:
                pass
        
        print(f"✓ Playwright 已安装: {version}")
        print(f"  安装路径: {playwright.__file__}")
        return True
    except ImportError:
        print("❌ Playwright 未安装")
        return False

def check_playwright_browsers():
    """检查 Playwright 浏览器安装情况"""
    print("\n检查 Playwright 浏览器...")
    
    ms_playwright_dir = Path.home() / "AppData" / "Local" / "ms-playwright"
    
    if not ms_playwright_dir.exists():
        print("❌ Playwright 浏览器目录不存在")
        return False
    
    print(f"✓ Playwright 浏览器目录: {ms_playwright_dir}")
    
    # 检查已安装的浏览器
    browsers = []
    for item in ms_playwright_dir.iterdir():
        if item.is_dir() and not item.name.startswith('.'):
            browsers.append(item)
    
    if browsers:
        print("  已安装的浏览器:")
        for browser in browsers:
            print(f"    - {browser.name}")
        return True
    else:
        print("❌ 未找到已安装的浏览器")
        return False

def check_playwright_driver():
    """检查 Playwright 驱动"""
    print("\n检查 Playwright 驱动...")
    
    try:
        import playwright
        driver_dir = Path(playwright.__file__).parent / "driver"
        
        if driver_dir.exists():
            print(f"✓ Playwright 驱动目录: {driver_dir}")
            
            # 检查驱动内容
            package_dir = driver_dir / "package"
            if package_dir.exists():
                print(f"  驱动包目录: {package_dir}")
                return True
            else:
                print("❌ 驱动包目录不存在")
                return False
        else:
            print("❌ Playwright 驱动目录不存在")
            return False
    except ImportError:
        return False

def test_playwright_basic():
    """测试 Playwright 基本功能"""
    print("\n测试 Playwright 基本功能...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        print("正在启动 Playwright...")
        with sync_playwright() as p:
            # 尝试启动浏览器
            browser = p.chromium.launch(headless=True)
            print("✓ 成功启动 Chromium 浏览器")
            
            # 创建页面
            page = browser.new_page()
            print("✓ 成功创建页面")
            
            # 访问简单页面
            page.goto("data:text/html,<h1>Test</h1>")
            print("✓ 成功访问页面")
            
            browser.close()
            print("✓ Playwright 基本功能测试通过")
            return True
            
    except Exception as e:
        print(f"❌ Playwright 测试失败: {e}")
        return False

def install_playwright_browsers():
    """安装 Playwright 浏览器"""
    print("\n安装 Playwright 浏览器...")
    
    try:
        # 先安装 winldd 依赖
        print("安装 winldd 依赖...")
        result = subprocess.run([
            sys.executable, "-m", "playwright", "install", "winldd"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ winldd 依赖安装成功")
        else:
            print(f"警告: winldd 安装可能失败: {result.stderr}")
        
        # 安装 chromium 浏览器
        print("安装 Chromium 浏览器...")
        result = subprocess.run([
            sys.executable, "-m", "playwright", "install", "chromium"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Chromium 浏览器安装成功")
            return True
        else:
            print(f"❌ Chromium 浏览器安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装浏览器时出错: {e}")
        return False

def fix_playwright_environment():
    """修复 Playwright 环境"""
    print("\n修复 Playwright 环境...")
    
    # 设置环境变量
    ms_playwright_dir = Path.home() / "AppData" / "Local" / "ms-playwright"
    if ms_playwright_dir.exists():
        os.environ["PLAYWRIGHT_BROWSERS_PATH"] = str(ms_playwright_dir)
        print(f"✓ 设置 PLAYWRIGHT_BROWSERS_PATH: {ms_playwright_dir}")
    
    # 检查并创建必要目录
    ms_playwright_dir.mkdir(exist_ok=True)
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("Playwright 环境检查和修复工具")
    print("=" * 60)
    
    # 检查步骤
    checks = [
        ("Playwright 安装", check_playwright_installation),
        ("Playwright 驱动", check_playwright_driver),
        ("Playwright 浏览器", check_playwright_browsers),
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        if not check_func():
            all_passed = False
    
    if not all_passed:
        print("\n" + "="*60)
        print("检测到问题，尝试修复...")
        
        # 修复步骤
        if not check_playwright_installation():
            print("请先安装 Playwright: pip install playwright")
            return
        
        fix_playwright_environment()
        
        if not check_playwright_browsers():
            if not install_playwright_browsers():
                print("浏览器安装失败，请手动运行:")
                print("  python -m playwright install chromium")
                return
    
    # 最终测试
    print(f"\n{'='*20} 功能测试 {'='*20}")
    if test_playwright_basic():
        print("\n" + "="*60)
        print("🎉 Playwright 环境检查通过!")
        print("现在可以正常使用 Playwright 功能了")
        print("=" * 60)
    else:
        print("\n" + "="*60)
        print("❌ Playwright 环境仍有问题")
        print("请检查上方的错误信息并手动修复")
        print("=" * 60)

if __name__ == "__main__":
    main()
