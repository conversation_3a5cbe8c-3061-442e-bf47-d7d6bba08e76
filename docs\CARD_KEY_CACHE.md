# 卡密缓存功能说明

## 概述

为了提升用户体验，系统现在支持缓存用户的登录卡密，用户可以选择"记住卡密"，下次登录时无需重新输入。

## 功能特性

### 1. 安全存储
- **编码存储**: 卡密不以明文形式存储，使用Base64编码和字符串反转进行简单混淆
- **过期机制**: 缓存的卡密有效期为30天，过期后自动清除
- **时间戳记录**: 记录保存时间，用于过期检查

### 2. 用户控制
- **可选功能**: 用户可以选择是否记住卡密
- **手动清除**: 提供"清除保存的卡密"按钮，用户可随时清除
- **自动清除**: 登录失败或数据损坏时自动清除

### 3. 智能管理
- **过期检查**: 每次加载时检查是否过期
- **数据验证**: 检查存储数据的完整性
- **错误恢复**: 数据损坏时自动清除并重置

## 技术实现

### 存储结构
```javascript
// localStorage中的存储项
{
  'remembered_card_key': 'encoded_card_key',      // 编码后的卡密
  'remember_card_key_flag': 'true',              // 记住标志
  'card_key_timestamp': '1640995200000'          // 保存时间戳
}
```

### 编码算法
```javascript
// 编码过程
const encoded = btoa(encodeURIComponent(cardKey))  // Base64编码
const final = encoded.split('').reverse().join('') // 字符串反转

// 解码过程
const reversed = encodedStr.split('').reverse().join('') // 反转回来
const decoded = decodeURIComponent(atob(reversed))       // Base64解码
```

### 过期检查
```javascript
const EXPIRE_TIME = 30 * 24 * 60 * 60 * 1000 // 30天
const isExpired = (Date.now() - saveTime) > EXPIRE_TIME
```

## 使用方法

### 1. 保存卡密
1. 在登录页面输入卡密
2. 勾选"记住卡密（30天）"选项
3. 点击登录，登录成功后卡密会自动保存

### 2. 自动填充
1. 下次访问登录页面时
2. 如果有保存的卡密且未过期
3. 会自动填充到输入框中
4. "记住卡密"选项会自动勾选

### 3. 清除卡密
**方法一：手动清除**
- 点击"清除保存的卡密"按钮

**方法二：取消记住**
- 取消勾选"记住卡密"选项
- 重新登录时会清除保存的卡密

**方法三：自动清除**
- 卡密过期（30天后）
- 登录失败（可能卡密已更改）
- 数据损坏时

## API接口

### 工具函数
```javascript
import { 
  saveCardKey, 
  loadCardKey, 
  clearCardKey, 
  hasRememberedCardKey,
  getCardKeyInfo,
  refreshCardKeyTimestamp,
  CARD_KEY_CONFIG 
} from '@/utils/cardKeyStorage'
```

### 主要方法

#### saveCardKey(cardKey, remember)
保存卡密到本地存储
- **参数**: 
  - `cardKey` (string): 要保存的卡密
  - `remember` (boolean): 是否记住卡密
- **返回**: boolean - 保存是否成功

#### loadCardKey()
从本地存储加载卡密
- **返回**: Object - `{ cardKey: string, remember: boolean }`

#### clearCardKey()
清除保存的卡密
- **返回**: boolean - 清除是否成功

#### hasRememberedCardKey()
检查是否有保存的卡密
- **返回**: boolean - 是否有有效的保存卡密

#### getCardKeyInfo()
获取卡密保存的详细信息
- **返回**: Object - 包含保存时间、剩余时间等信息

## 安全考虑

### 1. 数据保护
- **非明文存储**: 使用编码避免明文存储
- **本地存储**: 数据仅存储在用户本地，不上传服务器
- **自动过期**: 30天后自动清除，减少长期暴露风险

### 2. 风险提示
- **共享设备**: 在共享设备上不建议使用此功能
- **公共场所**: 在公共场所使用时建议不保存卡密
- **定期更换**: 建议定期更换卡密并清除旧的缓存

### 3. 安全建议
- 仅在个人设备上使用此功能
- 定期清除保存的卡密
- 如果卡密泄露，立即清除缓存并更换卡密

## 配置选项

### 过期时间配置
在 `src/utils/cardKeyStorage.js` 中修改：
```javascript
const CARD_KEY_EXPIRE_TIME = 30 * 24 * 60 * 60 * 1000 // 30天
```

### 编码方式配置
可以修改 `encodeString` 和 `decodeString` 函数来使用不同的编码方式。

## 故障排除

### 常见问题

1. **卡密没有自动填充**
   - 检查是否勾选了"记住卡密"
   - 确认卡密是否已过期（30天）
   - 查看浏览器控制台是否有错误

2. **清除按钮不显示**
   - 确认之前是否保存过卡密
   - 检查保存的卡密是否已过期

3. **登录失败但卡密正确**
   - 可能是保存的卡密已过期或损坏
   - 尝试清除保存的卡密重新输入

### 调试方法

1. **查看控制台日志**
   ```javascript
   // 会输出相关的操作日志
   console.log('已加载保存的卡密，有效期30天')
   console.log('卡密已保存到本地存储')
   ```

2. **检查localStorage**
   ```javascript
   // 在浏览器控制台中执行
   console.log(localStorage.getItem('remembered_card_key'))
   console.log(localStorage.getItem('remember_card_key_flag'))
   console.log(localStorage.getItem('card_key_timestamp'))
   ```

3. **手动清除数据**
   ```javascript
   // 在浏览器控制台中执行
   localStorage.removeItem('remembered_card_key')
   localStorage.removeItem('remember_card_key_flag')
   localStorage.removeItem('card_key_timestamp')
   ```

## 更新日志

### v1.0.0
- 初始版本，支持基本的卡密缓存功能
- 30天过期机制
- 简单编码存储
- 手动清除功能

### 未来计划
- 支持更强的加密算法
- 支持多账户卡密管理
- 支持自定义过期时间
- 支持导入/导出卡密配置
