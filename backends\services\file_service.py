"""
文件管理服务模块
处理所有与文件上传、下载、删除相关的业务逻辑
"""

import os
import sqlite3
import subprocess
import uuid
import shutil
import logging
from pathlib import Path
from werkzeug.utils import secure_filename

from ffmpeg_config import FFMPEG_BIN


class FileService:
    """文件管理服务类"""
    
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        self.video_dir = self.base_dir / "videoFile"
        self.db_path = self.base_dir / "database.db"
        
        # 确保目录存在
        self.video_dir.mkdir(exist_ok=True)
    
    def upload_file(self, file, custom_filename=None):
        """
        单文件上传处理
        """
        try:
            # 生成文件名
            if custom_filename:
                filename = custom_filename + "." + file.filename.split('.')[-1]
            else:
                filename = secure_filename(file.filename)

            # 生成UUID文件名
            uuid_v1 = uuid.uuid1()
            final_filename = f"{uuid_v1}_{filename}"
            filepath = self.video_dir / final_filename

            # 保存文件
            file.save(str(filepath))

            return {
                "success": True,
                "code": 200,
                "msg": "File uploaded successfully",
                "data": final_filename
            }

        except Exception as e:
            logging.error(f"文件上传失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e),
                "data": None
            }
    
    def upload_save(self, files, custom_filename=None):
        """
        多文件上传并保存到数据库
        """
        if not files or files[0].filename == '':
            return {
                "success": False,
                "code": 400,
                "msg": "No file part in the request",
                "data": None
            }

        results = []
        for file in files:
            try:
                # 处理文件名
                if custom_filename:
                    filename = custom_filename + "." + file.filename.split('.')[-1]
                else:
                    filename = secure_filename(file.filename)

                # 生成UUID文件名
                uuid_v1 = uuid.uuid1()
                final_filename = f"{uuid_v1}_{filename}"
                filepath = self.video_dir / final_filename

                # 保存文件
                file.save(str(filepath))

                # 生成封面图
                cover_result = self._generate_cover(uuid_v1, filepath)
                
                # 保存到数据库
                db_result = self._save_to_database(
                    filename=filename,
                    final_filename=final_filename,
                    filepath=filepath,
                    cover_path=cover_result.get('cover_path')
                )

                if db_result['success']:
                    results.append({
                        "filename": filename,
                        "filepath": final_filename,
                        "cover_image": str(cover_result.get('cover_path')) if cover_result.get('cover_path') else None
                    })
                else:
                    return db_result

            except Exception as e:
                logging.error(f"文件上传保存失败: {str(e)}")
                return {
                    "success": False,
                    "code": 500,
                    "msg": f"upload failed: {str(e)}",
                    "data": None
                }

        return {
            "success": True,
            "code": 200,
            "msg": "Files uploaded and saved successfully",
            "data": results
        }
    
    def _generate_cover(self, uuid_v1, video_path):
        """
        生成视频封面图
        """
        try:
            cover_name = f"{uuid_v1}_cover.jpg"
            cover_path = self.video_dir / cover_name
            ffmpeg_bin = FFMPEG_BIN

            # 提取第一帧
            subprocess.run([
                ffmpeg_bin,
                "-y",
                "-i", str(video_path),
                "-vf", "select=eq(n\\,0)",
                "-q:v", "2",
                str(cover_path)
            ], check=True, encoding='utf-8', errors='ignore')

            logging.info(f"封面图生成成功: {cover_path}")
            return {
                "success": True,
                "cover_path": cover_path
            }

        except Exception as e:
            logging.error(f"封面图生成失败: {e}")
            return {
                "success": False,
                "cover_path": None
            }
    
    def _save_to_database(self, filename, final_filename, filepath, cover_path):
        """
        保存文件记录到数据库
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO file_records (filename, filesize, file_path, cover_image)
                    VALUES (?, ?, ?, ?)
                ''', (
                    filename,
                    round(float(os.path.getsize(filepath)) / (1024 * 1024), 2),
                    final_filename,
                    str(cover_path) if cover_path else None
                ))
                conn.commit()
                logging.info(f"上传文件已记录: {final_filename}")

            return {
                "success": True,
                "code": 200,
                "msg": "Database record created"
            }

        except Exception as e:
            logging.error(f"数据库保存失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": str(e)
            }
    
    def get_all_files(self):
        """
        获取所有文件记录
        """
        try:
            # 添加调试信息
            print(f"FileService - base_dir: {self.base_dir}")
            print(f"FileService - db_path: {self.db_path}")
            print(f"FileService - 数据库文件存在: {self.db_path.exists()}")

            if self.db_path.exists():
                print(f"FileService - 数据库文件大小: {self.db_path.stat().st_size} bytes")

            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 检查表是否存在
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='file_records';")
                table_exists = cursor.fetchone()
                print(f"FileService - file_records 表存在: {table_exists is not None}")

                if not table_exists:
                    # 列出所有表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    print(f"FileService - 所有表: {[table[0] for table in tables]}")

                # 查询所有记录，按最新的往下排（降序）
                cursor.execute("SELECT * FROM file_records ORDER BY id DESC")
                rows = cursor.fetchall()

                # 将结果转为字典列表
                data = [dict(row) for row in rows]

            return {
                "success": True,
                "code": 200,
                "msg": "success",
                "data": data
            }

        except Exception as e:
            logging.error(f"获取文件列表失败: {str(e)}")
            print(f"FileService - 异常详情: {str(e)}")
            print(f"FileService - 异常类型: {type(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": "get file failed!",
                "data": None
            }
    
    def delete_file(self, file_id):
        """
        删除单个文件
        """
        if not file_id or not str(file_id).isdigit():
            return {
                "success": False,
                "code": 400,
                "msg": "Invalid or missing file ID",
                "data": None
            }

        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 查询要删除的记录
                cursor.execute("SELECT * FROM file_records WHERE id = ?", (file_id,))
                record = cursor.fetchone()

                if not record:
                    return {
                        "success": False,
                        "code": 404,
                        "msg": "File not found",
                        "data": None
                    }

                record = dict(record)
                
                # 删除数据库记录
                cursor.execute("DELETE FROM file_records WHERE id = ?", (file_id,))
                conn.commit()

            # 删除物理文件
            file_path = self.video_dir / record['file_path']
            logging.info(f"尝试删除物理文件: {file_path}")
            
            try:
                if file_path.exists():
                    file_path.unlink()
                    file_deleted = True
                else:
                    file_deleted = False
            except Exception as fe:
                logging.error(f"物理文件删除失败: {fe}")
                file_deleted = False

            return {
                "success": True,
                "code": 200,
                "msg": "File deleted successfully",
                "data": {
                    "id": record['id'],
                    "filename": record['filename'],
                    "file_deleted": file_deleted
                }
            }

        except Exception as e:
            logging.error(f"删除文件失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": "delete failed!",
                "data": None
            }
    
    def delete_files(self, ids):
        """
        批量删除文件
        """
        if not ids or not isinstance(ids, list):
            return {
                "success": False,
                "code": 400,
                "msg": "参数错误，需提供 id 列表",
                "data": None
            }

        deleted = []
        failed = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                for file_id in ids:
                    try:
                        cursor.execute("SELECT * FROM file_records WHERE id = ?", (file_id,))
                        record = cursor.fetchone()
                        
                        if not record:
                            failed.append({"id": file_id, "msg": "文件不存在"})
                            continue
                            
                        record = dict(record)
                        
                        # 删除数据库记录
                        cursor.execute("DELETE FROM file_records WHERE id = ?", (file_id,))
                        conn.commit()
                        
                        # 删除物理文件
                        file_path = self.video_dir / record['file_path']
                        try:
                            if file_path.exists():
                                file_path.unlink()
                                deleted.append({"id": file_id, "filename": record['filename']})
                            else:
                                deleted.append({"id": file_id, "filename": record['filename'], "msg": "物理文件不存在"})
                        except Exception as fe:
                            failed.append({"id": file_id, "msg": f"物理文件删除失败: {fe}"})
                            
                    except Exception as e:
                        failed.append({"id": file_id, "msg": f"处理失败: {str(e)}"})

            return {
                "success": True,
                "code": 200,
                "msg": "批量删除完成",
                "data": {
                    "deleted": deleted,
                    "failed": failed
                }
            }

        except Exception as e:
            logging.error(f"批量删除文件失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": f"批量删除失败: {str(e)}",
                "data": None
            }
    
    def update_material_cover_image(self, file_url, material_id):
        """
        更新素材的封面图
        """
        if not file_url or not material_id:
            return {
                "success": False,
                "code": 400,
                "msg": "参数 fileUrl 和 material_id 必须提供",
                "data": None
            }

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查询原封面
                cursor.execute("SELECT cover_image FROM file_records WHERE id = ?", (material_id,))
                row = cursor.fetchone()
                old_cover = row[0] if row else None

                # 删除原封面文件
                if old_cover:
                    old_cover_path = self.video_dir / old_cover
                    if old_cover_path.exists():
                        try:
                            old_cover_path.unlink()
                        except Exception as e:
                            logging.warning(f"删除原封面失败: {e}")

                # 拷贝新封面到视频目录下，重命名
                ext = Path(file_url).suffix
                new_cover_name = f"{material_id}_cover{ext}"
                new_cover_path = self.video_dir / new_cover_name
                shutil.copyfile(file_url, new_cover_path)

                # 更新数据库
                cursor.execute(
                    "UPDATE file_records SET cover_image = ? WHERE id = ?",
                    (str(new_cover_path), material_id)
                )
                conn.commit()
                
                if cursor.rowcount == 0:
                    return {
                        "success": False,
                        "code": 404,
                        "msg": "未找到对应素材",
                        "data": None
                    }

            return {
                "success": True,
                "code": 200,
                "msg": "封面图更新成功",
                "data": {"cover_image": str(new_cover_path)}
            }

        except Exception as e:
            logging.error(f"封面图更新失败: {str(e)}")
            return {
                "success": False,
                "code": 500,
                "msg": f"封面图更新失败: {str(e)}",
                "data": None
            }


# 便捷函数供路由调用
def upload_file_handler(base_dir, file, custom_filename=None):
    """文件上传处理函数"""
    service = FileService(base_dir)
    return service.upload_file(file, custom_filename)


def upload_save_handler(base_dir, files, custom_filename=None):
    """文件上传保存处理函数"""
    service = FileService(base_dir)
    return service.upload_save(files, custom_filename)


def get_all_files_handler(base_dir):
    """获取所有文件处理函数"""
    service = FileService(base_dir)
    return service.get_all_files()


def delete_file_handler(base_dir, file_id):
    """删除文件处理函数"""
    service = FileService(base_dir)
    return service.delete_file(file_id)


def delete_files_handler(base_dir, ids):
    """批量删除文件处理函数"""
    service = FileService(base_dir)
    return service.delete_files(ids)


def update_material_cover_image_handler(base_dir, file_url, material_id):
    """更新封面图处理函数"""
    service = FileService(base_dir)
    return service.update_material_cover_image(file_url, material_id)
