#!/usr/bin/env python3
"""
创建KS-Downloader所需的detail表
"""

import sqlite3
import os
from pathlib import Path

def create_detail_table():
    # 数据库文件路径
    BASE_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
    db_path = BASE_DIR / "database.db"
    print(f"使用的数据库路径: {db_path}")
    
    # 连接到SQLite数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建detail表 - 根据manager.py中的定义
    detail_table_sql = """
    CREATE TABLE IF NOT EXISTS detail (
        collection_time TEXT,
        photoType TEXT,
        authorID TEXT,
        name TEXT,
        userSex TEXT,
        detailID TEXT PRIMARY KEY,
        caption TEXT,
        coverUrl TEXT,
        duration TEXT,
        realLikeCount INTEGER,
        shareCount INTEGER,
        commentCount INTEGER,
        timestamp TEXT,
        viewCount TEXT,
        download TEXT,
        shopping_data TEXT
    );
    """
    
    cursor.execute(detail_table_sql)
    
    # 提交更改
    conn.commit()
    print("detail表创建成功")
    
    # 验证表结构
    cursor.execute("PRAGMA table_info(detail);")
    columns = cursor.fetchall()
    print(f"\ndetail表结构：")
    for col in columns:
        print(f"  {col[1]}: {col[2]}")
    
    # 关闭连接
    conn.close()

if __name__ == '__main__':
    create_detail_table()
