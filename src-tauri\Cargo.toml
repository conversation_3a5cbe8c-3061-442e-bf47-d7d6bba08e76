[package]
name = "app"
version = "0.1.0"
description = "A Tauri app that uses sidecars to load a Python server."
authors = ["you"]
license = ""
repository = ""
default-run = "app"
edition = "2021"
rust-version = "1.60"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
command-group = "2.1.0"
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "2", features = [ "protocol-asset", "devtools"] }
tauri-plugin-shell = "2"
tauri-plugin-http = { version = "2.4.4", features = ["unsafe-headers", "brotli"] }
tauri-plugin-dialog = "2"
tauri-plugin-fs = "2"
sysinfo = "0.35.1"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem and the built-in dev server is disabled.
# If you use cargo directly instead of tauri's cli you can use this feature flag to switch between tauri's `dev` and `build` modes.
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
