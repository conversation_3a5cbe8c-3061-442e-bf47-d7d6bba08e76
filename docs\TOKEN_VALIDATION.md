# Token验证机制说明

## 概述

为了确保安全性，系统现在在每次调用主应用API（`VITE_API_BASE_URL`）之前，都会先向认证服务器验证 `access_token` 的有效性。

## 工作流程

### 1. 请求拦截
当调用任何主应用API时，`src/utils/request.js` 会：
1. 检查是否存在 `access_token`
2. 如果没有token，直接跳转到登录页
3. 如果有token，先验证token有效性

### 2. Token验证
向认证服务器发送验证请求：
- **端点**: `POST /api/token/check`
- **请求头**: `Authorization: Bearer {access_token}`
- **请求体**: `{ "access_token": "..." }`

### 3. 验证结果处理
- **验证成功** (`status: 1`): 继续执行原始API请求
- **验证失败**: 立即清除登录状态并跳转到登录页

## 性能优化

### 缓存机制
为了避免频繁验证影响性能，系统实现了缓存机制：
- **缓存时间**: 5分钟
- **缓存条件**: 同一个token且验证成功
- **缓存清除**: 验证失败、登出、token变更时自动清除

### 缓存逻辑
```javascript
// 检查缓存
if (tokenValidationCache.token === token && 
    tokenValidationCache.isValid && 
    (now - tokenValidationCache.lastCheckTime) < 5分钟) {
  return true // 使用缓存结果
}
```

## 配置说明

### API端点配置
在 `src/config/api.js` 中添加了新的端点：
```javascript
AUTH: {
  TOKEN_CHECK: '/api/token/check'
}
```

### 环境变量
使用现有的认证服务器配置：
- **开发环境**: `VITE_AUTH_BASE_URL=http://admin.dev.com`
- **生产环境**: `VITE_AUTH_BASE_URL=https://video.freio.cn`

## 安全特性

### 1. 强制验证
- 每次主应用API调用都必须通过token验证
- 任何验证失败都会立即退出登录
- 无法绕过验证机制

### 2. 自动登出
验证失败时自动执行：
1. 清除localStorage中的用户信息
2. 清除token验证缓存
3. 跳转到登录页
4. 刷新页面重置应用状态

### 3. 错误处理
处理各种异常情况：
- 网络连接失败
- 认证服务器不可用
- 响应格式错误
- 业务逻辑错误

## 日志记录

### 详细日志
系统会记录详细的验证日志：
```javascript
[request] 检测到token，开始验证...
[request] 开始验证token...
[request] Token验证响应状态: 200
[request] Token验证响应数据: {...}
[request] Token验证成功
[request] Token验证通过，继续执行请求
```

### 缓存日志
```javascript
[request] 使用缓存的token验证结果
```

### 错误日志
```javascript
[request] Token验证失败: 错误信息
[request] Token验证异常: Error对象
[request] Token验证失败，执行登出
```

## 影响范围

### 受影响的API
所有通过 `src/utils/request.js` 发送的请求，包括：
- 账号管理API
- 文件管理API
- 发布相关API
- 日志相关API

### 不受影响的API
认证相关API（使用 `src/utils/authRequest.js`）：
- 登录API
- 刷新token API
- 登出API
- 原有的token验证API

## 测试方法

### 1. 正常流程测试
1. 正常登录
2. 调用任何业务API
3. 观察控制台日志，确认token验证通过

### 2. Token失效测试
1. 登录后手动修改localStorage中的access_token
2. 调用任何业务API
3. 确认自动跳转到登录页

### 3. 缓存测试
1. 登录后连续调用多个API
2. 观察日志，第一次应该验证token，后续应该使用缓存

### 4. 网络异常测试
1. 断开网络连接
2. 调用业务API
3. 确认正确处理网络错误

## 性能考虑

### 优化措施
1. **缓存机制**: 减少重复验证请求
2. **并发控制**: 避免同时发送多个验证请求
3. **快速失败**: 验证失败立即返回，不继续执行

### 性能影响
- **首次验证**: 增加一次网络请求延迟
- **缓存命中**: 几乎无性能影响
- **验证失败**: 快速失败，减少无效请求

## 故障排除

### 常见问题
1. **频繁跳转登录页**
   - 检查token是否正确
   - 确认认证服务器正常运行
   - 查看控制台错误日志

2. **API请求变慢**
   - 检查缓存是否正常工作
   - 确认认证服务器响应速度
   - 考虑调整缓存时间

3. **验证请求失败**
   - 检查认证服务器地址配置
   - 确认 `/api/token/check` 端点可用
   - 查看网络连接状态

### 调试方法
1. 查看浏览器控制台日志
2. 检查Network面板的请求详情
3. 确认localStorage中的token格式
4. 测试认证服务器端点可用性

## 配置选项

### 缓存时间调整
在 `src/utils/request.js` 中修改：
```javascript
cacheTimeout: 5 * 60 * 1000 // 5分钟，可根据需要调整
```

### 验证端点修改
在 `src/config/api.js` 中修改：
```javascript
TOKEN_CHECK: '/api/token/check' // 可根据后端实际端点调整
```

## 安全建议

1. **定期更新token**: 配合定时刷新机制使用
2. **监控异常**: 记录验证失败的频率和原因
3. **服务器端验证**: 确保后端也有相应的验证逻辑
4. **HTTPS通信**: 生产环境必须使用HTTPS
