// Node.js environment compatibility
if (typeof window === "undefined") {
  global.window = global;
}
if (typeof location === "undefined") {
  global.location = {
    href: "https://v.m.chenzhongtech.com",
    protocol: "https:",
    host: "v.m.chenzhongtech.com",
    hostname: "v.m.chenzhongtech.com",
    port: "",
    pathname: "/",
    search: "",
    hash: "",
  };
}
if (typeof document === "undefined") {
  global.document = {
    referrer: "",
    cookie: "",
  };
}
// Mock navigator object with sendBeacon - use different approach for read-only properties
if (typeof navigator === 'undefined') {
  global.navigator = {
    userAgent:
      "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
    sendBeacon: function (url, data) {
      // Mock implementation - just return true
      console.log("Mock sendBeacon called with:", url, data);
      return true;
    },
  };
} else if (typeof navigator.sendBeacon === 'undefined') {
  navigator.sendBeacon = function (url, data) {
    // Mock implementation - just return true
    console.log("Mock sendBeacon called with:", url, data);
    return true;
  };
}

var Jose = (function (e) {
  var t = {};
  function n(r) {
    var o;
    return (
      t[r] ||
      ((o = t[r] =
        {
          i: r,
          l: !1,
          exports: {},
        }),
      e[r].call(o.exports, o, o.exports, n),
      (o.l = !0),
      o)
    ).exports;
  }
  return (
    (n.m = e),
    (n.c = t),
    (n.d = function (e, t, r) {
      n.o(e, t) ||
        Object.defineProperty(e, t, {
          enumerable: !0,
          get: r,
        });
    }),
    (n.r = function (e) {
      "undefined" != typeof Symbol &&
        Symbol.toStringTag &&
        Object.defineProperty(e, Symbol.toStringTag, {
          value: "Module",
        }),
        Object.defineProperty(e, "__esModule", {
          value: !0,
        });
    }),
    (n.t = function (e, t) {
      if ((1 & t && (e = n(e)), 8 & t)) return e;
      if (4 & t && "object" == typeof e && e && e.__esModule) return e;
      var r = Object.create(null);
      if (
        (n.r(r),
        Object.defineProperty(r, "default", {
          enumerable: !0,
          value: e,
        }),
        2 & t && "string" != typeof e)
      )
        for (var o in e)
          n.d(
            r,
            o,
            function (t) {
              return e[t];
            }.bind(null, o)
          );
      return r;
    }),
    (n.n = function (e) {
      var t =
        e && e.__esModule
          ? function () {
              return e.default;
            }
          : function () {
              return e;
            };
      return n.d(t, "a", t), t;
    }),
    (n.o = function (e, t) {
      return Object.prototype.hasOwnProperty.call(e, t);
    }),
    (n.p = ""),
    n((n.s = 4))
  );
})([
  function (e, t) {
    function n(e) {
      return (n =
        "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
          ? function (e) {
              return typeof e;
            }
          : function (e) {
              return e &&
                "function" == typeof Symbol &&
                e.constructor === Symbol &&
                e !== Symbol.prototype
                ? "symbol"
                : typeof e;
            })(e);
    }
    (function () {
      var e = Object.create,
        r = [];
      (t.prototypeOf = function (e) {
        return null == e ? null : e.__proto__;
      }),
        (t.create = e),
        (t.hasProp = function (e, t) {
          return (
            Object.prototype.hasOwnProperty.call(e, t) ||
            ("object" === n(e[t]) && void 0 !== e[t])
          );
        }),
        (t.isArray = function () {
          if ("function" != typeof Array.isArray) return obj instanceof Array;
        }),
        (t.defProp = function (e, t, n) {
          return Object.defineProperty(e, t, n);
        }),
        (t.checkIdentifier = function (e) {
          return r.includes(e);
        }),
        (t.isNaNP = function (e) {
          return e != e;
        });
    }).call(this);
  },
  function (e, t) {
    function n(e) {
      return (n =
        "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
          ? function (e) {
              return typeof e;
            }
          : function (e) {
              return e &&
                "function" == typeof Symbol &&
                e.constructor === Symbol &&
                e !== Symbol.prototype
                ? "symbol"
                : typeof e;
            })(e);
    }
    var r = (function () {
      return this;
    })();
    try {
      r = r || new Function("return this")();
    } catch (o) {
      "object" === ("undefined" == typeof window ? "undefined" : n(window)) &&
        (r = window);
    }
    e.exports = r;
  },
  function (e, t, n) {
    (function () {
      var e = n(7).VmError,
        r = {}.hasOwnProperty,
        o =
          ((function (e, t) {
            for (var n in t) r.call(t, n) && (e[n] = t[n]);
            function o() {
              this.constructor = e;
            }
            (o.prototype = t.prototype),
              (e.prototype = new o()),
              (e.__super__ = t.prototype);
          })(i, e),
          (i.display = "StopIteration"),
          i);
      function i(e, t) {
        (this.value = e),
          (this.message = null != t ? t : "iterator has stopped");
      }
      function a(e) {
        (this.elements = e), (this.index = 0);
      }
      (a.prototype.next = function () {
        if (this.index >= this.elements.length) throw new o("array over");
        return this.elements[this.index++];
      }),
        (e = a),
        (t.StopIteration = o),
        (t.ArrayIterator = e);
    }).call(this);
  },
  function (e, t, n) {
    function r(e) {
      return (r =
        "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
          ? function (e) {
              return typeof e;
            }
          : function (e) {
              return e &&
                "function" == typeof Symbol &&
                e.constructor === Symbol &&
                e !== Symbol.prototype
                ? "symbol"
                : typeof e;
            })(e);
    }
    (function () {
      var e = {}.hasOwnProperty,
        o = n(0).isArray,
        i =
          ((a.prototype.run = function () {
            for (
              var e = this.callStack[this.depth], t = e.error;
              0 <= this.depth && e && !this.paused;

            )
              if (
                ((e = t ? this.unwind(t) : e).run(),
                (t = e.error) instanceof Error && this.injectStackTrace(t),
                e.done())
              ) {
                if (e.guards.length) {
                  var n = e.guards.pop();
                  if (n.finalizer) {
                    (e.ip = n.finalizer), (e.exitIp = n.end), (e.paused = !1);
                    continue;
                  }
                }
                e.construct &&
                  "object" !== (n = r(this.rv)) &&
                  "function" !== n &&
                  (this.rv = e.scope.get(0)),
                  (e = this.popFrame()) &&
                    !t &&
                    (e.evalStack.push(this.rv), (this.rv = void 0));
              } else t = (e = this.callStack[this.depth]).error;
            if (
              (this.timedOut() &&
                ((t = new Error(this)), this.injectStackTrace(t)),
              t)
            )
              throw t;
          }),
          (a.prototype.unwind = function (e) {
            for (var t = this.callStack[this.depth]; t; ) {
              t.error = e;
              var n = t.ip - 1,
                r = t.guards.length;
              if (r && (r = t.guards[r - 1]).start <= n && n <= r.end) {
                if (null !== r.handler)
                  if (n <= r.handler)
                    t.evalStack.push(e), (t.error = null), (t.ip = r.handler);
                  else {
                    if (!(r.finalizer && t.ip <= r.finalizer)) {
                      t = this.popFrame();
                      continue;
                    }
                    t.ip = r.finalizer;
                  }
                else t.ip = r.finalizer;
                return (t.paused = !1), t;
              }
              t = this.popFrame();
            }
            throw e;
          }),
          (a.prototype.injectStackTrace = function (e) {
            var t,
              n,
              r,
              i,
              a,
              s,
              c,
              l = [],
              u = 0;
            for (
              this.depth > this.maxTraceDepth &&
                (u = this.depth - this.maxTraceDepth),
                n = r = a = this.depth,
                s = u;
              a <= s ? r <= s : s <= r;
              n = a <= s ? ++r : --r
            )
              "<anonymous>" === (i = (t = this.callStack[n]).script.name) &&
                t.fname &&
                (i = t.fname),
                l.push({
                  at: {
                    name: i,
                    filename: t.script.filename,
                  },
                  line: t.line,
                  column: t.column,
                });
            if (e.trace) {
              for (c = e.trace; o(c[c.length - 1]); ) c = c[c.length - 1];
              c.push(l);
            } else e.trace = l;
            return (e.stack = e.toString());
          }),
          (a.prototype.pushFrame = function (e, t, n, r, o, i, a) {
            if (
              (null == i && (i = "<anonymous>"),
              null == a && (a = !1),
              this.checkCallStack())
            )
              return (
                (n = new p(n, e.localNames, e.localLength)).set(0, t),
                (t = new s(this, e, n, this.realm, i, a)),
                o && t.evalStack.push(o),
                r && t.evalStack.push(r),
                (this.callStack[++this.depth] = t)
              );
          }),
          (a.prototype.checkCallStack = function () {
            return (
              this.depth !== this.maxDepth ||
              ((this.callStack[this.depth].error = new Error(
                "maximum call stack size exceeded"
              )),
              this.pause(),
              !1)
            );
          }),
          (a.prototype.popFrame = function () {
            var e = this.callStack[--this.depth];
            return e && (e.paused = !1), e;
          }),
          (a.prototype.pause = function () {
            return (this.paused = this.callStack[this.depth].paused = !0);
          }),
          (a.prototype.resume = function (e) {
            if (
              ((this.timeout = null != e ? e : -1),
              (this.paused = !1),
              (this.callStack[this.depth].paused = !1),
              this.run(),
              !this.paused)
            )
              return this.rexp;
          }),
          (a.prototype.timedOut = function () {
            return 0 === this.timeout;
          }),
          (a.prototype.send = function (e) {
            return this.callStack[this.depth].evalStack.push(e);
          }),
          (a.prototype.done = function () {
            return -1 === this.depth;
          }),
          a);
      function a(e, t) {
        (this.realm = e),
          (this.timeout = null != t ? t : -1),
          (this.maxDepth = 1e3),
          (this.maxTraceDepth = 50),
          (this.callStack = []),
          (this.evalStack = null),
          (this.depth = -1),
          (this.yielded = this.rv = void 0),
          (this.paused = !1),
          (this.r1 = this.r2 = this.r3 = null),
          (this.rexp = null);
      }
      (c.prototype.run = function () {
        for (
          var e = this.script.instructions;
          this.ip !== this.exitIp && !this.paused;

        )
          e[this.ip++].exec(this, this.evalStack, this.scope, this.realm);
        var t = this.evalStack.len();
        if (!this.paused && !this.error && 0 !== t)
          throw new Error(
            "Evaluation stack has " + t + " items after execution"
          );
      }),
        (c.prototype.done = function () {
          return this.ip === this.exitIp;
        }),
        (c.prototype.setLine = function (e) {
          this.line = e;
        }),
        (c.prototype.setColumn = function (e) {
          this.column = e;
        });
      var s = c;
      function c(e, t, n, r, o, i) {
        (this.fiber = e),
          (this.script = t),
          (this.scope = n),
          (this.realm = r),
          (this.fname = o),
          (this.construct = null != i && i),
          (this.evalStack = new l(this.script.stackSize, this.fiber)),
          (this.ip = 0),
          (this.exitIp = this.script.instructions.length),
          (this.paused = !1),
          (this.finalizer = null),
          (this.guards = []),
          (this.rv = void 0),
          (this.line = this.column = -1);
      }
      (u.prototype.push = function (e) {
        if (this.idx === this.array.length)
          throw new Error("maximum evaluation stack size exceeded");
        return (this.array[this.idx++] = e);
      }),
        (u.prototype.pop = function () {
          return this.array[--this.idx];
        }),
        (u.prototype.top = function () {
          return this.array[this.idx - 1];
        }),
        (u.prototype.len = function () {
          return this.idx;
        }),
        (u.prototype.clear = function () {
          return (this.idx = 0);
        });
      var l = u;
      function u(e, t) {
        (this.fiber = t), (this.array = new Array(e)), (this.idx = 0);
      }
      (d.prototype.get = function (e) {
        return this.data[e];
      }),
        (d.prototype.set = function (e, t) {
          return (this.data[e] = t);
        }),
        (d.prototype.name = function (t) {
          var n,
            r = this.names;
          for (n in r) if (e.call(r, n) && r[n] === t) return parseInt(n);
          return -1;
        });
      var p = d;
      function d(e, t, n) {
        (this.parent = e), (this.names = t), (this.data = new Array(n));
      }
      (h.prototype.get = function (e) {
        return this.object[e];
      }),
        (h.prototype.set = function (e, t) {
          return (this.object[e] = t);
        }),
        (h.prototype.has = function (e) {
          return e in this.object;
        });
      var f = h;
      function h(e, t) {
        (this.parent = e), (this.object = t);
      }
      (t.Fiber = i), (t.Scope = p), (t.WithScope = f);
    }).call(this);
  },
  function (e, t, n) {
    (n = new (n(5))()).eval(
      '["<script>",0,[[22]8false,15,null17]anonymous[,4,3163152,14[30721[,"$encode"1[8getCatVersi76,2049753379577389,88791625994-6-439,56919,018-52916763404582,-3064843563"O2true[o,8bjectjmpOnw_ms"04D"w2KsGuard6"tDeviceInfo,Ob2sa_2hsubsh2b_xc_Si,_M"rdom70t"floor[0bx28EDpro1"tonJSONingify68mes"a7[l"vjcpiu_"lg"xtupR"rpEHUDR4B54un0$HE"keyxf_nautnnipureprvsrlg"t8Aa54[toS0"[53"I""1wxNYwyaAEhW5J.0.2]'
    ),
      (e.exports = n);
  },
  function (e, t, n) {
    (function (t) {
      function r(e) {
        return (r =
          "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
            ? function (e) {
                return typeof e;
              }
            : function (e) {
                return e &&
                  "function" == typeof Symbol &&
                  e.constructor === Symbol &&
                  e !== Symbol.prototype
                  ? "symbol"
                  : typeof e;
              })(e);
      }
      (function (o) {
        var i = n(6),
          a = n(8),
          s = n(3).Fiber,
          c = n(11),
          l = n(13),
          u = n(14);
        function p(e) {
          (this.realm = new i(e)),
            (this.realm.global.startupRandom = new Date().getTime()),
            (this.realm.global.count = 100),
            new l().register(),
            new u().register(),
            ("object" !==
              ("undefined" == typeof window ? "undefined" : r(window)) &&
              "object" !== (void 0 === t ? "undefined" : r(t))) ||
              ((e = n(15)), (this.realm.global.KsGuard = new e.default()));
        }
        (p.prototype.eval = function (e, t) {
          (e = new c().unzip(e)),
            this.run(p.fromJSON(JSON.parse(e)), t),
            (this.realm.global.startupEnd = new Date().getTime());
        }),
          (p.prototype.run = function (e, t) {
            if (((e = this.createFiber(e, t)).run(), !e.paused)) return e.rexp;
          }),
          (p.prototype.call = function (e, n) {
            var r = window || t;
            if ("$encode" === e)
              try {
                throw new Error();
              } catch (i) {
                var o = i.stack.length;
                r &&
                  (r.SECS = {
                    s: 100 < o ? i.stack.substr(o - 100, 100) : i.stack,
                    c: this.realm.global.count,
                  });
              }
            return this.realm.global[e].apply(this, n);
          }),
          (p.prototype.createFiber = function (e, t) {
            return (
              (t = new s(this.realm, t)).pushFrame(e, this.realm.global), t
            );
          }),
          (p.fromJSON = a.fromJSON),
          (e.exports = p);
      }).call(this);
    }).call(this, n(1));
  },
  function (e, t, n) {
    (function (t) {
      function r(e) {
        return (r =
          "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
            ? function (e) {
                return typeof e;
              }
            : function (e) {
                return e &&
                  "function" == typeof Symbol &&
                  e.constructor === Symbol &&
                  e !== Symbol.prototype
                  ? "symbol"
                  : typeof e;
              })(e);
      }
      (function () {
        var o,
          i = {}.hasOwnProperty,
          a = (o = n(0)).prototypeOf,
          s = o.hasProp,
          c = (o = n(2)).ArrayIterator,
          l = o.StopIteration;
        function u(e) {
          var n,
            o,
            u = {
              isBrowser: "undefined" == typeof window,
              window: "undefined" == typeof window ? t : window,
              localStorage:
                "object" ===
                ("undefined" == typeof localStorage
                  ? "undefined"
                  : r(localStorage))
                  ? localStorage
                  : {},
              sessionStorage:
                "object" ===
                ("undefined" == typeof sessionStorage
                  ? "undefined"
                  : r(sessionStorage))
                  ? sessionStorage
                  : {},
              document:
                "object" ===
                ("undefined" == typeof document ? "undefined" : r(document))
                  ? document
                  : {},
              undefined: void 0,
              Object: Object,
              Function: Function,
              Number: Number,
              Boolean: Boolean,
              String: String,
              Array: Array,
              Int8Array: Int8Array,
              Int32Array: Int32Array,
              Uint8Array: Uint8Array,
              Date: Date,
              RegExp: RegExp,
              Error: Error,
              StopIteration: l,
              Math: Math,
              JSON: JSON,
              encodeURIComponent: encodeURIComponent,
              unescape: unescape,
              escape: escape,
              decodeURIComponent: decodeURIComponent,
              isNaN: isNaN,
              Infinity: 1 / 0,
              NaN: NaN,
              parseInt: parseInt,
              parseFloat: parseFloat,
              isFinite: isFinite,
              encodeURI: encodeURI,
              decodeURI: decodeURI,
              TypeError: TypeError,
              URIError: URIError,
              SyntaxError: SyntaxError,
              ReferenceError: ReferenceError,
              RangeError: RangeError,
              EvalError: EvalError,
              eval: eval,
              console: console,
            };
          for (n in ((this.has = function (e, t) {
            return null != e && (!!s(e, t) || this.has(a(e), t));
          }),
          (this.get = function (e, t) {
            return null == e ? void 0 : e[t];
          }),
          (this.set = function (e, t, n) {
            var o = r(e);
            return ("object" === o || "function" === o) && (e[t] = n), n;
          }),
          (this.del = function (e, t) {
            var n = r(e);
            return ("object" !== n && "function" !== n) || delete e[t];
          }),
          (this.instanceOf = function (e, t) {
            var n;
            return (
              null != t &&
              ("object" === (n = r(t)) || "function" === n) &&
              t instanceof e
            );
          }),
          (this.enumerateKeys = function (e) {
            var t,
              n = [];
            for (t in e) "__mdid__" !== t && n.push(t);
            return new c(n);
          }),
          e))
            i.call(e, n) && ((o = e[n]), (u[n] = o));
          this.global = u;
        }
        (u.prototype.inv = function (e) {
          return -e;
        }),
          (u.prototype.lnot = function (e) {
            return !e;
          }),
          (u.prototype.ladd = function (e) {
            return +e;
          }),
          (u.prototype.not = function (e) {
            return ~e;
          }),
          (u.prototype.inc = function (e) {
            return ++e;
          }),
          (u.prototype.dec = function (e) {
            return e - 1;
          }),
          (u.prototype.add = function (e, t) {
            return t + e;
          }),
          (u.prototype.sub = function (e, t) {
            return t - e;
          }),
          (u.prototype.mul = function (e, t) {
            return t * e;
          }),
          (u.prototype.div = function (e, t) {
            return t / e;
          }),
          (u.prototype.mod = function (e, t) {
            return t % e;
          }),
          (u.prototype.shl = function (e, t) {
            return t << e;
          }),
          (u.prototype.sar = function (e, t) {
            return t >> e;
          }),
          (u.prototype.shr = function (e, t) {
            return t >>> e;
          }),
          (u.prototype.or = function (e, t) {
            return t | e;
          }),
          (u.prototype.and = function (e, t) {
            return t & e;
          }),
          (u.prototype.xor = function (e, t) {
            return t ^ e;
          }),
          (u.prototype.ceq = function (e, t) {
            return t == e;
          }),
          (u.prototype.cneq = function (e, t) {
            return t != e;
          }),
          (u.prototype.cid = function (e, t) {
            return t === e;
          }),
          (u.prototype.cnid = function (e, t) {
            return t !== e;
          }),
          (u.prototype.lt = function (e, t) {
            return t < e;
          }),
          (u.prototype.lte = function (e, t) {
            return t <= e;
          }),
          (u.prototype.gt = function (e, t) {
            return e < t;
          }),
          (u.prototype.gte = function (e, t) {
            return e <= t;
          }),
          (e.exports = u);
      }).call(this);
    }).call(this, n(1));
  },
  function (e, t, n) {
    var r = n(0).isArray,
      o = function e(t, n) {
        null == n && (n = ""), (n += "    ");
        for (var o = "", i = 0; i < t.length; i++) {
          var a,
            s,
            c,
            l = t[i];
          r(l)
            ? (o = (o += "\n\n" + n + "Rethrown:") + e(l, n))
            : ((a = l.line),
              (s = l.column),
              (c = l.at.name),
              (l = l.at.filename),
              (o += c
                ? "\n" + n + "at " + c + " (" + l + ":" + a + ":" + s + ")"
                : "\n" + n + "at " + l + ":" + a + ":" + s));
        }
        return o;
      };
    function i(e) {
      (this.trace = null), (this.message = e);
    }
    (i.prototype.toString = function () {
      var e = this.constructor.display + ": " + this.message;
      return this.trace && (e += o(this.trace)), e;
    }),
      (i.prototype.stackTrace = function () {
        return this.toString();
      }),
      (t.VmError = i);
  },
  function (e, t, n) {
    (function () {
      var t = n(9),
        r = function (e) {
          for (var n = [], r = 0; r < e.length; r++) {
            for (
              var o = e[r], i = t[o[0]], a = [], s = 1, c = 1, l = o.length;
              1 <= l ? c < l : l < c;
              s = 1 <= l ? ++c : --c
            )
              a.push(o[s]);
            (i = new i(a.length ? a : null)), n.push(i);
          }
          return n;
        },
        o = function (e) {
          var t = e.lastIndexOf("/"),
            n = e.slice(0, t);
          t = e.slice(t + 1);
          return new RegExp(n, t);
        },
        i =
          ((a.fromJSON = function e(t) {
            for (var n = r(t[2]), a = [], s = t[3], c = 0; c < s.length; c++) {
              var l = s[c];
              a.push(e(l));
            }
            for (
              var u = t[4], p = u.length, d = [], f = t[5], h = 0;
              h < f.length;
              h++
            ) {
              var g = f[h];
              d.push({
                start: -1 !== g[0] ? g[0] : null,
                handler: -1 !== g[1] ? g[1] : null,
                finalizer: -1 !== g[2] ? g[2] : null,
                end: -1 !== g[3] ? g[3] : null,
              });
            }
            for (
              var m = t[6], y = t[7], v = [], b = t[8], _ = 0;
              _ < b.length;
              _++
            ) {
              var S = b[_];
              v.push(o(S));
            }
            return new i(null, null, n, a, u, p, d, m, y, v, null);
          }),
          a);
      function a(e, t, n, r, o, i, a, s, c, l, u) {
        (this.filename = e),
          (this.name = t),
          (this.instructions = n),
          (this.scripts = r),
          (this.localNames = o),
          (this.localLength = i),
          (this.guards = a),
          (this.stackSize = s),
          (this.strings = c),
          (this.regexps = l),
          (this.source = u);
      }
      e.exports = i;
    }).call(this);
  },
  function (module, exports, __webpack_require__) {
    function _typeof(e) {
      return (_typeof =
        "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
          ? function (e) {
              return typeof e;
            }
          : function (e) {
              return e &&
                "function" == typeof Symbol &&
                e.constructor === Symbol &&
                e !== Symbol.prototype
                ? "symbol"
                : typeof e;
            })(e);
    }
    (function () {
      var ref = __webpack_require__(2),
        StopIteration = ref.StopIteration,
        ref1 = __webpack_require__(0);
      ref1.defProp;
      var hasProp = ref1.hasProp,
        ref3 = __webpack_require__(3),
        Fiber = ref3.Fiber,
        Scope = ref3.Scope,
        WithScope = ref3.WithScope,
        OpcodeClassFactory = (function () {
          var e = 0;
          return function (t, n, r) {
            var o;
            return (
              ((o = function (e) {
                e && (this.args = e);
              }).prototype.id = e++),
              (o.prototype.exec = n),
              (o.prototype.calculateFactor =
                r ||
                function () {
                  return 2;
                }),
              o
            );
          };
        })();
      __webpack_require__(10);
      var Op = function (e, t, n) {
          return OpcodeClassFactory(e, t, n);
        },
        opcodes = [
          new Op("", function (e, t, n) {
            return ret(e);
          }),
          new Op("", function (e, t, n) {
            return t.pop();
          }),
          new Op("", function (e, t, n) {
            return t.push(t.top());
          }),
          new Op("", function (e, t, n) {
            var r = t.pop(),
              o = t.pop();
            return t.push(r), t.push(o);
          }),
          new Op("", function (e, t, n) {
            return (e.fiber.rv = t.pop()), ret(e);
          }),
          new Op("", function (e, t) {
            return (e.paused = !0);
          }),
          new Op("", function (e, t) {
            return (e.fiber.yielded = t.pop()), e.fiber.pause();
          }),
          new Op("", function (e, t, n) {
            return throwErr(e, t.pop());
          }),
          new Op("", function (e) {
            return e.guards.push(e.script.guards[this.args[0]]);
          }),
          new Op("", function (e) {
            var t = e.guards[e.guards.length - 1];
            if (e.script.guards[this.args[0]] === t) return e.guards.pop();
          }),
          new Op("", function (e, t, n) {
            return (e.fiber.r1 = t.pop());
          }),
          new Op("", function (e, t, n) {
            return (e.fiber.r2 = t.pop());
          }),
          new Op("", function (e, t, n) {
            return (e.fiber.r3 = t.pop());
          }),
          new Op("", function (e, t, n) {
            return t.push(e.fiber.r1);
          }),
          new Op("", function (e, t, n) {
            return t.push(e.fiber.r2);
          }),
          new Op("", function (e, t, n) {
            return t.push(e.fiber.r3);
          }),
          new Op("", function (e, t, n) {
            return t.push(+e.fiber.r3);
          }),
          new Op("", function (e, t, n) {
            return (t.fiber.rexp = t.pop());
          }),
          new Op("", function (e, t, n) {
            return callm(e, 0, "iterator", t.pop());
          }),
          new Op("", function (e, t, n, r) {
            return t.push(r.enumerateKeys(t.pop()));
          }),
          new Op("", function (e, t, n) {
            if (
              (callm(e, 0, "next", t.pop()), e.error instanceof StopIteration)
            )
              return (e.error = null), (e.paused = !1), (e.ip = this.args[0]);
          }),
          new Op("", function (e, t, n) {
            if ((n.set(1, t.pop()), (t = t.pop()), this.args[0]))
              return n.set(2, t);
          }),
          new Op("", function (e, t, n, r) {
            return t.push(r.global);
          }),
          new Op("", function (e, t, n, r) {
            var o = this.args[0],
              i = this.args[1],
              a = n.get(1);
            if (o < a.length) return n.set(i, Array.prototype.slice.call(a, o));
          }),
          new Op("", function (e, t, n) {
            return call(e, this.args[0], t.pop(), null, null, !0);
          }),
          new Op("", function (e, t, n) {
            return call(e, this.args[0], t.pop(), null, this.args[1]);
          }),
          new Op("", function (e, t, n) {
            return callm(e, this.args[0], t.pop(), t.pop(), this.args[1]);
          }),
          new Op("", function (e, t, n, r) {
            var o = t.pop(),
              i = t.pop();
            return null == o
              ? throwErr(
                  e,
                  new TypeError("Cannot read property '" + i + "' of " + o)
                )
              : "function" == typeof o &&
                "length" === i &&
                void 0 !== o.originFnLength
              ? t.push(r.get(o, "originFnLength"))
              : t.push(r.get(o, i));
          }),
          new Op("", function (e, t, n, r) {
            var o = t.pop(),
              i = t.pop(),
              a = t.pop();
            return null == o
              ? throwErr(
                  e,
                  new TypeError("Cannot set property '" + i + "' of " + o)
                )
              : Object.isExtensible(o) || "__proto__" !== i
              ? t.push(r.set(o, i, a))
              : throwErr(
                  e,
                  new Error(
                    "#<Object> is not extensible at set __proto__[as __proto__]"
                  )
                );
          }),
          new Op("", function (e, t, n, r) {
            var o = t.pop(),
              i = t.pop();
            return null == o
              ? throwErr(e, new Error("Cannot convert null to object"))
              : t.push(r.del(o, i));
          }),
          new Op("", function (e, t, n) {
            try {
              for (var r = this.args[0], o = this.args[1], i = n; r--; )
                i = i.parent;
              return t.push(i.get(o));
            } catch (a) {
              return throwErr(e, a);
            }
          }),
          new Op("", function (e, t, n) {
            for (var r = this.args[0], o = this.args[1], i = n; r--; )
              i = i.parent;
            return t.push(i.set(o, t.pop()));
          }),
          new Op("", function (e, t, n, r) {
            try {
              for (var o, i = this.args[0]; n instanceof WithScope; ) {
                if (n.has(i)) return t.push(n.get(i));
                n = n.parent;
              }
              for (; n instanceof Scope; ) {
                if (0 <= (o = n.name(i))) return t.push(n.get(o));
                n = n.parent;
              }
              return hasProp(r.global, i) || this.args[1]
                ? t.push(r.global[i])
                : throwErr(e, new Error(i + " is not defined"));
            } catch (a) {
              return throwErr(e, a);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              for (
                var o, i = this.args[0], a = t.pop();
                n instanceof WithScope;

              ) {
                if (n.has(i)) return t.push(n.set(i, a));
                n = n.parent;
              }
              for (; n instanceof Scope; ) {
                if (0 <= (o = n.name(i))) return t.push(n.set(o, a));
                n = n.parent;
              }
              return t.push((r.global[i] = a));
            } catch (s) {
              return throwErr(e, s);
            }
          }),
          new Op("", function (e, t, n, r) {
            return hasProp(r.global, this.args[0]) || this.args[1]
              ? t.push(r.global[this.args[0]])
              : "this" === this.args[0]
              ? t.push(r.global)
              : throwErr(e, new Error(this.args[0] + " is not defined"));
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push((r.global[this.args[0]] = t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e) {
            return (e.scope = new Scope(
              e.scope,
              e.script.localNames,
              e.script.localLength
            ));
          }),
          new Op("", function (e) {
            return (e.scope = e.scope.parent);
          }),
          new Op("", function (e, t) {
            return (e.scope = new WithScope(e.scope, t.pop()));
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.inv(t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.lnot(t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.ladd(t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.not(t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.inc(t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.dec(t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.add(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.sub(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.mul(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.div(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.mod(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.shl(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.sar(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.shr(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.or(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.and(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.xor(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.ceq(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.cneq(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.cid(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.cnid(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.lt(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.lte(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.gt(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.gte(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.has(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(r.instanceOf(t.pop(), t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t, n, r) {
            try {
              return t.push(_typeof(t.pop()));
            } catch (o) {
              return throwErr(e, o);
            }
          }),
          new Op("", function (e, t) {
            return t.pop(), t.push(void 0);
          }),
          new Op("", function (e, t, n) {
            return (e.ip = this.args[0]);
          }),
          new Op("", function (e, t, n) {
            if (t.pop()) return (e.ip = this.args[0]);
          }),
          new Op("", function (e, t, n) {
            if (!t.pop()) return (e.ip = this.args[0]);
          }),
          new Op("", function (e, t) {
            return t.push(void 0);
          }),
          new Op("", function (e, t, n) {
            return t.push(this.args[0]);
          }),
          new Op("", function (e, t, n) {
            return t.push(this.args[0] ? 1 / 0 : -1 / 0);
          }),
          new Op("", function (e, t, n) {
            return t.push(NaN);
          }),
          new Op("", function (e, t, n) {
            return t.push(-0);
          }),
          new Op("", function (e, t, n) {
            return t.push(e.script.strings[this.args[0]]);
          }),
          new Op("", function (e, t, n, r) {
            return t.push(e.script.regexps[this.args[0]], r);
          }),
          new Op("", function (e, t, n, r) {
            for (var o = this.args[0], i = {}; o--; ) {
              var a = t.pop(),
                s = t.pop();
              i.hasOwnProperty(a) || r.set(i, a, s);
            }
            return t.push(i);
          }),
          new Op("", function (e, t, n, r) {
            for (var o = this.args[0], i = new Array(o); o--; ) i[o] = t.pop();
            return t.push(i);
          }),
          new Op("", function (e, t, n, r) {
            var o = this.args[0];
            return t.push(
              createFunction(e.script.scripts[o], n, r, this.args[1])
            );
          }),
          new Op("", function (e) {
            return e.setLine(this.args[0]);
          }),
          new Op("", function (e) {
            return e.setColumn(this.args[0]);
          }),
          new Op("", function (e, t, n) {
            return debug();
          }),
        ],
        callm = function (e, t, n, r, o) {
          var i,
            a = e.evalStack,
            s = e.realm;
          return null == r
            ? throwErr(
                e,
                new Error(
                  "Cannot call method '" +
                    n +
                    "' of " +
                    (void 0 === r ? "undefined" : "null")
                )
              )
            : ((i = r.constructor.name || "Object"),
              (s = s.get(r, n)) instanceof Function
                ? call(e, t, s, r)
                : null == s
                ? (a.pop(),
                  throwErr(
                    e,
                    new Error("Object #<" + i + "> has no method '" + n + "'")
                  ))
                : (a.pop(),
                  throwErr(
                    e,
                    new Error(
                      "Property '" +
                        n +
                        "' of object #<" +
                        i +
                        "> is not a function"
                    )
                  )));
        },
        call = function (e, t, n, r, o, i) {
          if ("function" != typeof n)
            return throwErr(e, new Error("object is not a function"));
          for (
            var a = e.evalStack,
              s = e.fiber,
              c = e.realm,
              l = {
                length: t,
                callee: n,
              };
            t;

          )
            l[--t] = a.pop();
          (r = void 0 === r ? c.global : r),
            (l = Array.prototype.slice.call(l));
          try {
            var u = i ? createNativeInstance(n, l) : n.apply(r, l);
            if (!s.paused) return a.push(u);
          } catch (p) {
            throwErr(e, p);
          }
        },
        createFunction = function (e, t, n, r, o) {
          var i;
          return (
            ((i = function r() {
              var o,
                i,
                a,
                s = !1;
              if (
                ((i = r.__fiber__)
                  ? ((i.callStack[i.depth].paused = !0),
                    (r.__fiber__ = null),
                    (o = r.__construct__),
                    (r.__construct__ = null))
                  : ((i = new Fiber(n)), (s = !0)),
                (a = r.__callname__ || e.name),
                (r.__callname__ = null),
                i.pushFrame(e, this, t, arguments, r, a, o),
                s)
              )
                return i.run(), i.rv;
            }).originFnLength = o),
            i
          );
        },
        callArrayConstructor = function (e) {
          return 1 === e.length && (0 | e[0]) === e[0]
            ? new Array(e[0])
            : e.slice();
        },
        callRegExpConstructor = function (e) {
          return 1 === e.length ? new RegExp(e[0]) : new RegExp(e[0], e[1]);
        },
        createNativeInstance = function (e, t) {
          var n;
          return e === Array
            ? callArrayConstructor(t)
            : e === Date
            ? 0 === t.length
              ? new Date()
              : new Date(t[0])
            : e === RegExp
            ? callRegExpConstructor(t)
            : e === Number
            ? 0 === t.length
              ? new Number()
              : new Number(t[0])
            : e === Boolean
            ? 0 === t.length
              ? new Boolean()
              : new Boolean(t[0])
            : e === Uint8Array
            ? new Uint8Array(t[0])
            : e === Int8Array
            ? new Int8Array(t[0])
            : e === Int32Array
            ? new Int32Array(t[0])
            : e === String
            ? new String(t[0] || "")
            : (((n = function () {
                return e.apply(this, t);
              }).prototype = e.prototype),
              new n());
        },
        ret = function (e) {
          return e.evalStack.clear(), (e.exitIp = e.ip);
        },
        throwErr = function (e, t) {
          return (e.error = t), (e.paused = !0);
        },
        debug = function debug() {
          eval("debugger;");
        };
      module.exports = opcodes;
    }).call(this);
  },
  function (e, t) {
    e.exports = function (e, t) {
      (this.__proto__ = RegExp.prototype),
        Object.defineProperties(this, {
          global: {
            value: e.global,
          },
          ignoreCase: {
            value: e.ignoreCase,
          },
          multiline: {
            value: e.multiline,
          },
          source: {
            value: e.source,
          },
          hasIndices: {
            value: e.hasIndices,
          },
          dotAll: {
            value: e.dotAll,
          },
          flags: {
            value: e.flags,
          },
          sticky: {
            value: e.sticky,
          },
          unicode: {
            value: e.unicode,
          },
        });
    };
  },
  function (e, t, n) {
    var r, o;
    (o = n(12)),
      ((r = function () {}).prototype.zip = function (e) {
        return o.encode(e);
      }),
      (r.prototype.unzip = function (e) {
        return o.decode(e);
      }),
      (e.exports = r);
  },
  function (e, t) {
    function n(e, t) {
      (null == t || t > e.length) && (t = e.length);
      for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
      return r;
    }
    (e.exports.encode = function (e) {
      try {
        var t,
          n = {},
          r = [],
          o = e[0],
          i = 57344;
        e = (e + "").split("");
        for (var a = 1; a < e.length; a++)
          null != n[o + (t = e[a])] && o + t !== "toString"
            ? (o += t)
            : (r.push(1 < o.length ? n[o] : o.codePointAt(0)),
              (n[o + t] = i),
              i++,
              (o = t));
        return (
          r.push(1 < o.length ? n[o] : o.codePointAt(0)),
          r
            .map(function (e) {
              return String.fromCodePoint(e);
            })
            .join("")
        );
      } catch (s) {
        throw new Error(s);
      }
    }),
      (e.exports.decode = function (e) {
        try {
          for (
            var t = (function (e) {
                return (
                  (function (e) {
                    if (Array.isArray(e)) return n(e);
                  })(e) ||
                  (function (e) {
                    if (
                      ("undefined" != typeof Symbol &&
                        null != e[Symbol.iterator]) ||
                      null != e["@@iterator"]
                    )
                      return Array.from(e);
                  })(e) ||
                  (function (e, t) {
                    var r;
                    if (e)
                      return "string" == typeof e
                        ? n(e, t)
                        : "Map" ===
                            (r =
                              "Object" ===
                                (r = Object.prototype.toString
                                  .call(e)
                                  .slice(8, -1)) && e.constructor
                                ? e.constructor.name
                                : r) || "Set" === r
                        ? Array.from(e)
                        : "Arguments" === r ||
                          /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)
                        ? n(e, t)
                        : void 0;
                  })(e) ||
                  (function () {
                    throw new TypeError(
                      "Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
                    );
                  })()
                );
              })(e).map(function (e) {
                return e.codePointAt(0);
              }),
              r = {},
              o = String.fromCodePoint(t[0]),
              i = o,
              a = [o],
              s = 57344,
              c = 1;
            c < t.length;
            c++
          ) {
            var l,
              u = t[c];
            (a += l = u < 57344 ? String.fromCodePoint(t[c]) : r[u] || i + o),
              (o = l[0]),
              (r[s] = i + o),
              s++,
              (i = l);
          }
          return a;
        } catch (p) {
          throw new Error(p);
        }
      });
  },
  function (e, t) {
    function n(e) {
      return (n =
        "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
          ? function (e) {
              return typeof e;
            }
          : function (e) {
              return e &&
                "function" == typeof Symbol &&
                e.constructor === Symbol &&
                e !== Symbol.prototype
                ? "symbol"
                : typeof e;
            })(e);
    }
    var r,
      o,
      i,
      a,
      s,
      c,
      l,
      u,
      p,
      d,
      f,
      h,
      g,
      m,
      y,
      v,
      b,
      _,
      S,
      w,
      E = 168;
    function $(e, t) {
      return ((e >>> t) & 4294967295) | ((e << (32 - t)) & 4294967295);
    }
    function O(e, t, n, r, o, i, a) {
      (e[t] = (e[t] + e[n]) & 4294967295),
        (e[t] = (e[t] + i) & 4294967295),
        (e[o] ^= e[t]),
        (e[o] = $(e[o], 16)),
        (e[r] = (e[r] + e[o]) & 4294967295),
        (e[n] ^= e[r]),
        (e[n] = $(e[n], 12)),
        (e[t] = (e[t] + e[n]) & 4294967295),
        (e[t] = e[t] + a),
        (e[o] ^= e[t]),
        (e[o] = $(e[o], 8)),
        (e[r] = (e[r] + e[o]) & 4294967295),
        (e[n] ^= e[r]),
        (e[n] = $(e[n], 7));
    }
    function k(e, t, n, r, i, s, c) {
      var l, u, p;
      for (l = new Array(16), u = new Array(16); ; ) {
        for (p = 0; p < 16; ++p) u[p] = 0;
        break;
      }
      for (; (45 ^ E) % 24 < 14; ) {
        for (p = 0; p < 8; p++) l[p] = e[p];
        break;
      }
      for (; 11 < (35 ^ E) % 14; ) {
        for (p = 0; p < 8; p++) l[p + 8] = 4294967295 & c[p];
        break;
      }
      for (l[12] ^= r, s && (l[14] ^= 4294967295), p = 0; p < i; p++)
        u[p % 16] ^= t[n + p];
      for (; (33 ^ E) % 7 < 7; ) {
        for (p = 0; p < a; p++) {
          for (
            O(l, 0, 4, 8, 12, u[o[p][0]], u[o[p][1]]),
              O(l, 1, 5, 9, 13, u[o[p][2]], u[o[p][3]]),
              O(l, 2, 6, 10, 14, u[o[p][4]], u[o[p][5]]),
              O(l, 3, 7, 11, 15, u[o[p][6]], u[o[p][7]]),
              O(l, 0, 5, 10, 15, u[o[p][8]], u[o[p][9]]),
              O(l, 1, 6, 11, 12, u[o[p][10]], u[o[p][11]]);
            (36 ^ E) % 19 < 11;

          ) {
            O(l, 2, 7, 8, 13, u[o[p][12]], u[o[p][13]]);
            break;
          }
          O(l, 3, 4, 9, 14, u[o[p][14]], u[o[p][15]]);
        }
        break;
      }
      for (;;) {
        for (p = 0; p < 8; p++) e[p] ^= l[p] ^ l[p + 8];
        break;
      }
      for (;;) return e;
    }
    function T(e) {
      return (
        (e = unescape(encodeURIComponent(e))),
        new Int8Array(
          e.split("").map(function (e) {
            return e.charCodeAt(0) || 0;
          })
        )
      );
    }
    function P(e, t) {
      for (var n = ""; ; ) {
        for (var r = 0; r < t; r++) n += e;
        break;
      }
      for (;;) return n;
    }
    function A(e, t) {
      for (var n, r = 0; ; ) {
        n = new Int8Array(e.length);
        break;
      }
      for (; r < e.length; )
        for (var o = 0; o < t.length; o++) (n[r] = e[r] ^ (255 & t[o])), r++;
      return n;
    }
    function I(e) {
      var t;
      return (
        (t = parseInt(e, 16)),
        (e = Math.pow(2, (e.length / 2) * 8)) / 2 - 1 < t && (t -= e),
        t
      );
    }
    function x(e) {
      for (var t = []; 183 < (17 | E); ) {
        for (var n = 0; n < e.length; n += 2) t.push(I(e.substr(n, 2)));
        break;
      }
      return t;
    }
    function R(e, t) {
      var n;
      for (t = t || 4, n = []; 19 < (23 ^ E) % 34; ) {
        if (4 < t && 4294967295 < e)
          for (
            var r = e.toString(2),
              o = parseInt(r.substr(0, r.length - 16), 2),
              i = 0;
            i <= t - 1;
            i++
          )
            n[i] =
              0 === i || 1 === i
                ? parseInt(r.substr(r.length - 8 * (i + 1), 8), 2)
                : (o >>> (8 * (i - 2))) & 255;
        else for (i = 0; i <= t - 1; i++) n[i] = (e >>> (8 * i)) & 255;
        break;
      }
      return n;
    }
    function C(e, t, n) {
      var r = "",
        o = [];
      for (
        o = (
          t
            ? function (e, t) {
                var n;
                if (((t = t || 4), (n = []), (37 ^ E) % 3 < 1))
                  for (var r = t - 1; 0 <= r; r--)
                    n[t - 1 - r] = (e >>> (8 * r)) & 255;
                return n;
              }
            : R
        )(e, n);
        ;

      ) {
        for (var i = 0; i < o.length; i++)
          r += 0 === o[i] ? "00" : (o[i] < 16 ? "0" : "") + o[i].toString(16);
        break;
      }
      return r;
    }
    function N(e) {
      for (var t, n = "", r = 0; r < e.length; r++) {
        for (; (39 ^ E) % 11 < 5; ) {
          t =
            255 & e[r]
              ? ((255 & e[r]) < 16 ? "0" : "") + (255 & e[r]).toString(16)
              : "00";
          break;
        }
        for (;;) {
          n += t;
          break;
        }
      }
      for (;;) return n;
    }
    function L() {
      return "e0000000000000";
    }
    function M(e) {
      return (function (e) {
        var t;
        for (
          t = "",
            e.forEach(function (e) {
              var n, r;
              for (r = 8 - (n = (e >>> 0).toString(16)).length; ; ) {
                t += 0 < r ? P("0", r) + n : n;
                break;
              }
            });
          ;

        )
          return t;
      })(
        (function (e) {
          var t, n, r, o;
          for (
            t = 0, n = e.length, r = 0, (o = i.slice())[0] ^= 16842784;
            64 < n;

          )
            (n -= 64), k(o, e, r, (t += 64), 64, !1, i), (r += 64);
          for (t += n; ; ) return k(o, e, r, t, n, !0, i);
        })(
          (function (e) {
            for (
              var t,
                n = T(e),
                r =
                  ((e = n.length % 4 == 0 ? 0 : 4 - (n.length % 4)),
                  new Int8Array(n.length + e)),
                o = 0;
              o < n.length;
              ++o
            )
              r[o] = n[o];
            for (t = new Array(r.length / 4); ; ) {
              for (o = 0; o < r.length; o += 4)
                t[o / 4] = new Int32Array(r.slice(o, o + 4).buffer)[0];
              break;
            }
            return t;
          })(e)
        )
      );
    }
    function j(e) {
      return T(M(e));
    }
    function D(e) {
      for (;;) {
        (c = new Int32Array(_.slice(12, 16).buffer)[0]),
          (l = new Int32Array(_.slice(8, 12).buffer)[0]),
          (u = new Int32Array(_.slice(4, 8).buffer)[0]),
          (p = new Int32Array(_.slice(0, 4).buffer)[0]),
          (d = new Int32Array(_.slice(16, 20).buffer)[0]),
          (f = new Int32Array(_.slice(20, 24).buffer)[0]),
          (h = new Int32Array(_.slice(24, 28).buffer)[0]),
          (g = new Int32Array(_.slice(28, 32).buffer)[0]),
          (m = new Int32Array(_.slice(44, 48).buffer)[0]),
          (y = new Int32Array(_.slice(40, 44).buffer)[0]),
          (v = new Int32Array(_.slice(36, 40).buffer)[0]),
          (b = new Int32Array(_.slice(32, 36).buffer)[0]);
        break;
      }
      !(function (e) {
        var t, n, r;
        for (
          s = e,
            n = e.length,
            e = (e = s).split("").map(function (e) {
              return e.codePointAt(0) || 0;
            }),
            r = new Int8Array(e).slice(0, n);
          (22 ^ E) % 20 < 12;

        ) {
          for (t = 0; t < 4; t++)
            (c = (c <<= 8) | r[t + 4]),
              (l = (l <<= 8) | r[t + 4]),
              (u = (u <<= 8) | r[t + 4]);
          break;
        }
        for (0 == c && (c = 324508639); (34 ^ E) % 5 < 7; ) {
          0 == l && (l = 610839776);
          break;
        }
        0 == u && (u = 4256789809);
      })("Vuz4fCHxn1CO");
      for (var t = new Int8Array(e.length), n = 0; n < e.length; n++)
        t[n] = (function (e) {
          var t, n, r, o;
          for (t = 0; ; ) {
            n = 1 & l;
            break;
          }
          if (((r = 1 & u), (19 | E) < 190))
            for (var i = 0; i < 8; i++) {
              if (1 & c)
                (c = (c ^ ((p >> 1) & 4294967295)) | y),
                  1 & l
                    ? ((l = (l ^ ((d >> 1) & 4294967295)) | v), (n = 1))
                    : ((l = (l >> 1) & 4294967295 & g), (n = 0));
              else
                for (c = (c >> 1) & 4294967295 & h; ; ) {
                  if (1 & u) (u = (u ^ ((f >> 1) & 4294967295)) | b), (r = 1);
                  else {
                    for (;;) {
                      u = (u >> 1) & 4294967295 & m;
                      break;
                    }
                    r = 0;
                  }
                  break;
                }
              for (
                o = ((t << 1) & 4294967295) | (n ^ r);
                (28 ^ E) % 28 < 14;

              ) {
                t = 127 < o ? o - 256 : o < -128 ? o + 256 : o;
                break;
              }
            }
          if (((e ^= t += 3), 7 < (66 ^ E) % 28)) return e;
        })(e[n]);
      return t;
    }
    function U(e) {
      var t, r, o, i;
      for (
        (!e || "object" !== n(e)) &&
          console.error("Type Error: data must be a object"),
          (e.url && e.query && e.form && e.requestBody) ||
            console.error("data must have url、query、form、requestBody");
        ;

      ) {
        e.query.caver || console.error("query.caver must exist!");
        break;
      }
      for (
        o = r = e.url,
          (i = new RegExp(
            /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/
          )).test(r) &&
            ((r = (i = r.split("//"))[1].indexOf("/")),
            (o = i[1].substring(r))),
          t = o = -1 != o.indexOf("?") ? o.split("?")[0] : o,
          r = (function (e) {
            return Object.keys(e).reduce(function (t, r) {
              var o;
              return (
                ~r.indexOf(S) ||
                  ("object" !== n(e[r]) || e[r] instanceof Array
                    ? ((o = r + "=" + e[r]), t.push(o))
                    : t.push((o = r + "=[object Object]"))),
                t
              );
            }, []);
          })(Object.assign({}, e.query, e.form)),
          i = ((document && document.cookie.split(";")) || [])
            .map(function (e) {
              return [
                (e = e.split("="))[0].trim(),
                e.slice(1).join("=").trim(),
              ];
            })
            .filter(function (e) {
              return ~w.indexOf(e[0]) && !!e[1];
            })
            .map(function (e) {
              return e[0] + "=" + e[1];
            }, []),
          t += r
            .concat(i)
            .sort(function (e, t) {
              for (; 10 < (44 ^ E) % 24; ) {
                if (e === t) return 0;
                break;
              }
              for (;;) return e < t ? -1 : 1;
            })
            .join("");
        (21 ^ E) % 21 < 3;

      ) {
        Object.keys(e.requestBody).length &&
          (t += JSON.stringify(e.requestBody));
        break;
      }
      return t;
    }
    function H(e, t) {
      var n, r;
      return (
        (n = e.toString(2)),
        (e = t.toString(2)),
        (n = (P("0", (t = Math.max(n.length, e.length)) - n.length) + n).split(
          ""
        )),
        (r = (P("0", t - e.length) + e).split("")),
        (t = n
          .map(function (e, t) {
            return ("0" === e && "0" === r[t]) ||
              !(("0" === e && "1" === r[t]) || ("1" === e && "0" === r[t]))
              ? "0"
              : "1";
          })
          .join("")),
        parseInt(t, 2)
      );
    }
    for (
      r = function () {},
        o = [
          [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
          [14, 10, 4, 8, 9, 15, 13, 6, 1, 12, 0, 2, 11, 7, 5, 3],
          [11, 8, 12, 0, 5, 2, 15, 13, 10, 14, 3, 6, 7, 1, 9, 4],
          [7, 9, 3, 1, 13, 12, 11, 14, 2, 6, 5, 10, 4, 0, 15, 8],
          [9, 0, 5, 7, 2, 4, 10, 15, 14, 1, 11, 12, 6, 8, 3, 13],
          [2, 12, 6, 10, 0, 11, 8, 3, 4, 13, 7, 5, 15, 14, 1, 9],
          [12, 5, 1, 15, 14, 13, 4, 10, 0, 7, 6, 3, 9, 2, 8, 11],
          [13, 11, 7, 14, 12, 1, 3, 9, 5, 0, 15, 4, 8, 6, 2, 10],
          [6, 15, 14, 9, 11, 3, 0, 8, 12, 2, 13, 7, 1, 4, 10, 5],
          [10, 2, 8, 4, 7, 6, 1, 5, 15, 11, 9, 14, 3, 12, 13, 0],
        ],
        i = [
          2837534710, 2845986804, 2436420605, 706843635, 719254516, 2557931286,
          2596197199, 2432949778,
        ];
      -3 < (63 ^ E) % 10;

    ) {
      for (var V = 0; V < 8; ++V) i[V] &= 4294967295;
      break;
    }
    for (a = 10, d = 0, g = h = f = p = u = l = c = 0; 0 < (46 ^ E) % 5; ) {
      m = 0;
      break;
    }
    for (
      y = 0,
        b = v = 0,
        _ = new Int8Array([
          98, 0, 0, 128, 49, 117, 185, 253, 224, 172, 104, 36, 223, 155, 87, 19,
          32, 0, 0, 64, 2, 0, 0, 16, 255, 255, 255, 127, 255, 255, 255, 63, 0,
          0, 0, 240, 0, 0, 0, 192, 0, 0, 0, 128, 255, 255, 255, 15,
        ]),
        S = "__NS",
        w = [],
        r.prototype.register = function () {
          Object.defineProperties(Object, {
            jmpOnw_b2sa: {
              writable: !0,
              configurable: !0,
              value: j,
            },
            jmpOnw_b2has: {
              writable: !0,
              configurable: !0,
              value: M,
            },
            jmpOnw_cts: {
              writable: !0,
              configurable: !0,
              value: D,
            },
            jmpOnw_xcb: {
              writable: !0,
              configurable: !0,
              value: A,
            },
            jmpOnw_h2b: {
              writable: !0,
              configurable: !0,
              value: x,
            },
            jmpOnw_b2h: {
              writable: !0,
              configurable: !0,
              value: N,
            },
            jmpOnw_i2h: {
              writable: !0,
              configurable: !0,
              value: C,
            },
            jmpOnw_bxor: {
              writable: !0,
              configurable: !0,
              value: H,
            },
            jmpOnw_i2b1: {
              writable: !0,
              configurable: !0,
              value: R,
            },
            jmpOnw_s2ua: {
              writable: !0,
              configurable: !0,
              value: T,
            },
            jmpOnw_geh: {
              writable: !0,
              configurable: !0,
              value: L,
            },
            jmpOnw_ms: {
              writable: !0,
              configurable: !0,
              value: U,
            },
          });
        };
      ;

    ) {
      e.exports = r;
      break;
    }
  },
  function (e, t) {
    function n(e) {
      return (n =
        "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
          ? function (e) {
              return typeof e;
            }
          : function (e) {
              return e &&
                "function" == typeof Symbol &&
                e.constructor === Symbol &&
                e !== Symbol.prototype
                ? "symbol"
                : typeof e;
            })(e);
    }
    var r,
      o = {
        prod: "log-sdk.ksapisrv.com",
        oversea: "logsdk.kwai-pro.com",
      },
      i = {
        prod: "914e7528de",
        oversea: "602a26d3bn",
      };
    function a(e) {
      try {
        var t = {
            data: [
              {
                key: "event",
                value: e.duration
                  ? {
                      duration: e.duration,
                    }
                  : {},
                dimension: {
                  event_client_timestamp: Date.now(),
                  collect_version: "1.2.9",
                  collect_name: "RadarSeedCollect",
                  name: e.name || "",
                  message:
                    "object" === n(e.value) ? JSON.stringify(e.value) : e.value,
                  category: e.keyObj.appKey || e.keyObj.appkey || "",
                  sample_rate: 1,
                  other_session_increase_id: 2,
                },
                h5_extra_attr: {
                  domain: "",
                  url: "",
                  hash: "",
                  online: !0,
                  downlink: 10,
                  rtt: 50,
                  screen_width: 1792,
                  screen_height: 1120,
                  device_pixel_ratio: 2,
                },
                refer_url_package: {
                  page: (location && location.href) || "",
                },
                url_package: {
                  page: "",
                },
                project_id: i[e.keyObj.oversea ? "oversea" : "prod"],
                radar_session_id: "",
              },
            ],
          },
          r = {
            common: {
              identity_package: {
                device_id: e.keyObj.did || "",
                global_id: "",
                user_id: e.keyObj.uid || "",
              },
              app_package: {
                language: "zh-CN",
                version_name: "",
              },
              device_package: {
                ua: "",
              },
              service_name: "radarSDK",
              network_package: {
                type: 3,
              },
              h5_extra_attr: "",
            },
            logs: [
              {
                client_timestamp: Date.now(),
                stat_package: {
                  custom_stat_event: {
                    key: "radar_log",
                    value: JSON.stringify(t),
                  },
                },
              },
            ],
          },
          a =
            "https://" +
            o[e.keyObj.oversea ? "oversea" : "prod"] +
            "/rest/wd/common/log/collect/radar";
        // navigator.sendBeacon(a, JSON.stringify(r));
      } catch (s) {
        console.log("log error", s);
      }
    }
    ((r = function () {}).prototype.register = function () {
      Object.defineProperties(Object, {
        jmpOnw_send: {
          writable: !0,
          configurable: !0,
          value: a,
        },
      });
    }),
      (e.exports = r);
  },
  function (e, t, n) {
    n.r(t),
      function (e) {
        function n(e) {
          return (n =
            "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
              ? function (e) {
                  return typeof e;
                }
              : function (e) {
                  return e &&
                    "function" == typeof Symbol &&
                    e.constructor === Symbol &&
                    e !== Symbol.prototype
                    ? "symbol"
                    : typeof e;
                })(e);
        }
        function r(e, t) {
          for (var r = 0; r < t.length; r++) {
            var o = t[r];
            (o.enumerable = o.enumerable || !1),
              (o.configurable = !0),
              "value" in o && (o.writable = !0),
              Object.defineProperty(
                e,
                ((i = (function (e, t) {
                  if ("object" != n(e) || null === e) return e;
                  var r = e[Symbol.toPrimitive];
                  if (void 0 === r) return String(e);
                  if ("object" != n((e = r.call(e, "string")))) return e;
                  throw new TypeError(
                    "@@toPrimitive must return a primitive value."
                  );
                })((i = o.key))),
                "symbol" == n(i) ? i : String(i)),
                o
              );
          }
          var i;
        }
        function o(e, t) {
          if (e !== t)
            throw new TypeError("Cannot instantiate an arrow function");
        }
        function i(e) {
          return (
            (function (e) {
              if (Array.isArray(e)) return a(e);
            })(e) ||
            (function (e) {
              if (
                ("undefined" != typeof Symbol && null != e[Symbol.iterator]) ||
                null != e["@@iterator"]
              )
                return Array.from(e);
            })(e) ||
            (function (e, t) {
              var n;
              if (e)
                return "string" == typeof e
                  ? a(e, t)
                  : "Map" ===
                      (n =
                        "Object" ===
                          (n = Object.prototype.toString
                            .call(e)
                            .slice(8, -1)) && e.constructor
                          ? e.constructor.name
                          : n) || "Set" === n
                  ? Array.from(e)
                  : "Arguments" === n ||
                    /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
                  ? a(e, t)
                  : void 0;
            })(e) ||
            (function () {
              throw new TypeError(
                "Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
              );
            })()
          );
        }
        function a(e, t) {
          (null == t || t > e.length) && (t = e.length);
          for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
          return r;
        }
        var s = function (e) {
            var t = this;
            return Array.from(e).map(
              function (e) {
                return o(this, t), e.codePointAt(0) || 0;
              }.bind(this)
            );
          },
          c = function (e) {
            var t =
              1 < arguments.length && void 0 !== arguments[1]
                ? arguments[1]
                : 4;
            if (4 <= t && e >= Math.pow(2, 32)) return [255, 255, 255, 255];
            for (var n = [], r = 0; r <= t - 1; r++)
              n[r] = (e >>> (8 * r)) & 255;
            return n;
          },
          l = "SECS";
        function u(e, t) {
          var n = 0,
            r = new Array(16),
            o = new Array(16),
            i = e,
            a = t;
          function s(e, t) {
            return ((e << t) & 4294967295) | (e >>> (32 - t));
          }
          function c(e, t, n, r, o) {
            (e[t] = (e[t] + e[n]) & 4294967295),
              (e[o] ^= e[t]),
              (e[o] = s(e[o], 16)),
              (e[r] = (e[r] + e[o]) & 4294967295),
              (e[n] ^= e[r]),
              (e[n] = s(e[n], 12)),
              (e[t] = (e[t] + e[n]) & 4294967295),
              (e[o] ^= e[t]),
              (e[o] = s(e[o], 8)),
              (e[r] = (e[r] + e[o]) & 4294967295),
              (e[n] ^= e[r]),
              (e[n] = s(e[n], 7));
          }
          function l() {
            for (var e = new Array(r.length), t = 0; t < r.length; ++t)
              e[t] = r[t];
            for (t = 0; t < 20; t += 2)
              c(e, 0, 4, 8, 12),
                c(e, 1, 5, 9, 13),
                c(e, 2, 6, 10, 14),
                c(e, 3, 7, 11, 15),
                c(e, 0, 5, 10, 15),
                c(e, 1, 6, 11, 12),
                c(e, 2, 7, 8, 13),
                c(e, 3, 4, 9, 14);
            for (t = 0; t < 16; ++t) o[t] = (e[t] + r[t]) & 4294967295;
          }
          (u.prototype.chachaEncrypt = function (e) {
            (r[(n = 0)] = 394484062),
              (r[1] = 2378328696),
              (r[2] = 630790222),
              (r[3] = 1922531795);
            for (var t = 0; t < 8; t++) r[t + 4] = i[t];
            (r[12] = 1), (r[13] = a[0]), (r[14] = a[1]), (r[15] = a[2]), l();
            for (var s = new Uint8Array(e.length), c = 0; c < e.length; c++) {
              var u =
                e[c] ^
                ((u = void 0),
                64 === n && (++r[12], l(), (n = 0)),
                (u = (o[n >> 2] >> ((3 & n) << 3)) & 255),
                ++n,
                127 < u ? u - 256 : u < -128 ? 256 + u : u);
              s[c] = u;
            }
            return s;
          }),
            (u.prototype.chachaDecrypt = u.prototype.chachaEncrypt);
        }
        for (
          var p = [],
            d =
              "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
            f = 0;
          f < 64;
          ++f
        )
          (p[f] = d[f]), d.charCodeAt(f);
        function h(e, t, n) {
          for (var r, o = [], i = t; i < n; i += 3)
            (r =
              ((e[i] << 16) & 16711680) +
              ((e[i + 1] << 8) & 65280) +
              (255 & e[i + 2])),
              o.push(
                p[(r >> 18) & 63] +
                  p[(r >> 12) & 63] +
                  p[(r >> 6) & 63] +
                  p[63 & r]
              );
          return o.join("");
        }
        (g = v),
          (m = [
            {
              key: "getInstance",
              value: function () {
                return v.instance || (v.instance = new v()), this.instance;
              },
            },
          ]),
          (y = [
            {
              key: "init",
              value: function () {
                try {
                  var e, t, n;
                  (e = this.infoCache).push.apply(
                    e,
                    [68, 0].concat(
                      i(
                        c(
                          null == (t = document) || null == (n = t.scripts)
                            ? void 0
                            : n.length,
                          4
                        )
                      )
                    )
                  );
                } catch (r) {}
              },
            },
            {
              key: "collectDeviceInfo",
              value: function () {
                var t = this,
                  n = window || e;
                try {
                  var r,
                    a,
                    d,
                    f = this.count;
                  (this.count += 1),
                    (g = [45, 61, 0, 2].concat(
                      i(this.infoCache),
                      [112, 0].concat(i(c(f || 0, 4))),
                      [114, 1].concat(
                        i(
                          c(
                            (null == (v = n[l]) || null == (r = v.s)
                              ? void 0
                              : r.length) || 0,
                            2
                          )
                        ),
                        i(s((null == (a = n[l]) ? void 0 : a.s) || ""))
                      ),
                      [115, 0].concat(
                        i(c((null == (d = n[l]) ? void 0 : d.c) || 0, 4))
                      )
                    ));
                } catch (b) {
                  var g = [45, 61, 0, 2].concat(
                    [117, 1].concat(
                      i(c(100, 2)),
                      i(
                        s(
                          (null == b || null == (f = b.stack)
                            ? void 0
                            : f.substr(0, 100)) || ""
                        )
                      )
                    )
                  );
                }
                n[l] = {
                  s: "",
                  c: 0,
                };
                for (var m = [], y = 0; y < g.length; y++) m.push(35 ^ g[y]);
                var v = (function (e) {
                  for (
                    var t, n = e.length, r = n % 3, o = [], i = 0, a = n - r;
                    i < a;
                    i += 16383
                  )
                    o.push(h(e, i, a < i + 16383 ? a : i + 16383));
                  return (
                    1 == r
                      ? ((t = e[n - 1]),
                        o.push(p[t >> 2] + p[(t << 4) & 63] + "=="))
                      : 2 == r &&
                        ((t = (e[n - 2] << 8) + e[n - 1]),
                        o.push(
                          p[t >> 10] + p[(t >> 4) & 63] + p[(t << 2) & 63] + "="
                        )),
                    o.join("")
                  );
                })(
                  new u(
                    [
                      4183807412, 394484062, 1106561997, 2378328696, 630790222,
                      2546784104, 2891127470, 1922531795,
                    ],
                    [2215853858, 1643070585, 1849059804]
                  ).chachaEncrypt(m)
                );
                return function (e) {
                  var n = this,
                    r =
                      (o(this, t),
                      {
                        "+": "-",
                        "/": "_",
                        "=": ".",
                      });
                  return e.replace(
                    /[+/=]/g,
                    function (e) {
                      return o(this, n), r[e];
                    }.bind(this)
                  );
                }.bind(this)(v);
              },
            },
          ]) && r(g.prototype, y),
          m && r(g, m),
          Object.defineProperty(g, "prototype", {
            writable: !1,
          });
        var g,
          m,
          y = v;
        function v() {
          if (this instanceof v)
            return (
              (this.count = 100),
              (this.infoCache = []),
              v.instance || (v.instance = this).init(),
              v.instance
            );
          throw new TypeError("Cannot call a class as a function");
        }
        (y.instance = null), (t.default = y);
      }.call(this, n(1));
  },
]);

console.log("Jose object type:", typeof Jose);
console.log("Jose constructor:", Jose.constructor.name);
console.log("Jose keys:", Object.keys(Jose).slice(0, 10)); // 只显示前10个键
console.log("Jose sample:", Jose);

function getSig4(customParams) {
  var params = customParams || {
    url: "/rest/wd/kconf/get",
    query: {
      caver: "2",
    },
    form: {},
    requestBody: {
      key: "frontend.browserConfig.h5ShareConfig",
      type: "json",
    },
    projectInfo: {
      did: "web_ee6adc237b8644098c1aa0b3126d25d0",
      appKey: "nuojwbmH5T",
    },
  };

  var sign4;

  Jose.call("$encode", [
    params,
    {
      suc(e, t) {
        sign4 = e;
      },
    },
  ]);

  return sign4;
}

// ES6模块导出
export { getSig4 };

// 兼容性导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { getSig4 };
} else if (typeof window !== 'undefined') {
  window.getSig4 = getSig4;
}
