"""
快手作品详情查询服务模块
负责处理 detailEnquire 接口的核心业务逻辑
"""

import asyncio
import os
import sys
import logging
import re
import uuid
import json
import traceback
import subprocess
import shutil
from pathlib import Path

# 导入项目配置
try:
    from conf import BASE_DIR
except ImportError:
    # 如果在打包环境下，BASE_DIR 应该在主模块中定义
    BASE_DIR = Path(__file__).parent.parent

from ffmpeg_config import FFMPEG_BIN, FFPROBE_BIN


class DetailEnquireService:
    """快手作品详情查询服务类"""
    
    def __init__(self, base_dir=None):
        self.base_dir = Path(base_dir) if base_dir else BASE_DIR
        self.video_dir = self.base_dir / "videoFile"
        self.video_dir.mkdir(exist_ok=True)
        
    async def process_detail_enquire(self, url, cookie, download=True, proxy='',
                                   video_generation_key='', auto_ab=False, auto_publish=False,
                                   cover_title='', custom_cover_path='',
                                   cover_font_size=72, cover_font_color='#FFFF00',
                                   cover_title_lines=None):
        """
        处理快手作品详情查询的主要逻辑

        Args:
            url: 快手视频链接
            cookie: 用户cookie
            download: 是否下载视频
            proxy: 代理设置
            video_generation_key: 视频生成密钥
            auto_ab: 是否启用AB去重
            auto_publish: 是否自动发布
            cover_title: 封面标题文字
            custom_cover_path: 自定义封面图片路径
            cover_font_size: 封面标题字体大小
            cover_font_color: 封面标题字体颜色（简单模式）
            cover_title_lines: 多行颜色配置（高级模式）
            title_input_mode: 标题输入模式（simple/advanced）

        Returns:
            dict: 处理结果
        """
        try:
            logging.info("=== DetailEnquireService 开始处理 ===")
            logging.info(f"URL: {url}")
            logging.info(f"Cookie长度: {len(cookie)}")
            logging.info(f"是否下载: {download}")
            logging.info(f"代理: {proxy if proxy else '无'}")
            logging.info(f"视频生成密钥: {'已提供' if video_generation_key else '未提供'}")
            logging.info(f"自动AB去重: {auto_ab}")
            logging.info(f"自动发布: {auto_publish}")
            logging.info(f"封面标题: {'已提供' if cover_title else '未提供'}")
            logging.info(f"自定义封面图片: {'已提供' if custom_cover_path else '未提供'}")

            # 导入 KS-Downloader
            result = await self._setup_ks_downloader_and_process(
                url, cookie, download, proxy
            ) 
            
            if isinstance(result, str):
                # 返回错误信息
                logging.error(f"KS-Downloader 执行失败: {result}")
                return {
                    "success": False,
                    "code": 500,
                    "msg": f"详情查询失败: {result}",
                    "data": None
                }
                
            elif isinstance(result, dict):
                # 成功获取作品详情
                self._log_success_info(result)
                
                # 如果启用了下载功能，进行后续处理
                if download:
                    # 检查是否有下载的文件或已存在的文件
                    has_download = result.get('download') and len(result.get('download', [])) > 0
                    has_existing_file = 'existing_video_path' in result

                    # 即使没有下载链接，也尝试查找已存在的文件
                    if has_download or has_existing_file:
                        logging.info(f"开始处理视频文件 (下载: {has_download}, 已存在: {has_existing_file})")
                        await self._process_downloaded_video(
                            result, video_generation_key, auto_ab, auto_publish,
                            cover_title, custom_cover_path, cover_font_size, cover_font_color, cover_title_lines
                        )
                    else:
                        # 尝试查找已存在的文件
                        logging.info("没有下载链接，尝试查找已存在的视频文件...")
                        video_file_path = self._find_video_file(result)
                        if video_file_path:
                            logging.info(f"找到已存在的视频文件: {video_file_path}")
                            # 将文件路径添加到result中，然后处理
                            result['existing_video_path'] = str(video_file_path)
                            await self._process_downloaded_video(
                                result, video_generation_key, auto_ab, auto_publish,
                                cover_title, custom_cover_path, cover_font_size, cover_font_color, cover_title_lines
                            )
                        else:
                            logging.warning("没有找到任何视频文件，跳过视频处理")
                
                return {
                    "success": True,
                    "code": 200,
                    "msg": "详情查询成功",
                    "data": result
                }
            else:
                logging.error(f"未知的返回结果类型: {type(result)}")
                return {
                    "success": False,
                    "code": 500,
                    "msg": "未知的返回结果类型",
                    "data": None
                }
                
        except Exception as e:
            logging.error(f"DetailEnquireService 处理出错: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
            return {
                "success": False,
                "code": 500,
                "msg": f"详情查询出错: {str(e)}",
                "data": None
            }
    
    async def _setup_ks_downloader_and_process(self, url, cookie, download, proxy):
        """设置 KS-Downloader 并处理请求"""
        # 检查是否为打包环境
        is_frozen = getattr(sys, 'frozen', False)

        if is_frozen:
            # 打包环境：KS-Downloader 在 _internal 目录中
            ks_downloader_root = os.path.join(self.base_dir, "_internal", "KS-Downloader")
            ks_downloader_source = os.path.join(self.base_dir, "_internal", "KS-Downloader", "source")
        else:
            # 开发环境：KS-Downloader 在当前目录
            ks_downloader_root = os.path.join(self.base_dir, "KS-Downloader")
            ks_downloader_source = os.path.join(self.base_dir, "KS-Downloader", "source")

        # 添加路径到 sys.path
        for path in [ks_downloader_root, ks_downloader_source]:
            if path not in sys.path:
                sys.path.insert(0, path)
                logging.info(f"添加路径到 sys.path: {path}")

        try:
            from app.app import KS
            logging.info(f"成功导入 KS 类（{'打包' if is_frozen else '开发'}环境）")
        except ImportError as e:
            logging.error(f"导入 KS 类失败: {e}")
            logging.error(f"当前环境: {'打包' if is_frozen else '开发'}")
            logging.error(f"KS-Downloader 根目录: {ks_downloader_root}")
            logging.error(f"KS-Downloader source 目录: {ks_downloader_source}")
            logging.error(f"根目录是否存在: {os.path.exists(ks_downloader_root)}")
            logging.error(f"source 目录是否存在: {os.path.exists(ks_downloader_source)}")
            if os.path.exists(ks_downloader_source):
                app_dir = os.path.join(ks_downloader_source, "app")
                logging.error(f"app 目录是否存在: {os.path.exists(app_dir)}")
                if os.path.exists(app_dir):
                    app_py = os.path.join(app_dir, "app.py")
                    logging.error(f"app.py 文件是否存在: {os.path.exists(app_py)}")
            raise Exception(f"导入 KS-Downloader 失败: {e}")

        # 运行异步方法
        async def run_detail_enquire():
            # 创建 KS 实例
            async with KS() as ks_instance:
                # 设置下载目录
                ks_instance.manager.path = str(self.video_dir)
                ks_instance.download.path = str(self.video_dir)

                # 禁用下载记录检查，强制每次都下载
                ks_instance.database.record = 1

                async def always_false(x):
                    # logging.info(f"检查下载记录: {x} -> 强制返回False")
                    return False
                ks_instance.database.has_download_data = always_false

                logging.info(f"设置下载目录: {self.video_dir}")
                logging.info("已禁用下载记录检查，强制每次下载")

                # 1. 先通过 examiner.run 处理链接（处理短链接重定向）
                logging.info(f"开始处理链接: {url}")
                processed_urls = await ks_instance.examiner.run(url, proxy=proxy)
                logging.info(f"处理后的链接: {processed_urls}") 

                if not processed_urls:
                    return "链接处理失败，无法获取有效的快手链接"

                # 2. 取第一个有效链接
                valid_urls = processed_urls if isinstance(processed_urls, list) else [processed_urls]
                target_url = valid_urls[0] if valid_urls else url
                logging.info(f"目标链接: {target_url}")

                # 3. 调用 detail_one 方法
                logging.info(f"调用 detail_one 方法，download={download}")
                result = await ks_instance.detail_one(
                    url=target_url,
                    download=download,
                    proxy=proxy, 
                    cookie=cookie
                )
                logging.info(f"detail_one 方法执行完成")
                return result

        # 执行异步任务
        return await run_detail_enquire()
    
    def _log_success_info(self, result):
        """记录成功获取的作品信息"""
        detail_id = result.get('detailID', 'unknown')
        author_name = result.get('name', 'unknown')
        caption = result.get('caption', '')[:50]

        logging.info("成功获取作品详情:")
        logging.info(f"   - 作品ID: {detail_id}")
        logging.info(f"   - 作者: {author_name}")
        logging.info(f"   - 标题: {caption}...")
        logging.info(f"   - 时长: {result.get('duration', 'unknown')}")
        logging.info(f"   - 下载链接数: {len(result.get('download', []))}")
        
        # 记录购物数据（直接使用KS-Downloader从INIT_STATE中提取的数据）
        shopping_data_raw = result.get('shopping_data', "[]")
        shopping_data = []
        
        # 如果shopping_data是字符串，尝试解析为JSON
        if isinstance(shopping_data_raw, str):
            try:
                import json
                shopping_data = json.loads(shopping_data_raw)
            except (json.JSONDecodeError, TypeError) as e:
                logging.warning(f"解析购物数据JSON失败: {e}")
                shopping_data = []
        else:
            shopping_data = shopping_data_raw if isinstance(shopping_data_raw, list) else []
        
        if shopping_data:
            logging.info("=== 购物数据提取成功 ===")
            for shop_info in shopping_data:
                logging.info(f"   - 数据类型: {shop_info.get('type', 'unknown')}")
                if shop_info.get('type') == 'user_shop':
                    logging.info(f"   - 商户ID: {shop_info.get('sellerId', 'unknown')}")
                    logging.info(f"   - 店铺名称: {shop_info.get('title', 'unknown')}")
                    logging.info(f"   - 商品数量: {shop_info.get('subTitle', 'unknown')}")
                    logging.info(f"   - 店铺链接: {shop_info.get('link', 'unknown')[:100]}...")
                elif shop_info.get('type') == 'product_info':
                    logging.info(f"   - 商品分类: {shop_info.get('category', 'unknown')}")
                    logging.info(f"   - 商品标题: {shop_info.get('title', 'unknown')}")
                    logging.info(f"   - 分类类型: {shop_info.get('categoryType', 'unknown')}")
                    logging.info(f"   - 图标链接: {shop_info.get('iconUrl', 'unknown')}")
                elif shop_info.get('type') == 'shopping_keywords':
                    logging.info(f"   - 购物关键词: {shop_info.get('keywords', [])}")
                elif shop_info.get('type') == 'shopping_tags':
                    logging.info(f"   - 购物标签: {shop_info.get('tags', [])}")
            
            # 返回购物数据供进一步处理
            return shopping_data
        else:
            logging.info("   - 未检测到购物数据")
            return []
    
    def _parse_shopping_data_to_json(self, result):
        """将shopping_data从JSON字符串转换为JSON对象，并组合购物信息"""
        try:
            shopping_data_raw = result.get('shopping_data', "[]")
            
            # 如果shopping_data是字符串，解析为JSON对象
            if isinstance(shopping_data_raw, str):
                import json
                shopping_data = json.loads(shopping_data_raw)
            elif isinstance(shopping_data_raw, list):
                shopping_data = shopping_data_raw
            else:
                shopping_data = []
            
            # 将数组形式的购物数据转换为对象形式，按类型组织
            organized_shopping_data = {
                'user_shops': [],      # 用户店铺
                'products': [],        # 产品信息
                'keywords': [],        # 购物关键词
                'tags': [],           # 购物标签
                'summary': {           # 统计摘要
                    'total_shops': 0,
                    'total_products': 0,
                    'has_shopping_data': False
                }
            }
            
            if shopping_data:
                for shop_info in shopping_data:
                    data_type = shop_info.get('type', 'unknown')
                    
                    if data_type == 'user_shop':
                        organized_shopping_data['user_shops'].append({
                            'sellerId': shop_info.get('sellerId', 'unknown'),
                            'title': shop_info.get('title', 'unknown'),
                            'subTitle': shop_info.get('subTitle', 'unknown'),
                            'link': shop_info.get('link', 'unknown'),
                            'entranceTitle': shop_info.get('entranceTitle', ''),
                            'icon': shop_info.get('icon', ''),
                            'headImg': shop_info.get('headImg', ''),
                            'source': shop_info.get('source', 'unknown')
                        })
                        
                    elif data_type == 'product_info':
                        organized_shopping_data['products'].append({
                            'category': shop_info.get('category', 'unknown'),
                            'title': shop_info.get('title', 'unknown'),
                            'categoryType': shop_info.get('categoryType', 'unknown'),
                            'iconUrl': shop_info.get('iconUrl', 'unknown'),
                            'bizType': shop_info.get('bizType', ''),
                            'source': shop_info.get('source', 'unknown')
                        })
                        
                    elif data_type == 'shopping_keywords':
                        organized_shopping_data['keywords'].extend(shop_info.get('keywords', []))
                        
                    elif data_type == 'shopping_tags':
                        organized_shopping_data['tags'].extend(shop_info.get('tags', []))
                
                # 更新统计信息
                organized_shopping_data['summary']['total_shops'] = len(organized_shopping_data['user_shops'])
                organized_shopping_data['summary']['total_products'] = len(organized_shopping_data['products'])
                organized_shopping_data['summary']['has_shopping_data'] = True
                
                logging.info(f"成功组织购物数据: {organized_shopping_data['summary']['total_shops']} 个店铺, {organized_shopping_data['summary']['total_products']} 个产品")
            else:
                logging.info("未检测到购物数据")
            
            # 同时保留原始数组格式和新的对象格式
            result['shopping_data'] = shopping_data  # 原始数组格式
            result['shopping_info'] = organized_shopping_data  # 新的对象格式
            
        except (json.JSONDecodeError, TypeError) as e:
            logging.warning(f"解析shopping_data JSON失败: {e}")
            result['shopping_data'] = []
            result['shopping_info'] = {
                'user_shops': [],
                'products': [],
                'keywords': [],
                'tags': [],
                'summary': {'total_shops': 0, 'total_products': 0, 'has_shopping_data': False}
            }
        except Exception as e:
            logging.error(f"处理shopping_data时发生未知错误: {e}")
            result['shopping_data'] = []
            result['shopping_info'] = {
                'user_shops': [],
                'products': [],
                'keywords': [],
                'tags': [],
                'summary': {'total_shops': 0, 'total_products': 0, 'has_shopping_data': False}
            }
    
    async def _process_downloaded_video(self, result, video_generation_key, auto_ab, auto_publish,
                                      cover_title='', custom_cover_path='',
                                      cover_font_size=72, cover_font_color='#FFFF00',
                                      cover_title_lines=None):
        """处理下载的视频文件"""
        try:
            logging.info("开始处理视频文件...")

            # 查找视频文件
            video_file_path = self._find_video_file(result)
            
            if video_file_path and video_file_path.exists():
                logging.info(f"找到下载的视频文件: {video_file_path}")
                result['video_file_path'] = str(video_file_path)

                # 检查AB去重条件
                logging.info(f"AB去重条件检查:")
                logging.info(f"  - auto_ab: {auto_ab}")
                logging.info(f"  - video_generation_key: {'已提供' if video_generation_key else '未提供'}")

                # 如果启用了AB去重功能，先进行AB处理再保存
                if auto_ab and video_generation_key:
                    logging.info("开始AB去重处理流程...")
                    await self._process_ab_video_generation(
                        video_file_path, result, video_generation_key, cover_title, custom_cover_path,
                        cover_font_size, cover_font_color, cover_title_lines
                    )
                else:
                    logging.info("跳过AB去重，直接保存原视频到数据库")
                    # 如果不进行AB处理，直接保存原视频到数据库
                    await self._save_video_to_database(video_file_path, result, cover_title, custom_cover_path, cover_font_size, cover_font_color, cover_title_lines)
            else:
                logging.warning("未找到下载的视频文件")
                
        except Exception as e:
            logging.error(f"处理下载视频失败: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
            # 确保即使出错也能返回一些基本信息
            if not result.get('video_file_path'):
                logging.warning("处理失败，但仍返回基本的视频信息")
    
    async def _save_video_to_database(self, video_file_path, video_data,
                                     cover_title='', custom_cover_path='',
                                     cover_font_size=72, cover_font_color='#FFFF00',
                                     cover_title_lines=None):
        """保存视频文件到数据库"""
        try:
            import uuid
            import subprocess
            import sqlite3
            import shutil

            logging.info(f"💾 开始保存视频到数据库:")
            logging.info(f"   - 视频文件路径: {video_file_path}")
            logging.info(f"   - 封面标题: {repr(cover_title)}")
            logging.info(f"   - 自定义封面路径: {repr(custom_cover_path)}")

            # 确保路径是Path对象
            video_path = Path(video_file_path) if not isinstance(video_file_path, Path) else video_file_path
            
            # 如果传入的路径不存在，尝试从video_data中获取现有路径
            if not video_path.exists():
                if 'existing_video_path' in video_data and video_data['existing_video_path']:
                    existing_path = Path(video_data['existing_video_path'])
                    if existing_path.exists():
                        video_path = existing_path
                        logging.info(f"使用现有视频路径: {video_path}")
                    else:
                        logging.warning(f"existing_video_path 也不存在: {existing_path}")
                        # 继续使用原路径，可能是文件已经被移动
                        
            # 获取视频信息
            author_name = video_data.get('name', 'unknown')
            detail_id = video_data.get('detailID', 'unknown')
            caption = video_data.get('caption', '')

            # 清理文件名中的非法字符
            safe_author_name = re.sub(r'[<>:"/\\|?*]', '_', author_name)
            safe_caption = re.sub(r'[<>:"/\\|?*]', '_', caption)

            # 生成原始文件名
            original_filename = f"{detail_id}_{safe_author_name}_{safe_caption[:30]}.mp4"

            # 生成UUID文件名（复用uploadSave逻辑）
            uuid_v1 = uuid.uuid1()
            final_filename = f"{uuid_v1}_{original_filename}"
            final_filepath = self.video_dir / final_filename

            # 如果文件不存在，报错
            if not video_path.exists():
                logging.error(f"源视频文件不存在: {video_path}")
                return None

            # 移动文件到最终位置
            if video_path != final_filepath:
                shutil.move(str(video_path), str(final_filepath))
                logging.info(f"文件已移动: {video_path} -> {final_filepath}")
            else:
                logging.info(f"文件已在目标位置: {final_filepath}")

            # 生成封面图（支持自定义封面和文字叠加）
            cover_name = f"{uuid_v1}_cover.jpg"
            cover_path = self.video_dir / cover_name

            logging.info(f"🎨 准备生成封面图: {cover_path}")
            logging.info(f"   - 视频文件: {final_filepath}")
            logging.info(f"   - 封面标题: {repr(cover_title)}")
            logging.info(f"   - 自定义封面: {repr(custom_cover_path)}")

            cover_path = await self._generate_cover_with_title(
                final_filepath, cover_path, cover_title, custom_cover_path,
                cover_font_size, cover_font_color, cover_title_lines
            )

            if cover_path and Path(cover_path).exists():
                logging.info(f"✅ 封面图生成成功: {cover_path}")
            else:
                logging.error(f"❌ 封面图生成失败或文件不存在: {cover_path}")

            # 保存到数据库（复用uploadSave逻辑）
            db_path = self.base_dir / "database.db"
            with sqlite3.connect(str(db_path)) as conn:
                cursor = conn.cursor()

                # 计算文件大小（MB）
                file_size_mb = round(float(os.path.getsize(final_filepath)) / (1024 * 1024), 2)

                # 插入新记录（复用uploadSave逻辑）
                cursor.execute('''
                    INSERT INTO file_records (filename, filesize, file_path, cover_image)
                    VALUES (?, ?, ?, ?)
                ''', (
                    original_filename,
                    file_size_mb,
                    str(final_filename),
                    str(cover_path) if cover_path else None
                ))

                conn.commit()
                record_id = cursor.lastrowid
                
                logging.info(f"✅ 视频记录保存成功:")
                logging.info(f"   - 记录ID: {record_id}")
                logging.info(f"   - 原始文件名: {original_filename}")
                logging.info(f"   - 最终文件名: {final_filepath}")
                logging.info(f"   - 文件大小: {file_size_mb} MB")
                logging.info(f"   - 封面图: {cover_path}")

                # 更新 result 中的数据库信息
                video_data['database_record'] = {
                    "id": record_id,
                    "filename": original_filename,
                    "filepath": str(final_filename),
                    "cover_image": str(cover_path) if cover_path else None,
                    "filesize": file_size_mb
                }

                # 更新result中的文件路径信息，因为文件已经被移动
                video_data['video_file_path'] = str(final_filepath)
                video_data['final_filepath'] = str(final_filepath)
                
                # 如果原来有existing_video_path，更新为新路径
                video_data['existing_video_path'] = str(final_filepath)
                    

                return video_data['database_record']

        except Exception as e:
            logging.error(f"❌ 保存视频到数据库失败: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
            return None
    
    def _find_video_file(self, result):
        """查找下载的视频文件"""
        # 获取视频信息
        detail_id = result.get('detailID', 'unknown')
        author_name = result.get('name', 'unknown')
        caption = result.get('caption', '')

        logging.info(f"🔍 查找视频文件:")
        logging.info(f"   - 作品ID: {detail_id}")
        logging.info(f"   - 作者: {author_name}")
        logging.info(f"   - 标题: {caption[:50]}...")

        # 清理文件名中的非法字符
        safe_author_name = re.sub(r'[<>:"/\\|?*]', '_', author_name)
        safe_caption = re.sub(r'[<>:"/\\|?*]', '_', caption)

        logging.info(f"   - 安全作者名: {safe_author_name}")
        logging.info(f"   - 安全标题: {safe_caption[:30]}...")

        # 检查 KS-Downloader 是否返回了已存在文件的路径
        video_file_path = None

        if 'existing_video_path' in result:
            # KS-Downloader 找到了已存在的文件
            existing_path = Path(result['existing_video_path'])
            if existing_path.exists():
                video_file_path = existing_path
                logging.info(f"返回的已存在文件: {video_file_path}")
            else:
                logging.warning(f"返回的文件路径不存在: {existing_path}")
        else:
            # KS-Downloader 没有返回已存在文件路径，查找新下载的文件
            timestamp = result.get('timestamp', '').replace(':', '.')  # 替换冒号为点号

            # 首先在 videoFile 目录查找（处理过的文件）
            all_files = list(self.video_dir.glob("*.mp4"))
            original_files = [f for f in all_files
                            if not f.name.startswith('AB去重_')
                            and not f.name.startswith('secondary_')
                            and f.suffix.lower() == '.mp4']

            logging.info(f"   - 视频目录: {self.video_dir}")
            logging.info(f"   - 找到 {len(all_files)} 个MP4文件")
            logging.info(f"   - 过滤后 {len(original_files)} 个原始文件")
            if original_files:
                logging.info(f"   - 原始文件列表: {[f.name for f in original_files[:5]]}...")

            # 按优先级查找文件
            if timestamp and safe_author_name:
                logging.info(f"   - 查找策略1: 时间戳({timestamp}) + 作者名({safe_author_name})")
                # 1. 查找包含时间戳和作者名的文件
                for f in original_files:
                    if timestamp in f.name and safe_author_name in f.name:
                        video_file_path = f
                        logging.info(f"✅ 通过时间戳+作者名找到文件: {video_file_path}")
                        break

            if not video_file_path and safe_author_name:
                # 2. 查找包含作者名的文件
                for f in original_files:
                    if safe_author_name in f.name:
                        video_file_path = f
                        logging.info(f"通过作者名找到文件: {video_file_path}")
                        break

            if not video_file_path and timestamp:
                # 3. 查找包含时间戳的文件
                for f in original_files:
                    if timestamp in f.name:
                        video_file_path = f
                        logging.info(f"通过时间戳找到文件: {video_file_path}")
                        break

            if not video_file_path and original_files:
                # 4. 使用最新的文件
                video_file_path = max(original_files, key=lambda x: x.stat().st_mtime)
                logging.info(f"使用最新的文件: {video_file_path}")

        # 如果在 videoFile 目录没找到文件，尝试从 Download 目录查找并拷贝
        if not video_file_path:
            logging.info("在 videoFile 目录未找到文件，尝试从 Download 目录查找...")
            download_dir = Path("backends/KS-Downloader/Download")

            if download_dir.exists():
                download_files = list(download_dir.glob("*.mp4"))
                logging.info(f"Download 目录中找到 {len(download_files)} 个MP4文件")

                # 在 Download 目录中查找匹配的文件
                for f in download_files:
                    if safe_author_name in f.name:
                        logging.info(f"在 Download 目录找到匹配文件: {f.name}")

                        # 拷贝文件到 videoFile 目录
                        import shutil
                        target_path = self.video_dir / f.name
                        try:
                            shutil.copy2(f, target_path)
                            video_file_path = target_path
                            logging.info(f"✅ 文件已拷贝到 videoFile 目录: {target_path}")
                            break
                        except Exception as e:
                            logging.error(f"❌ 拷贝文件失败: {e}")
            else:
                logging.warning(f"Download 目录不存在: {download_dir}")

        # 如果还是没找到文件，记录详细信息
        if not video_file_path:
            all_files = list(self.video_dir.glob("*.mp4"))
            logging.info(f"视频目录中的所有mp4文件: {[f.name for f in all_files]}")
            logging.error(f"未找到任何匹配的视频文件")
            timestamp = result.get('timestamp', '').replace(':', '.')
            logging.error(f"搜索条件 - timestamp: '{timestamp}', author: '{safe_author_name}'")

        return video_file_path
    
    async def _process_ab_video_generation(self, video_file_path, result, video_generation_key,
                                         cover_title='', custom_cover_path='',
                                         cover_font_size=72, cover_font_color='#FFFF00',
                                         cover_title_lines=None):
        """处理AB视频生成和去重"""
        try:
            logging.info("=== 开始AB视频生成流程 ===")
            logging.info(f"使用原始视频路径进行AB处理: {video_file_path}")
            
            # 1. 先获取启用自动发布的快手账号数量，决定生成几个AB版本
            ab_video_count = await self._get_auto_publish_account_count()
            logging.info(f"检测到 {ab_video_count} 个启用自动发布的快手账号，将生成 {ab_video_count} 个AB去重版本")
            
            if ab_video_count == 0:
                logging.warning("没有启用自动发布的快手账号，跳过AB去重")
                # 保存原视频到数据库
                await self._save_video_to_database(video_file_path, result, cover_title, custom_cover_path, cover_font_size, cover_font_color, cover_title_lines)
                return
            
            # 2. 生成图片 - 根据账号数量生成足够的图片
            # 每个AB版本需要3张图片，所以总共需要 ab_video_count * 3 张图片
            total_images_needed = ab_video_count * 3
            prompt = self._generate_image_prompt(result)
            image_urls = self._generate_images_from_api(video_generation_key, prompt, image_count=total_images_needed)
            
            if not image_urls:
                logging.warning("图片生成失败，跳过AB去重")
                # 保存原视频到数据库
                await self._save_video_to_database(video_file_path, result, cover_title, custom_cover_path, cover_font_size, cover_font_color, cover_title_lines)
                return
            
            # 3. 下载图片
            image_download_dir = self.video_dir / "temp_images"
            image_download_dir.mkdir(exist_ok=True)
            
            downloaded_images = self._download_images(image_urls, image_download_dir)
            
            if not downloaded_images:
                logging.warning("图片下载失败，跳过AB去重")
                # 保存原视频到数据库
                await self._save_video_to_database(video_file_path, result, '', '', 72, '#FFFF00', None)
                return
            
            # 4. 生成多个AB去重版本
            ab_results = []
            generated_videos = []
            
            # 将下载的图片按每3张一组分组，为每个账号生成一个独特的AB版本
            for i in range(ab_video_count):
                try:
                    # 为每个AB版本选择不同的图片组合
                    start_idx = (i * 3) % len(downloaded_images)
                    end_idx = min(start_idx + 3, len(downloaded_images))
                    
                    # 如果图片不够，则重复使用
                    batch_images = downloaded_images[start_idx:end_idx]
                    if len(batch_images) < 3:
                        batch_images = (downloaded_images * 3)[:3]  # 重复图片填充到3张
                    
                    logging.info(f"正在生成第 {i+1}/{ab_video_count} 个AB版本，使用图片索引 {start_idx}-{end_idx-1}")
                    
                    # 生成副视频
                    import uuid
                    secondary_video_name = f"secondary_{uuid.uuid1()}_v{i+1}.mp4"
                    secondary_video_path = self.video_dir / secondary_video_name
                    
                    created_video = self._create_video_from_images(
                        batch_images, secondary_video_path
                    )
                    
                    if not created_video:
                        logging.warning(f"第 {i+1} 个副视频生成失败，跳过")
                        continue
                    
                    # 执行AB去重 - 使用原始视频路径，传递封面参数
                    ab_result = await self._execute_ab_processing(video_file_path, secondary_video_path, result,
                                                                 version_suffix=f"v{i+1}",
                                                                 cover_title=cover_title,
                                                                 custom_cover_path=custom_cover_path,
                                                                 cover_font_size=cover_font_size,
                                                                 cover_font_color=cover_font_color,
                                                                 cover_title_lines=cover_title_lines)

                    if ab_result.get('success'):
                        logging.info(f"第 {i+1} 个AB去重处理成功")
                        ab_results.append(ab_result)
                        generated_videos.append({
                            'version': i+1,
                            'output_path': ab_result.get('output'),
                            'filename': ab_result.get('filename'),
                            'record_id': ab_result.get('record_id'),
                            'cover_image': ab_result.get('cover_image')
                        })
                    else:
                        logging.error(f"第 {i+1} 个AB去重处理失败: {ab_result.get('error')}")
                    
                    # 清理临时副视频文件
                    try:
                        if secondary_video_path.exists():
                            secondary_video_path.unlink()
                    except Exception as e:
                        logging.warning(f"清理第 {i+1} 个副视频文件失败: {e}")
                        
                except Exception as e:
                    logging.error(f"生成第 {i+1} 个AB版本时发生异常: {e}")
                    continue
            
            # 5. 处理结果
            if ab_results:
                logging.info(f"成功生成 {len(ab_results)} 个AB去重版本")
                # 更新result中的信息 - 存储所有AB版本信息
                result['ab_processed'] = True
                result['ab_versions'] = generated_videos
                result['ab_count'] = len(ab_results)
                
                # 暂时保留原始主视频文件，由上层逻辑根据自动发布状态决定是否清理
                # 如果启用自动发布但没有可用账号，需要保留文件供手动发布
                result['temp_main_video_path'] = str(video_file_path)  # 记录原始文件路径
                logging.info(f"AB处理完成，暂时保留原始主视频文件: {video_file_path}")
            else:
                logging.error("所有AB去重处理都失败了，保存原视频到数据库")
                # 保存原视频到数据库
                await self._save_video_to_database(video_file_path, result, cover_title, custom_cover_path, cover_font_size, cover_font_color, cover_title_lines)
            
            # 6. 清理临时文件
            try:
                for img_file in downloaded_images:
                    Path(img_file).unlink()
                if image_download_dir.exists():
                    image_download_dir.rmdir()
                logging.info("临时文件清理完成")
            except Exception as cleanup_e:
                logging.warning(f"清理临时文件失败: {cleanup_e}")
                
        except Exception as e:
            logging.error(f"AB视频生成流程失败: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
            # 发生异常，保存原视频到数据库
            try:
                await self._save_video_to_database(video_file_path, result, cover_title, custom_cover_path, cover_font_size, cover_font_color, cover_title_lines)
            except Exception as save_e:
                logging.error(f"保存原视频到数据库也失败: {save_e}")
                
    async def _get_auto_publish_account_count(self):
        """获取启用自动发布的快手账号数量"""
        try:
            import sqlite3
            
            db_path = self.base_dir / "database.db" 
            with sqlite3.connect(str(db_path)) as conn:
                cursor = conn.cursor()
                # 查询快手账号（type=4）且启用自动发布（auto_publish=1）的数量
                cursor.execute("""
                    SELECT COUNT(*) FROM user_info 
                    WHERE type = 4 AND auto_publish = 1
                """)
                count = cursor.fetchone()[0]
                logging.info(f"数据库查询：找到 {count} 个启用自动发布的快手账号")
                return max(count, 1)  # 至少生成1个版本，即使没有启用自动发布的账号
                
        except Exception as e:
            logging.error(f"获取启用自动发布的账号数量失败: {e}")
            return 1  # 出错时返回1，保证至少生成一个版本
    
    async def _execute_ab_processing(self, main_video_path, secondary_video_path, result, version_suffix="",
                                   cover_title='', custom_cover_path='',
                                   cover_font_size=72, cover_font_color='#FFFF00',
                                   cover_title_lines=None):
        """执行AB视频去重处理并保存到数据库"""
        try:
            logging.info("=== 开始执行AB视频处理 ===")
            logging.info(f"主视频路径 (来自KS-Downloader): {main_video_path}")
            logging.info(f"副视频路径 (从生成图片创建): {secondary_video_path}")
            logging.info(f"版本后缀: {version_suffix}")
            
            # 验证文件存在
            if not main_video_path.exists():
                error_msg = f"主视频文件不存在: {main_video_path}"
                logging.error(error_msg)
                return {'success': False, 'error': error_msg}
            
            if not secondary_video_path.exists():
                error_msg = f"副视频文件不存在: {secondary_video_path}"
                logging.error(error_msg)
                return {'success': False, 'error': error_msg}
            
            import uuid
            import sqlite3
            
            # 从 main.py 复制的AB处理逻辑
            from ffmpeg_config import FFMPEG_BIN, FFPROBE_BIN
            
            # 生成新文件名和路径 - 添加版本后缀
            uuid_v1 = uuid.uuid1()
            filename = main_video_path.name
            version_part = f"_{version_suffix}" if version_suffix else ""
            final_filename = f"AB去重_{uuid_v1}{version_part}_{filename}"
            output_path = self.video_dir / final_filename

            # 获取主视频时长
            def get_duration(ffprobe_path, video_path):
                cmd = [
                    ffprobe_path, "-v", "error", "-show_entries",
                    "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", str(video_path)
                ]
                result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore')
                try:
                    return float(result.stdout.strip())
                except Exception as e:
                    logging.error(f"获取视频时长失败: {e}")
                    return None

            duration = get_duration(FFPROBE_BIN, main_video_path)
            
            # 检查CUDA支持
            def is_cuda_available():
                try:
                    result = subprocess.run(
                        [FFMPEG_BIN, "-hide_banner", "-encoders"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        encoding="utf-8",
                        errors="replace"
                    )
                    return "h264_nvenc" in result.stdout
                except Exception as e:
                    logging.error(f"检测 CUDA 失败: {e}")
                    return False

            cuda = is_cuda_available()
            logging.info(f"自动检测 CUDA 支持: {cuda}")
            video_codec = "h264_nvenc" if cuda else "libx264"
            preset = "p1" if cuda else "medium"
            
            # 构造ffmpeg命令 - 修复副视频无音频的问题
            cmd = [
                FFMPEG_BIN,
                "-y",
                "-i", str(main_video_path),
                "-stream_loop", "-1", "-i", str(secondary_video_path),
                "-f", "lavfi", "-i", "anullsrc=channel_layout=stereo:sample_rate=44100",  # 添加静音音频源
                "-filter_complex",
                "[0:v]fps=60,scale=1080:1920,setsar=1[v1p];"
                "[v1p]split=2[v1i][v1r];"
                "[v1i]trim=end_frame=2,setpts=PTS-STARTPTS[init_v1f];"
                "[v1r]trim=start_frame=2,setpts=PTS-STARTPTS[v1rest];"
                "[1:v]fps=60,scale=1080:1920,setsar=1[v2p];"
                "[v2p]trim=start_frame=2,setpts=PTS-STARTPTS[v2trim];"
                "[v1rest][v2trim]interleave[inter];"
                "[init_v1f][inter]concat=n=2:v=1:a=0[vout];"
                "[0:a]anull[a1];"
                "[2:a]volume=0.001[a2];"  # 使用静音音频源代替副视频音频
                "[a1][a2]amix=inputs=2:duration=first:dropout_transition=0[aout]",
                "-map", "[vout]",
                "-map", "[aout]",
                "-shortest",
                "-r", "120",
                "-c:v", video_codec,
            ]
            
            # 根据编码器类型添加质量参数
            if video_codec == "libx264":
                cmd += ["-crf", "28"]
            elif video_codec == "h264_nvenc":
                cmd += ["-qp", "28"]
                
            cmd += [
                "-preset", preset,
                "-c:a", "aac",
                "-b:a", "128k",
                str(output_path)
            ]

            logging.info("开始执行AB去重处理...")
            logging.info(f"命令: {' '.join(cmd)}")
            
            # 执行ffmpeg命令
            process = subprocess.Popen(
                cmd,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                encoding="utf-8",
                errors="replace"
            )
            
            # 监控进度
            import re
            time_pattern = re.compile(r'time=(\d+):(\d+):(\d+\.\d+)')
            while True:
                line = process.stderr.readline()
                if not line:
                    break
                logging.info(line.strip())
                if duration:
                    match = time_pattern.search(line)
                    if match:
                        h, m, s = map(float, match.groups())
                        current = h * 3600 + m * 60 + s
                        percent = min(current / duration * 100, 100)
                        if percent < 100:
                            logging.info(f"视频处理进度: {percent:.2f}%")
                        
            process.wait()
            logging.info(f"ffmpeg 执行完成，returncode: {process.returncode}")

            if process.returncode == 0:
                # 生成封面图 - 使用统一的封面生成方法，支持自定义封面和标题
                cover_name = f"{uuid_v1}_cover.jpg"
                cover_path = self.video_dir / cover_name

                # 使用统一的封面生成方法
                generated_cover = await self._generate_cover_with_title(
                    output_path, cover_path, cover_title, custom_cover_path,
                    cover_font_size, cover_font_color, cover_title_lines
                )

                if generated_cover:
                    logging.info(f"AB处理封面图生成成功: {cover_path}")
                    cover_path = generated_cover
                else:
                    logging.error("AB处理封面图生成失败")
                    cover_path = None

                # 保存到数据库
                db_path = self.base_dir / "database.db"
                with sqlite3.connect(str(db_path)) as conn:
                    cursor = conn.cursor()
                    file_size_mb = round(float(os.path.getsize(output_path)) / (1024 * 1024), 2)
                    
                    cursor.execute('''
                        INSERT INTO file_records (filename, filesize, file_path, cover_image)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        final_filename,
                        file_size_mb,
                        final_filename,
                        str(cover_path) if cover_path else None
                    ))
                    
                    conn.commit()
                    record_id = cursor.lastrowid
                    
                    logging.info(f"AB处理后文件已记录到数据库:")
                    logging.info(f"   - 记录ID: {record_id}")
                    logging.info(f"   - 文件名: {final_filename}")
                    logging.info(f"   - 文件大小: {file_size_mb} MB")
                    logging.info(f"   - 封面图: {cover_path}")

                return {
                    "success": True,
                    "output": str(output_path),
                    "filename": final_filename,
                    "filepath": final_filename,
                    "cover_image": str(cover_path) if cover_path else None,
                    "record_id": record_id,
                    "filesize": file_size_mb
                }
            else:
                logging.error("ffmpeg AB处理执行失败")
                return {
                    "success": False,
                    "error": "ffmpeg AB处理执行失败"
                }
                
        except Exception as e:
            logging.error(f"AB处理异常: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _generate_image_prompt(self, result):
        """根据视频信息生成图片生成的提示词"""
        author_name = result.get('name', '创作者')
        caption = result.get('caption', '精彩内容')
        
        # 简化和清理标题
        clean_caption = re.sub(r'[#@\[\]【】]', '', caption)[:50]
        
        prompt = f"基于'{clean_caption}'主题的创意插画，现代简约风格，色彩丰富，高质量数字艺术，不要违规内容，适合社交媒体分享。"
        # logging.info(f"生成的图片提示词: {prompt}")
        return prompt
    
    def _generate_images_from_api(self, api_key, prompt, aspect_ratio="16:9", image_count=3):
        """调用外部API生成图片"""
        import requests
        import urllib3
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # logging.info(f"开始调用图片生成API - prompt: {prompt[:50]}... 生成数量: {image_count}")

        try:
            url = "https://api.minimaxi.com/v1/image_generation"

            payload = json.dumps({
                "model": "image-01",
                "prompt": prompt,
                "aspect_ratio": aspect_ratio,
                "response_format": "url",
                "n": image_count,
                "prompt_optimizer": True
            })

            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }

            logging.info(f"等待AI生成 ...")
            # 添加verify=False来禁用SSL证书验证
            response = requests.post(url, headers=headers, data=payload, timeout=1500, verify=False)

            if response.status_code == 200:
                result = response.json()
                logging.info(f"API调用成功，状态: {result.get('base_resp', {}).get('status_msg', 'unknown')}")

                if result.get('base_resp', {}).get('status_code') == 0:
                    image_urls = result.get('data', {}).get('image_urls', [])
                    logging.info(f"成功获取 {len(image_urls)} 张图片URL")
                    return image_urls
                else:
                    logging.error(f"API返回错误: {result.get('base_resp', {}).get('status_msg', 'unknown')}")
                    return None
            else:
                logging.error(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")
                return None

        except Exception as e:
            logging.error(f"图片生成API调用失败: {e}")
            return None

    def _download_images(self, image_urls, download_dir):
        """下载图片到本地"""
        import requests
        import urllib3
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        downloaded_files = []

        for i, url in enumerate(image_urls):
            try:
                logging.info(f"下载图片 {i+1}/{len(image_urls)}: {url}")

                # 添加verify=False来禁用SSL证书验证
                response = requests.get(url, timeout=30, verify=False)
                if response.status_code == 200:
                    # 生成文件名
                    filename = f"generated_image_{i+1}.jpg"
                    filepath = Path(download_dir) / filename

                    with open(filepath, 'wb') as f:
                        f.write(response.content)

                    downloaded_files.append(str(filepath))
                    logging.info(f"图片下载成功: {filepath}")
                else:
                    logging.error(f"图片下载失败，状态码: {response.status_code}")

            except Exception as e:
                logging.error(f"下载图片失败: {e}")

        logging.info(f"图片下载完成，成功下载 {len(downloaded_files)} 张图片")
        return downloaded_files

    def _create_video_from_images(self, image_files, output_path, duration_per_image=1, fps=30):
        """使用ffmpeg将图片生成视频"""
        if not image_files:
            logging.error("没有图片文件可用于生成视频")
            return None

        try:
            ffmpeg_bin = FFMPEG_BIN
            logging.info(f"准备生成视频，图片数量: {len(image_files)}")

            # 计算总时长和帧数
            total_duration = len(image_files) * duration_per_image
            total_frames = total_duration * fps

            logging.info(f"开始生成视频: {output_path}")
            logging.info(f"参数: {len(image_files)}张图片, 每张图片{duration_per_image}秒, {fps}fps")
            logging.info(f"总时长: {total_duration}秒, 总帧数: {total_frames}")

            # 创建输入文件列表
            input_list = []
            for img_file in image_files:
                input_list.extend(["-loop", "1", "-t", str(duration_per_image), "-i", str(img_file)])

            # 创建filter_complex来连接所有图片
            if len(image_files) == 1:
                filter_complex = f"[0:v]fps={fps}[v]"
            else:
                inputs = "".join([f"[{i}:v]fps={fps}[v{i}];" for i in range(len(image_files))])
                concat = "".join([f"[v{i}]" for i in range(len(image_files))])
                filter_complex = f"{inputs}{concat}concat=n={len(image_files)}:v=1:a=0[v]"

            # 使用ffmpeg生成视频
            cmd = [
                ffmpeg_bin,
                "-y",  # 覆盖输出文件
            ] + input_list + [
                "-filter_complex", filter_complex,
                "-map", "[v]",
                "-pix_fmt", "yuv420p",
                "-c:v", "libx264",
                "-crf", "28",
                "-preset", "medium",
                str(output_path)
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')

            if result.returncode == 0:
                logging.info(f"视频生成成功: {output_path}")
                return str(output_path)
            else:
                logging.error(f"视频生成失败: {result.stderr}")
                return None

        except Exception as e:
            logging.error(f"生成视频时发生错误: {e}")
            return None

    async def _generate_cover_with_title(self, video_path, cover_path, cover_title='', custom_cover_path='',
                                       cover_font_size=72, cover_font_color='#FFFF00',
                                       cover_title_lines=None):
        """
        生成带有标题文字的封面图片

        Args:
            video_path: 视频文件路径
            cover_path: 输出封面图片路径
            cover_title: 封面标题文字
            custom_cover_path: 自定义封面图片路径
            cover_font_size: 封面标题字体大小
            cover_font_color: 封面标题字体颜色（简单模式）
            cover_title_lines: 多行颜色配置（高级模式）
            title_input_mode: 标题输入模式（simple/advanced）

        Returns:
            Path: 生成的封面图片路径，失败时返回None
        """
        try:
            from ffmpeg_config import FFMPEG_BIN
            import subprocess

            logging.info(f"🎨 开始生成封面图片:")
            logging.info(f"   - 视频路径: {video_path}")
            logging.info(f"   - 封面输出路径: {cover_path}")
            logging.info(f"   - 封面标题: {repr(cover_title)}")
            logging.info(f"   - 自定义封面路径: {repr(custom_cover_path)}")

            # 确定基础封面图片来源
            if custom_cover_path and Path(custom_cover_path).exists():
                # 使用自定义封面图片
                logging.info(f"使用自定义封面图片: {custom_cover_path}")
            else:
                # 使用视频第一帧作为封面
                logging.info(f"使用视频第一帧作为封面: {video_path}")

            # 如果没有标题文字，直接生成基础封面
            if not cover_title.strip():
                if custom_cover_path and Path(custom_cover_path).exists():
                    # 直接复制自定义封面图片
                    import shutil
                    shutil.copy2(custom_cover_path, str(cover_path))
                    logging.info(f"✅ 复制自定义封面图片成功: {cover_path}")
                else:
                    # 从视频提取第一帧
                    subprocess.run([
                        FFMPEG_BIN,
                        "-y",
                        "-i", str(video_path),
                        "-vf", "select=eq(n\\,0)",
                        "-q:v", "2",
                        str(cover_path)
                    ], check=True, encoding='utf-8', errors='ignore')
                    logging.info(f"✅ 从视频提取封面成功: {cover_path}")
                return cover_path

            # 有标题文字，需要叠加文字
            logging.info(f"开始生成带标题的封面图片，标题: {cover_title}")

            # 准备文字叠加的 ffmpeg 命令
            if custom_cover_path and Path(custom_cover_path).exists():
                # 在自定义封面图片上叠加文字
                cmd = [
                    FFMPEG_BIN,
                    "-y",
                    "-i", custom_cover_path,
                ]
            else:
                # 在视频第一帧上叠加文字
                cmd = [
                    FFMPEG_BIN,
                    "-y",
                    "-i", str(video_path),
                ]
            logging.info(f"准备生成带标题的封面图片，标题: {cover_title_lines}")
            # 处理多行颜色配置
            if cover_title_lines and isinstance(cover_title_lines, list) and len(cover_title_lines) > 0:
                # 使用多行颜色配置
                logging.info(f"🎨 使用多行颜色配置: {cover_title_lines}")

                # 调试：检查每行配置的字段
                for i, line_config in enumerate(cover_title_lines):
                    logging.info(f"🔍 行{i+1}配置: {line_config}")
                    if isinstance(line_config, dict):
                        logging.info(f"   - text: {line_config.get('text', 'MISSING')}")
                        logging.info(f"   - color: {line_config.get('color', 'MISSING')}")
                        logging.info(f"   - fontSize: {line_config.get('fontSize', 'MISSING')} (默认: {cover_font_size})")
                        logging.info(f"   - spacing: {line_config.get('spacing', 'MISSING')} (默认: 10)")

                title_lines_with_colors = []

                # 统计有效的配置行数（有文字的行）
                valid_configs = []
                for line_config in cover_title_lines:
                    if isinstance(line_config, dict) and 'text' in line_config and 'color' in line_config:
                        text = line_config['text'].strip()
                        if text:  # 只处理非空文本
                            valid_configs.append(line_config)

                if not valid_configs:
                    logging.warning("多行配置中没有有效的文本行，跳过文字叠加")
                    return cover_path

                # 如果只有一行有效配置，但文字较长，需要自动换行到用户设置的行数
                if len(valid_configs) == 1 and len(cover_title_lines) > 1:
                    # 获取第一行的配置
                    first_config = valid_configs[0]
                    text = first_config['text'].strip()
                    color = first_config['color']
                    font_size = first_config.get('fontSize', cover_font_size)
                    spacing = first_config.get('spacing', 10)

                    # 根据用户设置的行数进行自动换行
                    target_lines = len(cover_title_lines)  # 用户设置的行数
                    max_chars_per_line = max(1, len(text) // target_lines)  # 计算每行大概字符数

                    # 自动分割文字
                    auto_split_lines = self._split_title_to_lines(text, max_lines=target_lines, max_chars_per_line=max_chars_per_line)

                    logging.info(f"🔄 自动换行: 原文字='{text}', 目标行数={target_lines}, 每行字符数={max_chars_per_line}")
                    logging.info(f"🔄 换行结果: {auto_split_lines}")

                    # 为每个分割后的行创建配置
                    for i, line_text in enumerate(auto_split_lines):
                        if line_text.strip():
                            # 使用对应行的配置，如果没有则使用第一行的配置
                            if i < len(cover_title_lines):
                                line_config = cover_title_lines[i]
                                line_font_size = line_config.get('fontSize', font_size)
                                line_spacing = line_config.get('spacing', spacing)
                            else:
                                # 超出配置行数，使用第一行的默认值
                                line_font_size = font_size
                                line_spacing = spacing

                            title_lines_with_colors.append({
                                'text': line_text.strip(),
                                'color': self._convert_color_to_ffmpeg_format(color),
                                'fontSize': line_font_size,
                                'spacing': line_spacing
                            })
                else:
                    # 多行都有内容，直接使用用户配置
                    for line_config in valid_configs:
                        text = line_config['text'].strip()
                        color = line_config['color']
                        font_size = line_config.get('fontSize', cover_font_size)
                        spacing = line_config.get('spacing', 10)

                        title_lines_with_colors.append({
                            'text': text,
                            'color': self._convert_color_to_ffmpeg_format(color),
                            'fontSize': font_size,
                            'spacing': spacing
                        })

                if not title_lines_with_colors:
                    logging.warning("处理后没有有效的文本行，跳过文字叠加")
                    return cover_path

            else:
                # 使用传统的单一颜色配置
                logging.info(f"🎨 使用传统单一颜色配置: {cover_title}")
                if not cover_title.strip():
                    logging.info("没有标题文字，跳过文字叠加")
                    return cover_path

                # 处理标题文字，支持自动换行（最多三行）
                title_lines = self._split_title_to_lines(cover_title, max_lines=3, max_chars_per_line=12)
                converted_color = self._convert_color_to_ffmpeg_format(cover_font_color)

                title_lines_with_colors = []
                for line_text in title_lines:
                    if line_text.strip():
                        title_lines_with_colors.append({
                            'text': line_text.strip(),
                            'color': converted_color,
                            'fontSize': cover_font_size,
                            'spacing': 10  # 默认间距
                        })

            # 构建文字叠加滤镜
            text_filters = []

            # 检查是否所有行都使用相同的字体大小和间距
            first_font_size = title_lines_with_colors[0]['fontSize']
            first_spacing = title_lines_with_colors[0]['spacing']
            same_font_size = all(line['fontSize'] == first_font_size for line in title_lines_with_colors)
            same_spacing = all(line['spacing'] == first_spacing for line in title_lines_with_colors)

            if same_font_size and same_spacing and len(title_lines_with_colors) > 1:
                # 如果所有行使用相同的字体大小和间距，使用FFmpeg原生的多行文本功能
                logging.info(f"🔄 使用FFmpeg原生多行文本功能 (字体大小: {first_font_size}, 间距: {first_spacing})")

                # 合并所有文本行 - 使用真正的换行符
                combined_text = "\n".join([line['text'] for line in title_lines_with_colors])
                escaped_text = combined_text.replace("'", "\\'").replace(":", "\\:")

                # 检查是否所有行都使用相同颜色
                first_color = title_lines_with_colors[0]['color']
                same_color = all(line['color'] == first_color for line in title_lines_with_colors)

                if same_color:
                    # 所有行相同颜色，但仍然分别处理每行以确保正确的换行和间距
                    logging.info(f"📝 相同颜色多行文本，分别处理每行以确保正确换行")

                    font_path = self._get_chinese_font_path()
                    shadow_size = max(2, int(first_font_size / 18))

                    # 计算每行的Y位置
                    total_lines = len(title_lines_with_colors)
                    line_height = first_font_size + first_spacing
                    total_height = (total_lines - 1) * line_height + first_font_size
                    start_y = f"(h-{total_height})/2"

                    for i, line in enumerate(title_lines_with_colors):
                        line_text = line['text'].replace("'", "\\'").replace(":", "\\:")
                        y_offset = i * line_height
                        y_position = f"{start_y}+{y_offset}" if y_offset > 0 else start_y

                        text_filter = f"drawtext=text='{line_text}'{font_path}:fontsize={first_font_size}:fontcolor={first_color}:x=(w-text_w)/2:y={y_position}:shadowcolor=black:shadowx={shadow_size}:shadowy={shadow_size}"
                        text_filters.append(text_filter)

                    logging.info(f"📝 生成{total_lines}个单行文字滤镜 (相同颜色)")
                    logging.info(f"   字体大小: {first_font_size}, 颜色: {first_color}, 行间距: {first_spacing}")
                else:
                    # 不同颜色，需要分别处理每行
                    logging.info(f"🔄 不同颜色，分别处理每行")
                    for i, line_config in enumerate(title_lines_with_colors):
                        line_text = line_config['text']
                        line_color = line_config['color']
                        line_font_size = line_config['fontSize']

                        escaped_line = line_text.replace("'", "\\'").replace(":", "\\:")

                        # 计算Y坐标 - 基于行索引和字体大小+间距
                        total_lines = len(title_lines_with_colors)
                        line_height = first_font_size + first_spacing
                        total_height = total_lines * line_height - first_spacing  # 最后一行不需要间距
                        start_y = f"(h-{total_height})/2"
                        y_offset = i * line_height
                        y_pos = f"{start_y}+{y_offset}" if y_offset > 0 else start_y

                        font_path = self._get_chinese_font_path()
                        shadow_size = max(2, int(line_font_size / 18))

                        text_filter = f"drawtext=text='{escaped_line}'{font_path}:fontsize={line_font_size}:fontcolor={line_color}:x=(w-text_w)/2:y={y_pos}:shadowcolor=black:shadowx={shadow_size}:shadowy={shadow_size}"
                        text_filters.append(text_filter)

                        logging.info(f"📝 生成文字滤镜 - 行{i+1}: {escaped_line}")
                        logging.info(f"   Y坐标: {y_pos}, 颜色: {line_color}")
            else:
                # 不同字体大小或间距，分别处理每行
                logging.info(f"🔄 不同字体大小或间距，分别处理每行")

                # 计算总高度
                total_height = 0
                for i, line_config in enumerate(title_lines_with_colors):
                    line_font_size = line_config['fontSize']
                    line_spacing = line_config['spacing'] if i > 0 else 0
                    total_height += line_font_size + line_spacing

                # 为每一行创建文字滤镜
                current_y_offset = 0
                for i, line_config in enumerate(title_lines_with_colors):
                    line_text = line_config['text']
                    line_color = line_config['color']
                    line_font_size = line_config['fontSize']
                    line_spacing = line_config['spacing'] if i > 0 else 0

                    escaped_line = line_text.replace("'", "\\'").replace(":", "\\:")

                    # 计算Y坐标
                    y_pos = f"(h-{total_height})/2+{current_y_offset + line_spacing}"
                    current_y_offset += line_font_size + line_spacing

                    font_path = self._get_chinese_font_path()
                    shadow_size = max(2, int(line_font_size / 18))

                    text_filter = f"drawtext=text='{escaped_line}'{font_path}:fontsize={line_font_size}:fontcolor={line_color}:x=(w-text_w)/2:y={y_pos}:shadowcolor=black:shadowx={shadow_size}:shadowy={shadow_size}"
                    text_filters.append(text_filter)

                    logging.info(f"📝 生成文字滤镜 - 行{i+1}: {escaped_line}")
                    logging.info(f"   字体大小: {line_font_size}, 颜色: {line_color}, 间距: {line_spacing}")
                    logging.info(f"   Y坐标: {y_pos}")

            # 检查滤镜中是否包含错误的颜色格式
            for text_filter in text_filters:
                if "rgb(" in text_filter:
                    logging.error(f"❌ 错误：滤镜中包含 rgb() 格式！")

            # 组合所有滤镜
            if custom_cover_path and Path(custom_cover_path).exists():
                # 自定义封面图片不需要 select 滤镜
                video_filter = ",".join(text_filters)
            else:
                # 视频需要先选择第一帧
                video_filter = "select=eq(n\\,0)," + ",".join(text_filters)

            cmd.extend([
                "-vf", video_filter,
                "-q:v", "2",
                str(cover_path)
            ])

            logging.info(f"执行 ffmpeg 命令: {' '.join(cmd)}")
            subprocess.run(cmd, check=True, encoding='utf-8', errors='ignore')
            logging.info(f"✅ 带标题的封面图片生成成功: {cover_path}")

            return cover_path

        except subprocess.CalledProcessError as e:
            logging.error(f"❌ 封面图片生成失败: {e}")
            return None
        except Exception as e:
            logging.error(f"❌ 封面图片生成出错: {e}")
            logging.error(f"错误堆栈: {traceback.format_exc()}")
            return None

    def _split_title_to_lines(self, title, max_lines=3, max_chars_per_line=20):
        """
        将标题文字分割成多行，支持自动换行

        Args:
            title: 原始标题文字
            max_lines: 最大行数
            max_chars_per_line: 每行最大字符数

        Returns:
            list: 分割后的行列表
        """
        if not title.strip():
            return []

        # 移除多余的空白字符
        title = title.strip()

        # 首先按换行符分割
        raw_lines = title.split('\n')

        lines = []
        for raw_line in raw_lines:
            raw_line = raw_line.strip()
            if not raw_line:
                continue

            # 如果当前行不超过最大字符数，直接添加
            if len(raw_line) <= max_chars_per_line:
                lines.append(raw_line)
                if len(lines) >= max_lines:
                    break
            else:
                # 需要进一步分割长行
                current_line = ""
                for char in raw_line:
                    if len(current_line) >= max_chars_per_line:
                        # 当前行已满，换行
                        lines.append(current_line)
                        current_line = char
                        if len(lines) >= max_lines:
                            break
                    else:
                        current_line += char

                # 添加最后一行
                if current_line and len(lines) < max_lines:
                    lines.append(current_line)

                if len(lines) >= max_lines:
                    break

        # 确保不超过最大行数
        return lines[:max_lines]

    def _convert_color_to_ffmpeg_format(self, color):
        """将各种颜色格式转换为FFmpeg兼容的格式"""
        if not color:
            return "0xFFFF00"  # 默认黄色

        logging.info(f"🎨 开始处理颜色: {color} (类型: {type(color)})")

        if color.startswith('#') and len(color) == 7:
            # 十六进制颜色格式 #RRGGBB
            font_color_rgb = f"0x{color[1:]}"
            logging.info(f"✅ 使用十六进制颜色: {color} -> {font_color_rgb}")
            return font_color_rgb
        elif color.startswith('#') and len(color) == 9:
            # 带透明度的颜色格式 #RRGGBBAA
            font_color_rgb = f"0x{color[1:]}"
            logging.info(f"✅ 使用带透明度颜色: {color} -> {font_color_rgb}")
            return font_color_rgb
        elif color.startswith('rgb(') and color.endswith(')'):
            # RGB格式颜色 rgb(r, g, b)
            try:
                # 提取RGB值
                rgb_values = color[4:-1].split(',')
                if len(rgb_values) == 3:
                    r = int(rgb_values[0].strip())
                    g = int(rgb_values[1].strip())
                    b = int(rgb_values[2].strip())
                    # 转换为十六进制格式
                    font_color_rgb = f"0x{r:02x}{g:02x}{b:02x}"
                    logging.info(f"✅ RGB颜色转换: {color} -> {font_color_rgb}")
                    return font_color_rgb
                else:
                    raise ValueError("RGB格式不正确")
            except (ValueError, IndexError) as e:
                logging.error(f"❌ RGB颜色格式解析失败: {color}, 错误: {e}")
                return "0xFFFF00"  # 默认黄色
        elif color.startswith('rgba(') and color.endswith(')'):
            # RGBA格式颜色 rgba(r, g, b, a)
            try:
                # 提取RGBA值
                rgba_values = color[5:-1].split(',')
                if len(rgba_values) == 4:
                    r = int(rgba_values[0].strip())
                    g = int(rgba_values[1].strip())
                    b = int(rgba_values[2].strip())
                    a = float(rgba_values[3].strip())
                    # 转换为十六进制格式（包含透明度）
                    alpha = int(a * 255) if a <= 1.0 else int(a)
                    font_color_rgb = f"0x{r:02x}{g:02x}{b:02x}{alpha:02x}"
                    logging.info(f"✅ RGBA颜色转换: {color} -> {font_color_rgb}")
                    return font_color_rgb
                else:
                    raise ValueError("RGBA格式不正确")
            except (ValueError, IndexError) as e:
                logging.error(f"❌ RGBA颜色格式解析失败: {color}, 错误: {e}")
                return "0xFFFF00"  # 默认黄色
        else:
            # 使用颜色名称或其他格式
            font_color_rgb = color if color else "yellow"
            logging.info(f"✅ 使用颜色名称: {font_color_rgb}")
            return font_color_rgb

    def _get_chinese_font_path(self):
        """
        获取中文字体路径，优先使用项目中的字体文件，兼容 pyinstaller 打包

        Returns:
            str: 字体路径参数，如果找到字体则返回 :fontfile='path'，否则返回空字符串
        """
        import platform
        import sys
        import os

        # 获取当前脚本的目录，兼容 pyinstaller 打包
        if getattr(sys, 'frozen', False):
            # 如果是打包后的可执行文件
            base_path = Path(sys.executable).parent
        else:
            # 如果是源码运行
            base_path = Path(__file__).parent.parent  # 从 services 目录回到 backends 目录

        # 项目中的字体文件路径
        project_font_path = base_path / "utils" / "font.ttf"

        # 字体候选路径列表
        font_candidates = [str(project_font_path)]  # 优先使用项目字体

        if platform.system() == "Windows":
            # Windows 系统中文字体路径作为备选
            font_candidates.extend([
                "C:/Windows/Fonts/msyh.ttc",      # 微软雅黑
                "C:/Windows/Fonts/msyhbd.ttc",    # 微软雅黑粗体
                "C:/Windows/Fonts/simhei.ttf",    # 黑体
                "C:/Windows/Fonts/simsun.ttc",    # 宋体
                "C:/Windows/Fonts/simkai.ttf",    # 楷体
            ])
        else:
            # Linux/Mac 系统中文字体路径作为备选
            font_candidates.extend([
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/System/Library/Fonts/PingFang.ttc",  # Mac
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",  # Linux
            ])

        # 检查字体文件是否存在
        for font_path in font_candidates:
            if Path(font_path).exists():
                # 转义路径中的特殊字符，确保路径格式正确
                escaped_path = str(font_path).replace("\\", "/").replace(":", "\\:")
                logging.info(f"✅ 找到字体文件: {font_path}")
                logging.info(f"   转义后路径: {escaped_path}")
                return f":fontfile='{escaped_path}'"

        # 如果没有找到字体文件，返回空字符串（使用系统默认字体）
        logging.warning("⚠️ 未找到中文字体文件，将使用系统默认字体，可能导致中文显示为方块")
        return ""


# 创建服务实例
detail_enquire_service = DetailEnquireService()


async def process_detail_enquire_request(url, cookie, download=True, proxy='',
                                       video_generation_key='', auto_ab=False, auto_publish=False,
                                       cover_title='', custom_cover_path='',
                                       cover_font_size=72, cover_font_color='#FFFF00',
                                       cover_title_lines=None):
    """
    处理 detailEnquire 请求的主入口函数

    Args:
        url: 快手视频链接
        cookie: 用户cookie
        download: 是否下载视频
        proxy: 代理设置
        video_generation_key: 视频生成密钥
        auto_ab: 是否启用AB去重
        auto_publish: 是否自动发布
        cover_title: 封面标题文字
        custom_cover_path: 自定义封面图片路径
        cover_font_size: 封面标题字体大小
        cover_font_color: 封面标题字体颜色

    Returns:
        dict: 处理结果
    """
    return await detail_enquire_service.process_detail_enquire(
        url=url,
        cookie=cookie,
        download=download,
        proxy=proxy,
        video_generation_key=video_generation_key,
        auto_ab=auto_ab,
        auto_publish=auto_publish,
        cover_title=cover_title,
        custom_cover_path=custom_cover_path,
        cover_font_size=cover_font_size,
        cover_font_color=cover_font_color,
        cover_title_lines=cover_title_lines
    )
