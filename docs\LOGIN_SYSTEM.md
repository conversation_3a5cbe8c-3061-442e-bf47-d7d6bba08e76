# 登录系统说明文档

## 概述

本系统实现了基于卡密的登录认证机制，支持token自动刷新和路由守卫。认证服务与主应用服务分离，使用不同的服务器域名。

## 架构设计

### 服务器分离
- **主应用服务器**: 处理业务逻辑（账号管理、文件管理、发布等）
- **认证服务器**: 专门处理登录认证、token管理

### 技术栈
- Vue 3 + Composition API
- Pinia 状态管理
- Vue Router 路由管理
- Element Plus UI组件
- Tauri HTTP插件

## 配置说明

### 环境变量配置

#### 开发环境 (`.env.development`)
```env
# 主应用 API 基础地址
VITE_API_BASE_URL=http://localhost:5409

# 认证服务器 API 基础地址
VITE_AUTH_BASE_URL=http://localhost:8080
```

#### 生产环境 (`.env.production`)
```env
# 主应用 API 基础地址
VITE_API_BASE_URL=http://localhost:5409

# 认证服务器 API 基础地址
VITE_AUTH_BASE_URL=https://auth.yourdomain.com
```

### API端点配置

认证相关端点（使用认证服务器）：
- `POST /api/auth/login` - 登录
- `POST /api/auth/refresh` - 刷新token
- `POST /api/auth/logout` - 登出
- `GET /api/auth/verify` - 验证token

## 功能特性

### 1. 登录认证
- 卡密验证登录
- 自动保存用户信息到localStorage
- 登录成功后跳转到首页

### 2. Token管理
- 自动保存access_token、refresh_token、expires_at
- 定时检查token过期状态（每分钟检查一次）
- token即将过期时自动刷新（提前5分钟）
- token失效时自动登出并跳转登录页

### 3. 路由守卫
- 未登录用户自动跳转到登录页
- 已登录用户访问登录页自动跳转到首页
- 页面刷新时自动恢复登录状态

### 4. 请求拦截
- 自动在请求头添加Authorization Bearer token
- 401错误自动处理（清除登录状态，跳转登录页）
- 区分认证请求和业务请求的错误处理

## 文件结构

```
src/
├── views/
│   └── Login.vue                 # 登录页面
├── api/
│   └── auth.js                   # 认证API
├── utils/
│   ├── request.js                # 主应用请求工具
│   └── authRequest.js            # 认证服务请求工具
├── stores/
│   └── user.js                   # 用户状态管理
├── components/
│   └── AuthGuard.vue             # 认证守卫组件
├── config/
│   └── api.js                    # API配置文件
└── router/
    └── index.js                  # 路由配置（含路由守卫）
```

## 使用方法

### 1. 登录流程
1. 用户访问任何需要认证的页面
2. 路由守卫检查登录状态
3. 未登录则跳转到登录页
4. 用户输入卡密点击登录
5. 调用认证服务器验证卡密
6. 验证成功后保存token信息
7. 跳转到首页并启动token刷新定时器

### 2. 自动token刷新
1. 系统每分钟检查一次token状态
2. 如果token将在5分钟内过期，自动调用刷新接口
3. 刷新成功则更新token信息
4. 刷新失败则清除登录状态并跳转登录页

### 3. 请求认证
所有业务API请求会自动携带以下请求头：
```javascript
{
  'Accept': 'application/json',
  'Authorization': `Bearer ${access_token}`
}
```

## 自定义配置

### 修改认证服务器地址
在对应的环境变量文件中修改 `VITE_AUTH_BASE_URL`

### 修改token刷新策略
在 `src/stores/user.js` 中修改：
- 检查间隔：修改 `setInterval` 的时间参数
- 提前刷新时间：修改 `isTokenExpiringSoon` 计算逻辑

### 修改API端点
在 `src/config/api.js` 中修改 `API_ENDPOINTS` 配置

## 错误处理

### 网络错误
- 无法连接认证服务器时显示友好提示
- 自动重试机制（可配置）

### 认证错误
- 401: 自动登出并跳转登录页
- 403: 显示访问被拒绝提示
- 404: 显示认证服务不可用提示
- 5xx: 显示服务器错误提示

### 业务错误
- 根据响应的错误码和消息显示对应提示
- 支持多种响应格式（code/success/status字段）

## 安全考虑

1. **Token存储**: 使用localStorage存储，页面关闭后保持登录状态
2. **自动过期**: token过期后自动清除，需要重新登录
3. **请求隔离**: 认证请求与业务请求使用不同的服务器
4. **错误处理**: 敏感错误信息不暴露给用户

## 开发调试

### 启用调试日志
所有请求都会在控制台输出详细日志，包括：
- 请求URL、方法、头部、数据
- 响应状态、内容
- 错误信息

### 测试登录功能
1. 确保认证服务器正常运行
2. 配置正确的 `VITE_AUTH_BASE_URL`
3. 使用有效的卡密进行测试
4. 检查浏览器localStorage中的用户信息
