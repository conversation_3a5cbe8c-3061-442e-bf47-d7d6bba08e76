#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import hashlib
import json
import re
from urllib.parse import unquote, quote

# 简化分析
user_id = "4770953772"
user_eid = "3xxh7bifwy6xjca"
carrier_id = "v1mLnht1qSI"
photo_id = "5192369066199894133"

print("=== carrierId 解密分析 ===")
print(f"userId: {user_id}")
print(f"userEid: {user_eid}")
print(f"carrierId: {carrier_id}")
print()

# Base64 解码尝试
print("1. Base64 解码尝试:")
try:
    decoded = base64.b64decode(carrier_id + "==").decode('utf-8', errors='ignore')
    print(f"   直接解码: {repr(decoded)}")
except Exception as e:
    print(f"   直接解码失败: {e}")

try:
    decoded = base64.urlsafe_b64decode(carrier_id + "==").decode('utf-8', errors='ignore')
    print(f"   URL安全解码: {repr(decoded)}")
except Exception as e:
    print(f"   URL安全解码失败: {e}")

# 哈希尝试
print("\n2. 哈希分析:")
test_strings = [user_id, user_eid, photo_id]

for test_str in test_strings:
    # MD5 -> Base64
    md5_hash = hashlib.md5(test_str.encode()).digest()
    md5_b64 = base64.b64encode(md5_hash).decode().rstrip('=')
    print(f"   Base64(MD5({test_str})): {md5_b64}")
    if carrier_id in md5_b64:
        print(f"   ✅ 在Base64编码的MD5中找到匹配!")

# 字符分析
print(f"\n3. 字符分析:")
print(f"   carrierId: {carrier_id} (长度: {len(carrier_id)})")
print(f"   userEid: {user_eid} (长度: {len(user_eid)})")

# 检查是否是userEid的变换
if len(carrier_id) == len(user_eid):
    print("   长度相同，检查字符映射:")
    for i, (c1, c2) in enumerate(zip(carrier_id, user_eid)):
        print(f"   位置{i}: '{c1}' <-> '{c2}'")

# 尝试简单的编码
print(f"\n4. 简单编码尝试:")
# 尝试将userEid作为不同进制解析
try:
    # 36进制
    num = int(user_eid, 36)
    print(f"   userEid作为36进制数: {num}")

    # 转换为62进制 (0-9, a-z, A-Z)
    alphabet = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    def to_base62(num):
        if num == 0:
            return "0"
        result = ""
        while num > 0:
            result = alphabet[num % 62] + result
            num //= 62
        return result

    base62_result = to_base62(num)
    print(f"   转换为62进制: {base62_result}")
    if base62_result == carrier_id:
        print(f"   ✅ 找到匹配! userEid -> 36进制数 -> 62进制编码")

except ValueError:
    print("   userEid不是有效的36进制数")

# 进一步分析 - 尝试截取和组合
print(f"\n5. 进一步分析:")

# 尝试从userEid中提取部分字符
print(f"   userEid: {user_eid}")
print(f"   carrierId: {carrier_id}")

# 检查carrierId是否是userEid的子串或变换
if carrier_id in user_eid:
    print(f"   ✅ carrierId是userEid的子串!")

# 尝试不同的截取方式
for start in range(len(user_eid)):
    for end in range(start + len(carrier_id), len(user_eid) + 1):
        substr = user_eid[start:end]
        if len(substr) == len(carrier_id):
            print(f"   userEid[{start}:{end}] = {substr}")
            if substr == carrier_id:
                print(f"   ✅ 找到完全匹配!")

# 尝试Base64编码userEid的不同部分
print(f"\n6. Base64编码userEid的不同部分:")
for i in range(len(user_eid) - 7):  # 至少8个字符才能产生11个字符的base64
    substr = user_eid[i:i+8]
    try:
        encoded = base64.b64encode(substr.encode()).decode().rstrip('=')
        print(f"   Base64({substr}) = {encoded}")
        if encoded == carrier_id:
            print(f"   ✅ 找到匹配!")
    except:
        pass

# 尝试MD5哈希userEid然后截取
print(f"\n7. MD5哈希userEid然后截取:")
md5_hex = hashlib.md5(user_eid.encode()).hexdigest()
print(f"   MD5(userEid) = {md5_hex}")

# 尝试将MD5的不同部分转换为base64
for i in range(0, len(md5_hex), 8):
    hex_part = md5_hex[i:i+8]
    if len(hex_part) == 8:
        try:
            # 将16进制转换为字节，然后base64编码
            bytes_part = bytes.fromhex(hex_part)
            b64_part = base64.b64encode(bytes_part).decode().rstrip('=')
            print(f"   Base64(hex[{i}:{i+8}]) = {b64_part}")
            if b64_part == carrier_id:
                print(f"   ✅ 找到匹配!")
        except:
            pass

# 尝试其他可能的编码方式
print(f"\n8. 其他编码尝试:")

# 尝试将userEid转换为数字，然后用不同的方法编码
try:
    # 计算userEid的各种数值表示
    ascii_sum = sum(ord(c) for c in user_eid)
    print(f"   userEid ASCII码总和: {ascii_sum}")

    # 尝试将这个数字转换为base64
    ascii_bytes = ascii_sum.to_bytes((ascii_sum.bit_length() + 7) // 8, 'big')
    ascii_b64 = base64.b64encode(ascii_bytes).decode().rstrip('=')
    print(f"   ASCII总和的Base64: {ascii_b64}")

    # 尝试CRC32
    import zlib
    crc32_val = zlib.crc32(user_eid.encode()) & 0xffffffff
    print(f"   CRC32: {crc32_val}")
    crc32_bytes = crc32_val.to_bytes(4, 'big')
    crc32_b64 = base64.b64encode(crc32_bytes).decode().rstrip('=')
    print(f"   CRC32的Base64: {crc32_b64}")

except Exception as e:
    print(f"   编码尝试失败: {e}")

# 尝试查看carrierId是否包含特定模式
print(f"\n9. carrierId模式分析:")
print(f"   carrierId: {carrier_id}")
print(f"   字符分布: {[c for c in carrier_id]}")

# 检查是否是某种时间戳或ID的编码
import time
current_time = int(time.time())
print(f"   当前时间戳: {current_time}")

# 尝试将carrierId解码为数字
try:
    # 尝试将carrierId当作base64解码后转换为数字
    decoded_bytes = base64.b64decode(carrier_id + "==")
    if len(decoded_bytes) >= 4:
        decoded_int = int.from_bytes(decoded_bytes[:4], 'big')
        print(f"   carrierId解码为整数: {decoded_int}")

        # 检查是否是时间戳
        if 1000000000 < decoded_int < 2000000000:  # 大致的时间戳范围
            import datetime
            dt = datetime.datetime.fromtimestamp(decoded_int)
            print(f"   可能的时间戳: {dt}")

except Exception as e:
    print(f"   数字解码失败: {e}")

print(f"\n10. 最终推测:")
print(f"   carrierId '{carrier_id}' 可能是:")
print(f"   1. 快手平台的内部ID编码")
print(f"   2. 基于userEid的某种哈希或编码算法")
print(f"   3. 包含时间戳或其他元数据的复合编码")
print(f"   4. 使用了快手特有的编码算法")

print("\n分析完成!")

# 直接运行分析
