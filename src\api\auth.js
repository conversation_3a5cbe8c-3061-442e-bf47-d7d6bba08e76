import { authHttp } from '@/utils/authRequest'
import { API_ENDPOINTS } from '@/config/api'
import { getMachineInfo } from '@/utils/machine';

/**
 * 登录API
 * @param {string} cardKey - 卡密
 * @returns {Promise} 登录结果
 */
export const loginApi = async(cardKey) => {
  const machineInfo = await getMachineInfo();
  console.log(machineInfo );
  return authHttp.post(API_ENDPOINTS.AUTH.LOGIN, {
    secret: cardKey,
    'mac_address': machineInfo?.mac || '',
    'ip_address': machineInfo?.ip_address|| '',
    'machine_name': machineInfo?.machine_name || ''
  })
}

/**
 * 刷新token API
 * @param {string} refreshToken - 刷新token
 * @param {string} accessToken - 当前的access_token（可选，会自动从localStorage获取）
 * @returns {Promise} 新的token信息
 */
export const refreshTokenApi = (refreshToken, accessToken = null) => {
  console.log('[refreshTokenApi] 开始刷新token，refresh_token:', refreshToken?.substring(0, 20) + '...')

  // 如果没有传入accessToken，尝试从localStorage获取
  if (!accessToken) {
    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      accessToken = userInfo.access_token
    } catch (error) {
      console.warn('[refreshTokenApi] 无法从localStorage获取access_token:', error)
    }
  }

  const requestData = {
    refresh_token: refreshToken
  }

  // 如果有access_token，也可以添加到请求体中（根据后端API要求）
  if (accessToken) {
    console.log('[refreshTokenApi] 当前access_token:', accessToken.substring(0, 20) + '...')
    // requestData.access_token = accessToken // 如果后端需要的话可以取消注释
  }

  return authHttp.post(API_ENDPOINTS.AUTH.REFRESH, requestData)
}

/**
 * 登出API
 * @returns {Promise} 登出结果
 */
export const logoutApi = () => {
  return authHttp.post(API_ENDPOINTS.AUTH.LOGOUT)
}

/**
 * 验证token有效性
 * @returns {Promise} 验证结果
 */
export const verifyTokenApi = () => {
  return authHttp.get(API_ENDPOINTS.AUTH.VERIFY)
}
