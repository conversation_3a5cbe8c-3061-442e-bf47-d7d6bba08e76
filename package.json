{"name": "tauri-python-sidecar", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "build:sidecar-winos": "pyinstaller main-x86_64-pc-windows-msvc.spec", "build:sidecar-macos": "pyinstaller -c -F --clean --name main-aarch64-apple-darwin --distpath src-tauri/bin/api backends/main.py", "build:sidecar-linux": "pyinstaller -c -F --clean --name main-x86_64-unknown-linux-gnu --distpath src-tauri/bin/api backends/main.py", "build:icons": "pnpm tauri icon src/assets/app-icon.png"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tauri-apps/api": "^2.0.0-alpha.11", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-fs": "^2.3.0", "@tauri-apps/plugin-http": "~2.4.4", "@tauri-apps/plugin-shell": "^2.2.1", "axios": "^1.9.0", "devtools-detector": "^2.0.23", "element-plus": "^2.9.11", "pinia": "^3.0.2", "sass": "^1.89.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0-alpha.17", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^6.3.5", "vue-tsc": "^1.8.5"}, "packageManager": "pnpm@10.4.1+sha512.c753b6c3ad7afa13af388fa6d808035a008e30ea9993f58c6663e2bc5ff21679aa834db094987129aa4d488b86df57f7b634981b2f827cdcacc698cc0cfb88af"}