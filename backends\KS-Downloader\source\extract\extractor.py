from datetime import datetime
import logging
from re import compile
from time import localtime, strftime
from types import SimpleNamespace
from typing import TYPE_CHECKING
from urllib.parse import parse_qs

from lxml.etree import HTML
from yaml import safe_load

from ..tools import Namespace
from ..translation import _

if TYPE_CHECKING:
    from ..manager import Manager


class HTMLExtractor:
    SCRIPT = "//script/text()"
    WEB_KEYWORD = "window.__APOLLO_STATE__="
    APP_KEYWORD = "window.INIT_STATE = "
    PHOTO_REGEX = compile(r"\"photo\":(\{\".*\"}),\"serialInfo\"")

    def __init__(self, manager: "Manager"):
        self.date_format = "%Y-%m-%d_%H:%M:%S"
        self.console = manager.console
        self.cleaner = manager.cleaner

    def run(
        self,
        html: str,
        id_: str,
        web: bool,
    ) -> dict:
        tree = self.__extract_object(
            html,
            web,
        )
        data = self.__convert_object(
            tree,
            web,
        )
        if not data:
            self.console.warning(_("提取网页数据失败"))
            return {}
        data = Namespace(data)
        return self.__extract_detail(
            data,
            id_,
            web,
            html=html,
        )

    def __extract_object(
        self,
        html: str,
        web: bool,
    ) -> str:
        if not html:
            self.console.warning(_("获取网页内容失败"))
            return ""
        html_tree = HTML(html)
        if not (data := html_tree.xpath(self.SCRIPT)):
            return ""
        keyword = self.WEB_KEYWORD if web else self.APP_KEYWORD
        for i in data:
            if keyword in i:
                return i
        raise ValueError(_("提取网页数据失败"))

    def __convert_object(
        self,
        text: str,
        web: bool,
    ) -> dict:
        if web:
            text = text.lstrip(self.WEB_KEYWORD if web else self.APP_KEYWORD)
            text = text.replace(
                ";(function(){var s;(s=document.currentScript||document.scripts["
                "document.scripts.length-1]).parentNode.removeChild(s);}());",
                "",
            )
        else:
            text = text[1] if (text := self.PHOTO_REGEX.search(text)) else ""
        return safe_load(text)

    def __extract_detail(
        self,
        data: Namespace,
        id_: str,
        web: bool,
        html: str = None,
    ) -> dict:
        print(f"[DEBUG] __extract_detail 被调用，web={web}, id_={id_}")
        if web:
            print(f"[DEBUG] 使用WEB提取模式")
            return self.__extract_detail_web(data, id_, html=html)
        else:
            print(f"[DEBUG] 使用APP提取模式")
            return self.__extract_detail_app(data, id_, html=html)

    def __extract_detail_app(
        self,
        data: Namespace,
        id_: str,
        html: str = None,
    ) -> dict:
        print(f"[DEBUG-APP] __extract_detail_app 开始，id_: {id_}")
        
        # 判断内容类型
        photo_type_raw = data.safe_extract("photoType", "")
        print(f"[DEBUG-APP] 原始 photoType: {photo_type_raw}")
        
        # 根据实际类型判断
        if photo_type_raw == "VIDEO":
            photo_type = _("视频")
            duration = APIExtractor.time_conversion(data.safe_extract("duration", 0))
        else:
            photo_type = _("图片")
            duration = "00:00:00"
        
        print(f"[DEBUG-APP] 处理后 photoType: {photo_type}")
        
        container = {
            "collection_time": datetime.now().strftime(self.date_format),
            "photoType": photo_type,
            "detailID": id_,
            "caption": data.safe_extract(
                "caption",
            ),
            "coverUrl": self._extract_cover_urls(data),
            "duration": duration,
            "realLikeCount": data.safe_extract(
                "likeCount",
                -1,
            ),
            "download": self._extract_download_urls(data, html=html),
            "timestamp": APIExtractor.format_date(
                data.safe_extract(
                    "timestamp",
                    0,
                ),
                self.date_format,
            ),
            "viewCount": data.safe_extract(
                "viewCount",
                -1,
            ),
            "shareCount": data.safe_extract(
                "shareCount",
                -1,
            ),
            "commentCount": data.safe_extract(
                "commentCount",
                -1,
            ),
            "userSex": APIExtractor.USER_SEX.get(
                data.safe_extract(
                    "userSex",
                ),
            ),
            "authorID": data.safe_extract(
                "userEid",
            ),
            "name": data.safe_extract(
                "userName",
            ),
        }
        
        # 添加购物数据提取，APP版本也需要支持
        self.__extract_shopping_data_web(container, data, id_, html=html)
        return container

    def __extract_detail_web(self, data: Namespace, id_: str, html: str = None) -> dict:
        data = data.safe_extract("defaultClient")
        detail = f"VisionVideoDetailPhoto:{id_}"
        if not Namespace.object_extract(data, detail):
            return {}
        container = {
            "collection_time": datetime.now().strftime(self.date_format),
            "photoType": _("视频"),
            "detailID": id_,
            "caption": Namespace.object_extract(
                data,
                f"{detail}.caption",
            ),
            "coverUrl": Namespace.object_extract(
                data,
                f"{detail}.coverUrl",
            ),
            "duration": APIExtractor.time_conversion(
                Namespace.object_extract(
                    data,
                    f"{detail}.duration",
                    0,
                )
            ),
            "realLikeCount": Namespace.object_extract(
                data,
                f"{detail}.realLikeCount",
                -1,
            ),
            "download": [
                Namespace.object_extract(
                    data,
                    f"{detail}.photoUrl",
                )
            ],
            "timestamp": APIExtractor.format_date(
                Namespace.object_extract(
                    data,
                    f"{detail}.timestamp",
                    0,
                ),
                self.date_format,
            ),
            "viewCount": Namespace.object_extract(
                data,
                f"{detail}.viewCount",
                -1,
            ),
            "shareCount": -1,
            "commentCount": -1,
        }
        self.__extract_author_web(container, data)
        logging.info(f"[购物数据] 即将调用 __extract_shopping_data_web，作品ID: {id_}")
        self.__extract_shopping_data_web(container, data, id_, html=html)
        logging.info(f"[购物数据] __extract_shopping_data_web 调用完成")
        return container

    @staticmethod
    def _extract_cover_urls(data: Namespace) -> str:
        cover_urls = data.safe_extract("coverUrls", [])
        cover_urls = [i.url for i in cover_urls]
        return cover_urls[0] if cover_urls else ""

    @staticmethod
    def _extract_download_urls(
        data: Namespace,
        index=0,
        html: str = None,
    ) -> list[str]:
        print(f"[DEBUG] _extract_download_urls 被调用，index={index}")
        
        # 首先检查内容类型
        photo_type = data.safe_extract("photoType", "")
        print(f"[DEBUG] 内容类型: {photo_type}")
        
        if photo_type == "VIDEO":
            # 视频类型：只从HTML中提取v2.kwaicdn.com/upic的MP4 URL
            
            if html:
                import re
                
                # 只提取v2.kwaicdn.com/upic的URL
                target_pattern = r'https://v2\.kwaicdn\.com/[^"]*\.mp4[^"]*'
                target_urls = re.findall(target_pattern, html)
                if target_urls:
                    best_url = target_urls[0]
                    print(f"[DEBUG] 直接命中目标视频(v2.kwaicdn.com/upic): {best_url}")
                    return [best_url]
            
            print(f"[DEBUG] 未找到v2.kwaicdn.com/upic视频，返回空列表")
            return []
        
        # 非视频类型，返回空列表
        print(f"[DEBUG] 非视频类型，返回空列表")
        return []

    @staticmethod
    def __extract_author_web(container: dict, data: Namespace) -> None:
        author = next(
            (getattr(data, i) for i in dir(data) if "VisionVideoDetailAuthor:" in i),
            None,
        )
        container["authorID"] = Namespace.object_extract(
            author,
            "id",
        )
        container["name"] = Namespace.object_extract(
            author,
            "name",
        )
        container["userSex"] = "未知"

    @staticmethod
    def __extract_shopping_data_web(container: dict, data: Namespace, id_: str, html: str = None) -> None:
        """从INIT_STATE对象中提取购物数据 - 新版本"""
        import logging
        import json
        import re
        import urllib.parse
        shopping_data = []

        # 添加调试日志
        logging.info(f"[购物数据] 开始提取购物数据，作品ID: {id_} (新版本)")

        try:
            # 方法1: 从INIT_STATE对象中直接提取属性参数
            # 优先使用传入的HTML内容解析INIT_STATE
            init_state_data = None
            if html:
                init_state_match = re.search(r'window\.INIT_STATE\s*=\s*({.*?})</script>', html, re.DOTALL)
                if init_state_match:
                    try:
                        init_state_str = init_state_match.group(1)
                        init_state_data = json.loads(init_state_str)
                        print(f"[DEBUG] 成功解析INIT_STATE对象")
                    except json.JSONDecodeError as e:
                        print(f"[DEBUG] 解析INIT_STATE失败: {e}")

            # 如果从HTML解析失败，尝试从data中查找INIT_STATE相关数据
            if not init_state_data:
                for key in dir(data):
                    if key.startswith('_'):
                        continue
                    try:
                        obj = getattr(data, key)
                        if isinstance(obj, str) and 'window.INIT_STATE' in obj:
                            init_state_match = re.search(r'window\.INIT_STATE\s*=\s*({.*?})</script>', obj, re.DOTALL)
                            if init_state_match:
                                init_state_str = init_state_match.group(1)
                                init_state_data = json.loads(init_state_str)
                                print(f"[DEBUG] 从属性 {key} 中成功解析INIT_STATE对象")
                                break
                    except Exception as e:
                        continue

            # 从INIT_STATE对象中提取购物信息
            if init_state_data:
                print(f"[DEBUG] 开始从INIT_STATE对象中提取购物信息")

                # 先提取产品信息，获取categoryType
                category_type = None
                for key, value in init_state_data.items():
                    if isinstance(value, dict) and 'plcEntryInfo' in value:
                        plc_entry_info = value['plcEntryInfo']
                        if isinstance(plc_entry_info, dict):
                            style_info = plc_entry_info.get('styleInfo', {})
                            weak_style = style_info.get('weakStyle', {})

                            if weak_style.get('category') == '购物':
                                category_type = plc_entry_info.get('categoryType', 29)  # 默认值29
                                product_info = {
                                    'type': 'product_info',
                                    'category': weak_style.get('category', ''),
                                    'title': weak_style.get('title', ''),
                                    'iconUrl': weak_style.get('iconUrl', ''),
                                    'bizType': plc_entry_info.get('bizType', ''),
                                    'categoryType': category_type,
                                    'source': 'init_state_plc'
                                }
                                shopping_data.append(product_info)
                                print(f"[DEBUG] 从plcEntryInfo提取产品信息: {weak_style.get('title', 'N/A')}, categoryType: {category_type}")
                                break

                # 然后提取用户商店信息，使用categoryType作为carrierType
                for key, value in init_state_data.items():
                    if isinstance(value, dict) and 'result' in value and value.get('result') == 1:
                        # 查找包含photo信息的对象
                        if 'photo' in value:
                            photo_data = value['photo']
                            user_id = photo_data.get('userId')
                            user_name = photo_data.get('userName', '')
                            kwai_id = photo_data.get('kwaiId', '')
                            photo_id = photo_data.get('photoId', '')
                            user_eid = photo_data.get('userEid', '')

                            logging.info(f"[购物数据] 从INIT_STATE对象中找到photo数据: {photo_data}")
                            if user_id:
                                # 构建用户商店链接
                                # 参考格式: https://app.kwaixiaodian.com/page/kwaishop-store-c-frame-h5/frame?layoutType=4&hyId=kwaishop-store-c-frame-h5&sellerId=8737306&authorId=8737306&carrierType=29&carrierId=v1mLnht1qSI&entrance=p&_refer=KWAISHOP_C_SHOPLIST

                                # carrierId 生成策略：
                                # 1. 优先使用 userEid (快手用户的编码ID)
                                # 2. 如果没有 userEid，尝试生成类似格式的ID
                                # 3. 最后使用 photoId 作为备选
                                if user_eid:
                                    carrier_id = user_eid
                                    print(f"[DEBUG] 使用 userEid 作为 carrierId: {carrier_id}")
                                elif photo_id:
                                    # 尝试从photoId生成类似userEid格式的carrierId
                                    import hashlib
                                    import base64
                                    # 使用photoId生成一个11字符的编码ID
                                    hash_input = f"{photo_id}_{user_id}"
                                    hash_digest = hashlib.md5(hash_input.encode()).digest()
                                    # 使用base64编码并截取11个字符，模拟userEid格式
                                    carrier_id = base64.b64encode(hash_digest).decode()[:11].replace('+', 'x').replace('/', 'y')
                                    print(f"[DEBUG] 基于 photoId 生成 carrierId: {carrier_id}")
                                else:
                                    # 最后备选：直接使用userId
                                    carrier_id = str(user_id)
                                    print(f"[DEBUG] 使用 userId 作为 carrierId: {carrier_id}")

                                # 使用从product_info提取的categoryType作为carrierType，如果没有则使用默认值29
                                carrier_type = category_type if category_type is not None else 29

                                shop_link = f"https://app.kwaixiaodian.com/page/kwaishop-store-c-frame-h5/frame?layoutType=4&hyId=kwaishop-store-c-frame-h5&sellerId={user_id}&authorId={user_id}&carrierType={carrier_type}&carrierId={carrier_id}&entrance=p&_refer=KWAISHOP_C_SHOPLIST"
                                shop_info = {
                                    'type': 'user_shop',
                                    'sellerId': str(user_id),
                                    'authorId': str(user_id),
                                    'userName': user_name,
                                    'kwaiId': kwai_id,
                                    'photoId': photo_id,
                                    'userEid': user_eid,
                                    'carrierId': carrier_id,
                                    'carrierType': carrier_type,
                                    'title': f"{user_name}的小店",
                                    'link': shop_link,
                                    'source': 'init_state_photo'
                                }
                                shopping_data.append(shop_info)
                                print(f"[DEBUG] 从photo数据提取用户商店: {user_name} (ID: {user_id}), carrierType: {carrier_type}")
                                break
            else:
                print(f"[DEBUG] 未找到INIT_STATE数据")

            # 方法2: 递归搜索Namespace对象 (fallback)
            
            
            # 将购物数据添加到容器中
            if shopping_data:
                for i, item in enumerate(shopping_data):
                    print(f"[DEBUG] 购物数据 {i+1}: {item}")
                    logging.info(f"[购物数据] 购物数据 {i+1}: {item}")
                # 直接存储购物数据数组，不序列化为JSON字符串
                container['shopping_data'] = shopping_data
            else:
                print(f"[DEBUG] 未找到购物数据")
                logging.info(f"[购物数据] 未找到购物数据")
                container['shopping_data'] = []  # 空数组
                
        except Exception as e:
            # 购物数据提取失败不应该影响主要数据提取
            print(f"[DEBUG] 购物数据提取异常: {e}")
            logging.error(f"[购物数据] 购物数据提取异常: {e}")
            import traceback
            print(f"[DEBUG] 异常堆栈: {traceback.format_exc()}")
            logging.error(f"[购物数据] 异常堆栈: {traceback.format_exc()}")


class APIExtractor:
    PHOTO_TYPE = {
        "VIDEO": "视频",
        "VERTICAL_ATLAS": "图片",
        "HORIZONTAL_ATLAS": "图片",
    }
    USER_SEX = {
        "F": "女",
        "": "未知",
        "M": "男",
    }

    def __init__(self, manager: "Manager"):
        self.date_format = "%Y-%m-%d_%H:%M:%S"
        self.console = manager.console
        self.cleaner = manager.cleaner

    @staticmethod
    def generate_data_object(data: dict) -> SimpleNamespace | list[SimpleNamespace]:
        return Namespace.generate_data_object(data)

    @staticmethod
    def safe_extract(
        data: SimpleNamespace,
        attribute_chain: str,
        default: str | int | list | dict | SimpleNamespace = "",
    ):
        attributes = attribute_chain.split(".")
        for attribute in attributes:
            if "[" in attribute:
                parts = attribute.split("[", 1)
                attribute = parts[0]
                index = parts[1].split("]", 1)[0]
                try:
                    index = int(index)
                    data = getattr(data, attribute, None)[index]
                except (IndexError, TypeError, ValueError):
                    return default
            else:
                data = getattr(data, attribute, None)
                if not data:
                    return default
        return data or default

    def run(self, data: list[dict], type_="detail") -> list[dict]:
        container = []
        if not data:
            return container
        match type_:
            case "detail":
                [
                    self.__extract_items(container, self.generate_data_object(item))
                    for item in data
                ]
            case "user":
                pass
            case _:
                raise ValueError
        return container

    def __extract_items(self, container: list, data: SimpleNamespace) -> None:
        item = {
            "collection_time": datetime.now().strftime(self.date_format),
        }
        self.__extract_comments(item, data)
        self.__extract_counts(item, data)
        self.__extract_photo(item, data)
        photo_type = item["photoType"]
        if photo_type == _("视频"):
            self.__extract_music(item, data, True)
            self.__extract_mp4(item, data)
        elif photo_type == _("图片"):
            self.__extract_music(item, data, False)
            self.__extract_atlas(item, data)
        else:
            item["download"] = ""
        container.append(item)

    def __extract_comments(self, item: dict, data: SimpleNamespace) -> None:
        pass

    def __extract_counts(self, item: dict, data: SimpleNamespace) -> None:
        item["fanCount"] = self.safe_extract(data, "counts.fanCount", -1)
        item["followCount"] = self.safe_extract(data, "counts.followCount", -1)
        item["collectionCount"] = self.safe_extract(data, "counts.collectionCount", -1)
        item["photoCount"] = self.safe_extract(data, "counts.photoCount", -1)

    def __extract_photo(self, item: dict, data: SimpleNamespace) -> None:
        photo = self.safe_extract(data, "photo")
        item["timestamp"] = self.format_date(
            self.safe_extract(photo, "timestamp", 0),
            self.date_format,
        )
        item["duration"] = self.time_conversion(self.safe_extract(photo, "duration", 0))
        item["userName"] = self.safe_extract(photo, "userName")
        item["userId"] = self.safe_extract(photo, "userId")
        item["commentCount"] = self.safe_extract(photo, "commentCount", 0)
        item["viewCount"] = self.safe_extract(photo, "viewCount", 0)
        self.__extract_cover(item, photo)
        item["height"] = self.safe_extract(photo, "height", -1)
        item["width"] = self.safe_extract(photo, "width", -1)
        item["likeCount"] = self.safe_extract(photo, "likeCount", -1)
        item["userSex"] = self.safe_extract(photo, "userSex")
        item["photoType"] = self.PHOTO_TYPE.get(
            self.safe_extract(photo, "photoType"), _("未知")
        )
        print(f"[DEBUG] __extract_photo: photoType 原始值: {self.safe_extract(photo, 'photoType')}")
        print(f"[DEBUG] __extract_photo: photoType 映射后: {item['photoType']}")
        print(f"[DEBUG] __extract_photo: PHOTO_TYPE 映射表: {self.PHOTO_TYPE}")
        item["caption"] = self.safe_extract(photo, "caption")
        item["userEid"] = self.safe_extract(photo, "userEid")
        item["detailID"] = self.__extract_id(self.safe_extract(photo, "share_info"))

    def __extract_music(
        self,
        item: dict,
        data: SimpleNamespace,
        video=True,
    ) -> None:
        if video:
            music = self.safe_extract(data, "photo.soundTrack")
        else:
            music = self.safe_extract(data, "photo.music")
        item["music_name"] = self.safe_extract(music, "name")
        item["audioUrls"] = []
        for i in self.safe_extract(music, "audioUrls", []):
            item["audioUrls"].append(i.url)
        item["audioUrls"] = " ".join(i for i in item["audioUrls"] if i)

    @staticmethod
    def __extract_id(share: str):
        parsed = parse_qs(share)
        return parsed.get("photoId", ["Unknown"])[0]

    def __extract_cover(self, item: dict, photo: SimpleNamespace, index=0) -> None:
        cover_urls = self.safe_extract(
            photo,
            "coverUrls",
        )
        item["coverUrls"] = cover_urls[index].url if cover_urls else ""
        webp_cover_urls = self.safe_extract(
            photo,
            "webpCoverUrls",
        )
        item["webpCoverUrls"] = webp_cover_urls[index].url if webp_cover_urls else ""
        head_urls = self.safe_extract(
            photo,
            "headUrls",
        )
        item["headUrls"] = head_urls[index].url if head_urls else ""

    def __extract_mp4(self, item: dict, data: SimpleNamespace) -> None:
        print(f"[DEBUG] __extract_mp4 被调用")
        print(f"[DEBUG] photoType: {item.get('photoType', 'NO_TYPE')}")
        mp4_url = self.safe_extract(data, "mp4Url")
        print(f"[DEBUG] safe_extract(data, 'mp4Url') 返回: {mp4_url} (类型: {type(mp4_url)})")
        item["download"] = mp4_url

    def __extract_atlas(self, item: dict, data: SimpleNamespace, index=0) -> None:
        print(f"[DEBUG] __extract_atlas 被调用")
        try:
            cdn = self.safe_extract(data, "atlas.cdn")
            print(f"[DEBUG] atlas.cdn 提取结果: {cdn} (类型: {type(cdn)})")
            cdn = cdn[index]
            print(f"[DEBUG] cdn[{index}]: {cdn}")
        except IndexError:
            print(f"[DEBUG] IndexError - cdn 索引 {index} 超出范围")
            cdn = None
        if not cdn:
            print(f"[DEBUG] cdn 为空，设置 download 为空字符串")
            item["download"] = ""
            return
        atlas = self.safe_extract(data, "atlas.list")
        print(f"[DEBUG] atlas.list 提取结果: {atlas} (类型: {type(atlas)})")
        urls = [f"https://{cdn}{i}" for i in atlas]
        print(f"[DEBUG] 生成的 URLs: {urls}")
        item["download"] = " ".join(urls)

    @staticmethod
    def format_date(
        timestamp: int,
        format_: str,
    ) -> str:
        if timestamp > 0:
            return strftime(format_, localtime(timestamp / 1000))
        return "unknown"

    @staticmethod
    def time_conversion(time_ms: int) -> str:
        seconds = time_ms // 1000
        hours, remainder = divmod(seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
