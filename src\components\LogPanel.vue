<template>
  <div class="log-panel-container" :style="{ height: panelHeight }">
    <!-- 日志面板头部 -->
    <div class="log-header" @click="toggleLogPanel">
      <div class="log-header-left">
        <el-icon class="log-icon">
          <Monitor />
        </el-icon>
        <span class="log-title">系统日志</span>
        <el-badge 
          v-if="unreadLogCount > 0" 
          :value="unreadLogCount" 
          :max="99"
          class="log-badge"
        />
        <span class="log-status" :class="logStatus.type">
          {{ logStatus.text }}
        </span>
      </div>
      <div class="log-header-right">
        <el-button-group size="small">
          <el-button 
            :icon="Refresh" 
            @click.stop="refreshLogs"
            :loading="isRefreshing"
            title="刷新日志"
          />
          <el-button 
            :icon="Delete" 
            @click.stop="clearLogs"
            title="清空日志"
          />
          <el-button 
            :icon="Setting" 
            @click.stop="showLogSettings"
            title="日志设置"
          />
        </el-button-group>
        <el-icon class="toggle-icon" :class="{ 'rotated': isExpanded }">
          <ArrowUp />
        </el-icon>
      </div>
    </div>

    <!-- 日志面板内容 -->
    <div class="log-panel" v-show="isExpanded">
      <div class="log-controls">
        <el-select 
          v-model="logFilter.level" 
          placeholder="日志级别" 
          size="small"
          style="width: 100px"
          @change="filterLogs"
        >
          <el-option label="全部" value="" />
          <el-option label="DEBUG" value="DEBUG" />
          <el-option label="INFO" value="INFO" />
          <el-option label="WARNING" value="WARNING" />
          <el-option label="ERROR" value="ERROR" />
        </el-select>
        
        <el-select 
          v-model="logFilter.limit" 
          placeholder="显示条数" 
          size="small"
          style="width: 80px"
          @change="refreshLogs"
        >
          <el-option label="20" :value="20" />
          <el-option label="50" :value="50" />
          <el-option label="100" :value="100" />
          <el-option label="200" :value="200" />
        </el-select>
        
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
          size="small"
          @change="toggleAutoRefresh"
        />
        
        <el-switch
          v-model="autoScroll"
          active-text="自动滚动"
          inactive-text="固定位置"
          size="small"
        />
      </div>

      <div class="log-container" ref="logContainer">
        <div 
          v-for="(log, index) in filteredLogs" 
          :key="index"
          :class="['log-entry', log.level.toLowerCase()]"
        >
          <span class="log-time">{{ formatTime(log.time) }}</span>
          <span class="log-level" :class="log.level.toLowerCase()">
            [{{ log.level }}]
          </span>
          <span class="log-message">{{ log.message }}</span>
        </div>
        
        <div v-if="filteredLogs.length === 0" class="log-empty">
          <el-empty description="暂无日志" :image-size="60" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from "vue";
import {
  Monitor,
  ArrowUp,
  Refresh,
  Delete,
  Setting,
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";

// API base URL
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || "http://localhost:5409";

// 默认设置
const DEFAULT_SETTINGS = {
  isExpanded: false,
  autoRefresh: true,
  autoScroll: true,
  logFilter: {
    level: '',
    limit: 50
  }
};

// 从localStorage加载设置
const loadSettings = () => {
  try {
    const saved = localStorage.getItem('logPanelSettings');
    return saved ? { ...DEFAULT_SETTINGS, ...JSON.parse(saved) } : DEFAULT_SETTINGS;
  } catch (error) {
    console.warn('加载日志面板设置失败:', error);
    return DEFAULT_SETTINGS;
  }
};

// 保存设置到localStorage
const saveSettings = () => {
  try {
    const settings = {
      isExpanded: isExpanded.value,
      autoRefresh: autoRefresh.value,
      autoScroll: autoScroll.value,
      logFilter: logFilter.value
    };
    localStorage.setItem('logPanelSettings', JSON.stringify(settings));
  } catch (error) {
    console.warn('保存日志面板设置失败:', error);
  }
};

// 初始化设置
const initialSettings = loadSettings();

// 响应式数据
const logs = ref([]);
const filteredLogs = ref([]);
const isExpanded = ref(initialSettings.isExpanded);
const isRefreshing = ref(false);
const autoRefresh = ref(initialSettings.autoRefresh);
const autoScroll = ref(initialSettings.autoScroll);
const autoRefreshTimer = ref(null);
const logContainer = ref(null);
const unreadLogCount = ref(0);

// 日志过滤器
const logFilter = ref(initialSettings.logFilter);

// 日志状态
const logStatus = ref({
  type: 'info',
  text: '就绪'
});

// 计算属性
const panelHeight = computed(() => {
  return isExpanded.value ? '300px' : '40px';
});

// 生命周期
onMounted(async () => {
  await initLogs();
  // 如果设置了自动刷新，则启动
  if (autoRefresh.value) {
    toggleAutoRefresh(true);
  }
});

onUnmounted(() => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value);
  }
  // 组件卸载时保存设置
  saveSettings();
});

// 监听设置变化并自动保存
watch([isExpanded, autoRefresh, autoScroll, logFilter], () => {
  saveSettings();
}, { deep: true });

// 方法
const initLogs = async () => {
  await fetchLogs();
  updateLogStatus('success', '已连接');
};

const fetchLogs = async () => {
  try {
    const params = new URLSearchParams({
      limit: logFilter.value.limit.toString()
    });
    
    if (logFilter.value.level) {
      params.append('level', logFilter.value.level);
    }

    const response = await fetch(`${apiBaseUrl}/api/logs?${params}`);
    const result = await response.json();

    if (result.code === 200) {
      const newLogs = result.data.logs;
      const oldCount = logs.value.length;
      logs.value = newLogs;
      
      // 计算未读日志数量
      if (oldCount > 0 && newLogs.length > oldCount) {
        unreadLogCount.value += newLogs.length - oldCount;
      }
      
      filterLogs();
      updateLogStatus('success', `已加载 ${newLogs.length} 条日志`);
      
      // 自动滚动到底部
      if (autoScroll.value) {
        await nextTick();
        scrollToBottom();
      }
    } else {
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('获取日志失败:', error);
    updateLogStatus('error', '连接失败');
    ElMessage.error(`获取日志失败: ${error.message}`);
  }
};

const filterLogs = () => {
  if (!logFilter.value.level) {
    filteredLogs.value = logs.value;
  } else {
    filteredLogs.value = logs.value.filter(log => log.level === logFilter.value.level);
  }
};

const refreshLogs = async () => {
  isRefreshing.value = true;
  try {
    await fetchLogs();
  } finally {
    isRefreshing.value = false;
  }
};

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空服务器日志缓存吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const response = await fetch(`${apiBaseUrl}/api/logs/clear`, {
      method: 'POST'
    });
    const result = await response.json();

    if (result.code === 200) {
      logs.value = [];
      filteredLogs.value = [];
      unreadLogCount.value = 0;
      updateLogStatus('success', '日志已清空');
      ElMessage.success('日志缓存已清空');
    } else {
      throw new Error(result.msg);
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空日志失败:', error);
      ElMessage.error(`清空日志失败: ${error.message}`);
    }
  }
};

const toggleLogPanel = () => {
  isExpanded.value = !isExpanded.value;
  if (isExpanded.value) {
    unreadLogCount.value = 0; // 清除未读标记
    nextTick(() => {
      if (autoScroll.value) {
        scrollToBottom();
      }
    });
  }
};

const toggleAutoRefresh = (enabled) => {
  if (enabled) {
    autoRefreshTimer.value = setInterval(fetchLogs, 6000); // 每3秒刷新
    updateLogStatus('info', '自动刷新中');
  } else {
    if (autoRefreshTimer.value) {
      clearInterval(autoRefreshTimer.value);
      autoRefreshTimer.value = null;
    }
    updateLogStatus('success', '手动模式');
  }
};

const showLogSettings = () => {
  ElMessageBox({
    title: '日志设置',
    message: `
      <div style="text-align: left;">
        <h4>当前设置:</h4>
        <p><strong>日志级别:</strong> ${logFilter.value.level || '全部'}</p>
        <p><strong>显示条数:</strong> ${logFilter.value.limit}</p>
        <p><strong>自动刷新:</strong> ${autoRefresh.value ? '开启' : '关闭'}</p>
        <p><strong>自动滚动:</strong> ${autoScroll.value ? '开启' : '关闭'}</p>
        <p><strong>面板展开:</strong> ${isExpanded.value ? '是' : '否'}</p>
        <br>
        <p><em>设置会自动保存到浏览器本地存储</em></p>
      </div>
    `,
    dangerouslyUseHTMLString: true,
    showCancelButton: true,
    confirmButtonText: '重置设置',
    cancelButtonText: '关闭',
    distinguishCancelAndClose: true,
    type: 'info'
  }).then(() => {
    // 重置设置
    resetSettings();
  }).catch(() => {
    // 用户取消，不做任何操作
  });
};

const resetSettings = () => {
  ElMessageBox.confirm(
    '确定要重置所有日志设置为默认值吗？',
    '确认重置',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 停止自动刷新
    if (autoRefreshTimer.value) {
      clearInterval(autoRefreshTimer.value);
      autoRefreshTimer.value = null;
    }
    
    // 重置所有设置
    isExpanded.value = DEFAULT_SETTINGS.isExpanded;
    autoRefresh.value = DEFAULT_SETTINGS.autoRefresh;
    autoScroll.value = DEFAULT_SETTINGS.autoScroll;
    logFilter.value = { ...DEFAULT_SETTINGS.logFilter };
    
    // 清除localStorage
    localStorage.removeItem('logPanelSettings');
    
    // 重新过滤日志
    filterLogs();
    
    updateLogStatus('success', '设置已重置');
    ElMessage.success('日志设置已重置为默认值');
  }).catch(() => {
    // 用户取消重置
  });
};

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight;
  }
};

const updateLogStatus = (type, text) => {
  logStatus.value = { type, text };
};

const formatTime = (timestamp) => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};
</script>

<style scoped>
.log-panel-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #1f1c1c;
  color: #fff;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  transition: height 0.3s ease;
  overflow: hidden;
  border-radius: 5px 5px 0 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  cursor: pointer;
  user-select: none;
  min-height: 40px;
}

.log-header:hover {
  background-color: #37373d;
}

.log-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.log-icon {
  color: #58a6ff;
}

.log-title {
  font-weight: 500;
  color: #f0f6fc;
}

.log-badge {
  margin-left: 4px;
}

.log-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
}

.log-status.success {
  background-color: #238636;
  color: #fff;
}

.log-status.error {
  background-color: #da3633;
  color: #fff;
}

.log-status.info {
  background-color: #1f6feb;
  color: #fff;
}

.log-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-icon {
  transition: transform 0.3s ease;
  color: #8b949e;
}

.toggle-icon.rotated {
  transform: rotate(180deg);
}

.log-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background-color: #21262d;
  border-bottom: 1px solid #30363d;
  font-size: 11px;
}

.log-container {
  flex: 1;
  overflow-y: auto;
  padding: 4px;
  background-color: #0d1117;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.log-entry {
  display: flex;
  align-items: flex-start;
  padding: 2px 8px;
  margin-bottom: 1px;
  border-radius: 2px;
  font-size: 11px;
  line-height: 1.4;
}

.log-entry:hover {
  background-color: #161b22;
}

.log-time {
  color: #7d8590;
  margin-right: 8px;
  min-width: 60px;
  font-size: 10px;
}

.log-level {
  margin-right: 8px;
  min-width: 50px;
  font-weight: bold;
  font-size: 10px;
}

.log-level.debug { color: #8b949e; }
.log-level.info { color: #58a6ff; }
.log-level.warning { color: #d29922; }
.log-level.error { color: #f85149; }
.log-level.critical { 
  color: #fff; 
  background-color: #da3633;
  padding: 1px 4px;
  border-radius: 2px;
}

.log-message {
  flex: 1;
  word-wrap: break-word;
  color: #e6edf3;
}

.log-entry.debug { border-left: 2px solid #8b949e; }
.log-entry.info { border-left: 2px solid #58a6ff; }
.log-entry.warning { border-left: 2px solid #d29922; }
.log-entry.error { border-left: 2px solid #f85149; }
.log-entry.critical { border-left: 2px solid #da3633; }

.log-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #7d8590;
}

.log-container::-webkit-scrollbar {
  width: 6px;
}

.log-container::-webkit-scrollbar-track {
  background: #21262d;
}

.log-container::-webkit-scrollbar-thumb {
  background: #30363d;
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: #484f58;
}
</style>
