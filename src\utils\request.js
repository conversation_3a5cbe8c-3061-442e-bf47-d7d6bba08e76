import { fetch } from '@tauri-apps/plugin-http'
import { ElMessage } from 'element-plus'
import { MAIN_API_CONFIG, AUTH_API_CONFIG, API_ENDPOINTS } from '@/config/api'

// 基础URL - 主应用服务器
const BASE_URL = MAIN_API_CONFIG.baseURL
// 认证服务器URL
const AUTH_BASE_URL = AUTH_API_CONFIG.baseURL

// Token验证缓存
let tokenValidationCache = {
  token: null,
  isValid: false,
  lastCheckTime: 0,
  cacheTimeout: 5 * 60 * 1000 // 5分钟缓存
}

// 获取用户token的函数
const getUserToken = () => {
  try {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    return userInfo.access_token || null
  } catch {
    return null
  }
}

// 清除token验证缓存
const clearTokenCache = () => {
  console.log('[request] 清除token验证缓存')
  tokenValidationCache = {
    token: null,
    isValid: false,
    lastCheckTime: 0,
    cacheTimeout: 5 * 60 * 1000
  }
}

// 调试函数：查看缓存状态
const debugTokenCache = () => {
  const now = Date.now()
  const cacheAge = now - tokenValidationCache.lastCheckTime
  console.log('[DEBUG] Token缓存状态:')
  console.log('  - Token:', tokenValidationCache.token ? tokenValidationCache.token.substring(0, 20) + '...' : 'null')
  console.log('  - 有效:', tokenValidationCache.isValid)
  console.log('  - 最后检查时间:', new Date(tokenValidationCache.lastCheckTime).toLocaleString())
  console.log('  - 缓存年龄:', Math.round(cacheAge / 1000) + '秒')
  console.log('  - 缓存超时:', Math.round(tokenValidationCache.cacheTimeout / 1000) + '秒')
  console.log('  - 是否过期:', cacheAge >= tokenValidationCache.cacheTimeout)
  return tokenValidationCache
}

// 暴露调试函数到全局（仅开发环境）
if (import.meta.env.DEV) {
  window.clearTokenCache = clearTokenCache
  window.debugTokenCache = debugTokenCache
}

// 退出登录函数
const performLogout = () => {
  console.log('[request] Token验证失败，执行登出操作')
  clearTokenCache()
  localStorage.removeItem('userInfo')
  if (window.location.hash !== '#/login') {
    window.location.hash = '#/login'
    window.location.reload()
  }
}

// 验证token的函数
const checkToken = async (token) => {
  try {
    // 检查缓存
    const now = Date.now()
    const cacheAge = now - tokenValidationCache.lastCheckTime
    const isTokenSame = tokenValidationCache.token === token
    const isCacheValid = tokenValidationCache.isValid
    const isCacheNotExpired = cacheAge < tokenValidationCache.cacheTimeout

    console.log('[request] 缓存检查详情:')
    console.log(`  - Token相同: ${isTokenSame}`)
    console.log(`  - 缓存有效: ${isCacheValid}`)
    console.log(`  - 缓存年龄: ${Math.round(cacheAge / 1000)}秒`)
    console.log(`  - 缓存超时: ${Math.round(tokenValidationCache.cacheTimeout / 1000)}秒`)
    console.log(`  - 未过期: ${isCacheNotExpired}`)

    if (isTokenSame && isCacheValid && isCacheNotExpired) {
      console.log('[request] 使用缓存的token验证结果')
      return true
    }

    console.log('[request] 缓存无效，需要重新验证')
    if (!isTokenSame) console.log('[request] 原因: Token已变更')
    if (!isCacheValid) console.log('[request] 原因: 缓存标记为无效')
    if (!isCacheNotExpired) console.log('[request] 原因: 缓存已过期')

    console.log('[request] 开始验证token...')
    const response = await fetch(`${AUTH_BASE_URL}${API_ENDPOINTS.AUTH.TOKEN_CHECK}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        access_token: token
      })
    })

    console.log('[request] Token验证响应状态:', response.status)

    if (!response.ok) {
      console.log('[request] Token验证HTTP错误:', response.status)
      return false
    }

    const result = await response.text()
    let resData = {}
    try {
      resData = result ? JSON.parse(result) : {}
    } catch (parseError) {
      console.error('[request] Token验证响应解析失败:', parseError)
      return false
    }

    console.log('[request] Token验证响应数据:', resData)

    // 检查业务状态码
    if (resData.status === 1) {
      console.log('[request] Token验证成功')
      // 更新缓存 - 使用当前时间
      const currentTime = Date.now()
      tokenValidationCache = {
        token: token,
        isValid: true,
        lastCheckTime: currentTime,
        cacheTimeout: 5 * 60 * 1000
      }
      console.log(`[request] 缓存已更新，下次验证时间: ${new Date(currentTime + 5 * 60 * 1000).toLocaleTimeString()}`)
      return true
    } else {
      console.log('[request] Token验证失败:', resData.message || resData.msg)
      // 清除缓存
      clearTokenCache()
      return false
    }
  } catch (error) {
    console.error('[request] Token验证异常:', error)
    // 清除缓存
    clearTokenCache()
    return false
  }
}

// 通用请求函数 - 仅用于主应用业务API
async function request({ url, method = 'GET', data = null, params = null, headers = {} }) {
  try {
    const token = getUserToken()
    headers['Content-Type'] = headers['Content-Type'] || 'application/json'

    // 拼接参数到URL
    let fullUrl = BASE_URL + url
    if (params && Object.keys(params).length > 0) {
      const query = new URLSearchParams(params).toString()
      fullUrl += (fullUrl.includes('?') ? '&' : '?') + query
    }
    console.log('开始验证 checkToken...')
    const isTokenValid = await checkToken(token)

    if (!isTokenValid) {
      console.log('[request] Token验证失败，执行登出')
      performLogout()
      return // 直接返回，不继续执行请求
    }

    console.log('[request] Token验证通过，继续执行请求')
    headers['Accept'] = 'application/json'
    headers['Authorization'] = `Bearer ${token}`


    const response = await fetch(fullUrl, {
      method,
      headers,
      ...(data ? { body: headers['Content-Type'] === 'application/json' ? JSON.stringify(data) : data } : {})
    })

    console.log('[request] url:', fullUrl)
    console.log('[request] method:', method)
    console.log('[request] headers:', headers)
    console.log('[request] data:', data)
    console.log('[request] response:', response)

    if (!response.ok) {
      let text = "";
      try {
        text = await response.text()
        console.log('[request] response text:', text)
      } catch (textError) {
        console.error('[request] 读取错误响应文本失败:', textError)
        text = `{"error": "无法读取响应内容", "status": ${response.status}}`
      }
      const resData = text ? JSON.parse(text) : {}
      console.log('[request] resData:', resData)

      // 处理401未授权错误
      if (response.status === 401) {
        ElMessage.error('登录已过期，请重新登录')
        // 清除本地存储的用户信息
        localStorage.removeItem('userInfo')
        // 跳转到登录页
        if (window.location.hash !== '#/login') {
          window.location.hash = '#/login'
          // 刷新页面以重置应用状态
          window.location.reload()
        }
        return
      }

      ElMessage.warning(resData?.msg || '请求失败');
      return
      // throw new Error(resData?.msg || '请求失败');
    }

    // 读取并解析响应体
    let text = "";
    try {
      text = await response.text()
      console.log('[request] response text:', text)
    } catch (textError) {
      console.error('[request] 读取成功响应文本失败:', textError)
      text = `{"error": "无法读取响应内容", "code": 500}`
    }
    const resData = text ? JSON.parse(text) : {}
    console.log('[request] resData:', resData)

    if (resData.code === 200 || resData.success) {
      return resData
    } else {
      ElMessage.error(resData?.data?.msg || '请求失败')
      // throw new Error(resData?.data?.msg || '请求失败')
      return resData
    }
  } catch (error) {
    // console.error('Request Error:', error)
    ElMessage.error(error || '网络连接失败')
    console.log('[request] checkout，')
    performLogout()
    throw error
  } finally {
    // 可以在这里添加一些全局的请求结束处理逻辑
    // console.log('Request completed:', url, method)
  }
}

// 封装常用的请求方法
export const http = {
  get(url, params) {
    return request({ url, method: 'GET', params })
  },

  post(url, data, config = {}) {
    return request({ url, method: 'POST', data, headers: config.headers || {} })
  },

  put(url, data, config = {}) {
    return request({ url, method: 'PUT', data, headers: config.headers || {} })
  },

  delete(url, params) {
    return request({ url, method: 'DELETE', params })
  },

  upload(url, formData) {
    return request({
      url,
      method: 'POST',
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
}

export default request