{"rustc": 16591470773350601817, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 15657897354478470176, "path": 12494107851484072431, "deps": [[784494742817713399, "tower_service", false, 1174622840264666335], [1655997011631319712, "webpki_roots", false, 14249991956284279895], [4920660634395069245, "hyper_util", false, 12904087735913788721], [5501164552881223878, "pki_types", false, 14327559253002116693], [9027827595814673957, "rustls", false, 4727676188276325104], [9538054652646069845, "tokio", false, 2051528350863639385], [10629569228670356391, "futures_util", false, 5529326595165766766], [17301905655554462353, "tokio_rustls", false, 18352157032600750705], [17860019243264344128, "http", false, 9568133891094124003], [17936921378057108349, "hyper", false, 10926049433159936393]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-rustls-4c1db15656db0016\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}