import { http } from '@/utils/request'

// 账号管理相关API
export const accountApi = {
  // 获取有效账号列表
  getValidAccounts(check_cookie='0') {
    return http.get(`/getValidAccounts?check_cookie=${check_cookie}`)
  },
  
  // 添加账号
  addAccount(data) {
    return http.post('/account', data)
  },
  
  // 更新账号
  updateAccount(data) {
    return http.post('/updateUserinfo', data)
  },
  
  // 删除账号
  deleteAccount(id) {
    return http.get(`/deleteAccount?id=${id}`)
  },
  
  // 获取所有账号
  getAllAccounts() {
    return http.get(`/getAllAccounts`)
  },

  // 获取账号信息和Cookie内容
  getAccountsWithCookie() {
    return http.get(`/getAccountsWithCookie`)
  },

  // 更新账号一键发布设置
  updateAccountAutoPublish(data) {
    return http.post('/updateAccountAutoPublish', data)
  }
}