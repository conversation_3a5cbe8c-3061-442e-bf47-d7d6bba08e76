<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <img src="../assets/app-icon.png" alt="Logo" class="logo-img" />
          <h1 class="app-title">小超媒体管理系统</h1>
        </div>
        <p class="login-subtitle">请输入卡密进行登录</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="cardKey">
          <el-input
            v-model="loginForm.cardKey"
            type="password"
            placeholder="请输入卡密"
            size="large"
            show-password
            :prefix-icon="Key"
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <div class="remember-options">
            <el-checkbox v-model="rememberCardKey" size="large">
              记住卡密（30天）
            </el-checkbox>
            <el-button
              v-if="hasRememberedCard"
              type="text"
              size="small"
              @click="handleClearSavedCardKey"
              class="clear-btn"
            >
              清除保存的卡密
            </el-button>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-btn"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-footer">
        <p class="copyright">© 2025 视频发布管理系统. All rights reserved.</p>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="bg-circle bg-circle-1"></div>
      <div class="bg-circle bg-circle-2"></div>
      <div class="bg-circle bg-circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Key } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { loginApi } from '@/api/auth'
import { saveCardKey, loadCardKey, clearCardKey, hasRememberedCardKey, CARD_KEY_CONFIG } from '@/utils/cardKeyStorage'

const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref()
const loading = ref(false)
const rememberCardKey = ref(false)
const hasRememberedCard = ref(false)

const loginForm = reactive({
  cardKey: ''
})

const loginRules = {
  cardKey: [
    { required: true, message: '请输入卡密', trigger: 'blur' },
    { min: 6, message: '卡密长度不能少于6位', trigger: 'blur' }
  ]
}

// 加载保存的卡密
const loadSavedCardKey = () => {
  const { cardKey, remember } = loadCardKey()
  hasRememberedCard.value = hasRememberedCardKey()

  if (cardKey) {
    loginForm.cardKey = cardKey
    rememberCardKey.value = remember
    console.log(`已加载保存的卡密，有效期${CARD_KEY_CONFIG.EXPIRE_DAYS}天`)
  }
}

// 清除保存的卡密
const handleClearSavedCardKey = () => {
  const success = clearCardKey()
  if (success) {
    hasRememberedCard.value = false
    rememberCardKey.value = false
    loginForm.cardKey = ''
    ElMessage.success('已清除保存的卡密')
  } else {
    ElMessage.error('清除卡密失败')
  }
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
    console.log('登录响应:', loginFormRef.value);

  try {
    await loginFormRef.value.validate()
    loading.value = true

    const response = await loginApi(loginForm.cardKey)
    console.log('登录响应:', response);
    if (response.status === 1) {
      // 保存卡密（如果用户选择记住）
      const saveSuccess = saveCardKey(loginForm.cardKey, rememberCardKey.value)
      if (saveSuccess && rememberCardKey.value) {
        console.log(`卡密已保存，有效期${CARD_KEY_CONFIG.EXPIRE_DAYS}天`)
      }

      // 保存用户信息和token
      userStore.setUserInfo({
        access_token: response.data.access_token,
        refresh_token: response.data.refresh_token,
        access_token_expires_at: response.data.access_token_expires_at,
        user_info: response.data.user_info || {}
      })

      ElMessage.success('登录成功')

      // 跳转到首页
      router.push('/')
    } else {
      // 登录失败，如果是卡密错误，可以选择清除保存的卡密
      console.log('登录失败:', response.message || '登录失败')

      // 如果是认证失败且用户之前保存了卡密，提示可能需要更新
      if (response.message && (response.message.includes('卡密') || response.message.includes('认证'))) {
        console.log('卡密验证失败，如果卡密已更改，请取消"记住卡密"选项')
      }
    }
  } catch (error) {
    console.error('登录错误:', error)
    // 只有在网络错误等特殊情况下才显示错误消息
    // authRequest已经处理了大部分错误情况
    if (error.name === 'TypeError' || error.message.includes('fetch')) {
      ElMessage.error('网络连接失败，请检查网络连接')
    }
    // 其他错误已经在authRequest中处理，不需要重复显示
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载保存的卡密
onMounted(() => {
  loadSavedCardKey()
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-card {
  width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.logo-img {
  width: 64px;
  height: 64px;
  margin-bottom: 12px;
}

.app-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.login-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 20px;
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
}

.login-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.copyright {
  color: #95a5a6;
  font-size: 12px;
  margin: 0;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.bg-circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.bg-circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 记住卡密选项样式 */
.remember-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.el-checkbox {
  color: #666;
  font-size: 14px;
}

.el-checkbox.is-checked .el-checkbox__label {
  color: #409eff;
}

.clear-btn {
  color: #f56c6c;
  font-size: 12px;
  padding: 0;
  margin-left: 10px;
}

.clear-btn:hover {
  color: #f78989;
}

/* 登录表单项间距调整 */
.login-form .el-form-item {
  margin-bottom: 20px;
}

.login-form .el-form-item:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    width: 90%;
    padding: 30px 20px;
  }

  .app-title {
    font-size: 20px;
  }
}
</style>
