{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 9651074268412295581], [2069998946850810971, "build_script_build", false, 6829209256351337886], [3834743577069889284, "build_script_build", false, 9296574138109942914], [13890802266741835355, "build_script_build", false, 14739595289361344898], [15441187897486245138, "build_script_build", false, 4415317989292133976], [2253952315205409758, "build_script_build", false, 2484992371458884143]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-b43370e59608d281\\output", "paths": ["src/test/ks_sig3.jar", "tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}